
// YAML 格式的 CC Effect
// 此部分为声明流程控制清单
CCEffect %{
  # techniques 是一个数组
  techniques:
  # passes 是 techniques 数组的第0项
  # 同时 passes 也是一个数组，存放渲染管道描述的数组集合
  - passes:
  	# passes 数组的第0项，完整的渲染流水线
    # vert 属性是指定顶点 Shader 片段的名字，如：这里的顶点 Shader 片段的名字为 vs
    # 根据文档介绍还可以这样子指定片段的入口函数 vs:vert ，那么就代替main函数，vert才是入口函数
    - vert: vs
      # frag 属性是指定片元 Shader 片段的名字，如：这里的片元 Shader 片段的名字为 fs
      # 根据文档介绍还可以这样子指定片段的入口函数 fs:frag ，那么就代替main函数，frag才是入口函数
      frag: fs
      # 混合模式开启
      blendState:
        targets:
        - blend: true
          blendSrc: one
          blendDst: one_minus_src_alpha
          blendSrcAlpha: one
          blendDstAlpha: one_minus_src_alpha
      rasterizerState:
        cullMode: none
      # properties 列出可以在 Inspector 编辑器编辑的属性    
      properties:
        round: { value: 0.1, editor: { tooltip: '圆角半径（百分比）' } }
        feather: { value: 0.01, editor: { tooltip: '边缘虚化（百分比）' } }
        hightlightColor: { value: [1,1,1,1], editor: { type: color} }
        textureSize: { value: [100 ,100]}
        radius: { value: 1.0}
}%

// 顶点 Shader 片段
CCProgram vs %{
  // 定义 float 类型的精度为高精度
  precision highp float;
  // CC 所有内置的 Shader 变量都必须要要通过 #include 引入该变量的头文件
  // 所有头文件都在 chunks 目录下
  // Mac: /Applications/CocosCreator.app/Contents/Resources/engine/cocos2d/renderer/build/chunks
  // 也可以通过 相对项目assets的相对路径 或者 绝对路径 引用头文件资源
  #include <cc-global>

  // 顶点Shader 从渲染管道里面获取哪些数据
  // in 用在函数的参数中，表示这个参数是输入的，在函数中改变这个值，并不会影响对调用的函数产生副作用。（相当于C语言的传值），这个是函数参数默认的修饰符

  // 顶点坐标
  // a_position 是笛卡尔坐标右手系，也是OpenGL的坐标系，原点在左下角，X轴正方向往右，Y轴正方向往上，Z轴正方向往外
  in vec3 a_position;
  // 输入的纹理坐标
  // a_uv0 是标准屏幕坐标系，即原点在左上角，X轴正方向往右，Y轴正方向往下
  in vec2 a_uv0;
  // 顶点颜色，实际为对应节点的颜色
  in vec4 a_color;

  // 输出的纹理坐标
  // 在片元 Shader 片段中可以接收到这个参数名的值
  out vec2 v_uv0;
  // 顶点 Shader 片段最后会输出的颜色值
  // 在片元 Shader 片段中可以接收到这个参数名的值
  out vec4 v_color;

  void main () {
    gl_Position = cc_matViewProj * vec4(a_position, 1);
    v_uv0 = a_uv0;
    v_color = a_color;
  }
}%

// 片元着色器片段
CCProgram fs %{
  precision highp float;
  #include <texture>

  // 接收来自上方顶点 Shader 片段的输出参数 v_uv0
  // 顶点的坐标
  in vec2 v_uv0;
  // 接收来自上方顶点 Shader 片段的输出参数 v_color
  #define PI 3.14159265359
  // 顶点的颜色
  in vec4 v_color;

  // uniform ：一致变量。在着色器执行期间一致变量的值是不变的
  // 与const常量不同的是，这个值在编译时期是未知的是由着色器外部初始化的
  // 一致变量在顶点着色器和片段着色器之间是共享的。它也只能在全局范围进行声明。
  uniform sampler2D texture;

  uniform Properties {
    float round;
    float feather;
  };

  uniform MyUniform {
    vec4 hightlightColor;
    vec2 textureSize;
    float radius;
  };

  vec4 getColorFromTexture(sampler2D texture,vec2 uv){
      vec4 color=vec4(1,1,1,1);
      CCTexture(texture,uv,color);
      return color;
  }

  void main () {
    vec4 color = v_color;
    // 纹理颜色 和 节点颜色进行混合得出最终颜色
    color *= texture(texture, v_uv0);

    if (color.a == 0.0) discard;
    float alpha = 1.0;
    // 圆角处理
    if(round > 0.0){
      vec2 vertex;
      if (v_uv0.x <= round) {
        if (v_uv0.y <= round) {
          vertex = vec2(round, round); // 左上角
        } else if (v_uv0.y >= 1.0 - round) {
          vertex = vec2(round, (1.0 - round)); // 左下角
        } else {
          vertex = vec2(round, v_uv0.y); // 左中
        }
      } else if (v_uv0.x >= 1.0 - round) {
        if (v_uv0.y <= round){
          vertex = vec2(1.0 - round, round); // 右上角
        } else if (v_uv0.y >= 1.0 - round) {
          vertex = vec2(1.0 - round, (1.0 - round)); // 右下角
        } else {
          vertex = vec2(1.0 - round, v_uv0.y); // 右中
        }
      } else if (v_uv0.y <= round) {
        vertex = vec2(v_uv0.x, round); // 上中
      } else if (v_uv0.y >= 1.0 - round) {
        vertex = vec2(v_uv0.x, (1.0 - round)); // 下中
      } else {
        vertex = v_uv0; // 中间
      }
      float dis = distance(v_uv0, vertex);
      alpha = smoothstep(round, round - feather, dis);
    } 
    
    // color.rgb *= color.a;
    color *= alpha;

    gl_FragColor = color;

    // vec4 o = vec4(1, 1, 1, 1);

    // float unitWidth=1.0/textureSize.x;  //单个像素的宽占uv.x的百分比
    // float unitHeight=1.0/textureSize.y; //单个像素的高占uv.y的百分比
    // float width= radius*unitWidth;   //radius为边缘高光的半径
    // float height= radius*unitHeight;

    // float angle=0.0;  //角度
    // float maxAlpha=0.0; //透明度，经过下面的循环后，如果透明度为0则说明该像素周围没有颜色，即它不是图像的边缘；反之则是图像的边缘
    // for(int i=0;i<16;i++){
    //     angle+=1.0/float(16)*2.0*PI;  //角度每个循环增加一次，循环结束时角度为2PI，整好一个圆的角度，圆的精度由SAMPLE决定，SAMPLE越高圆越精细，但运算量也会增加
    //     vec2 testPoint_uv=vec2(width*cos(angle),height*sin(angle));//该点的圆上该角度的相对UV坐标
    //     testPoint_uv=clamp(v_uv0+testPoint_uv,vec2(0,0),vec2(1,1));//加上该点的UV坐标变为绝对UV坐标
    //     float tempAlpha=getColorFromTexture(texture,testPoint_uv).a;//用绝对UV坐标从texture读取颜色，看它的透明度
    //     maxAlpha=max(maxAlpha,tempAlpha);//把透明度结果保存起来
    // }

    // vec4 finalColor=mix(vec4(0.0),hightlightColor,maxAlpha);//根据检测后的透明度决定该点最终的颜色
    // o=getColorFromTexture(texture,v_uv0);//读取该点本来的颜色
    // o*=v_color;//乘上顶点颜色，如果想要边缘高光也受node.color的影响，那么也要finalColor*=v_color;


    // gl_FragColor = mix(finalColor,color,o.a);//color;
  }
}%