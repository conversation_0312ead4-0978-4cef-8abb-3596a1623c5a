{"ver": "1.0.27", "uuid": "668343dd-a999-4d68-b78f-de4f7bc056a2", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec2 a_uv0;\nattribute vec4 a_color;\nvarying vec2 v_uv;\nvarying vec4 v_color;\nvoid main () {\n  gl_Position = cc_matViewProj * cc_matWorld * vec4(a_position, 1.0);\n  v_uv = a_uv0;\n  v_color = a_color;\n}", "frag": "\nprecision highp float;\nuniform sampler2D texture;\nuniform vec4 topColor;\nuniform vec4 bottomColor;\nvarying vec2 v_uv;\nvarying vec4 v_color;\nvoid main () {\n  vec4 texColor = texture2D(texture, v_uv);\n  vec4 gradient = mix(topColor, bottomColor, v_uv.y);\n  vec4 finalColor = texColor * gradient;\n  finalColor.a *= v_color.a;\n  gl_FragColor = finalColor;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec2 a_uv0;\nin vec4 a_color;\nout vec2 v_uv;\nout vec4 v_color;\nvoid main () {\n  gl_Position = cc_matViewProj * cc_matWorld * vec4(a_position, 1.0);\n  v_uv = a_uv0;\n  v_color = a_color;\n}", "frag": "\nprecision highp float;\nuniform sampler2D texture;\nuniform Properties {\n  vec4 topColor;\n  vec4 bottomColor;\n};\nin vec2 v_uv;\nin vec4 v_color;\nvoid main () {\n  vec4 texColor = texture(texture, v_uv);\n  vec4 gradient = mix(topColor, bottomColor, v_uv.y);\n  vec4 finalColor = texColor * gradient;\n  finalColor.a *= v_color.a;\n  gl_FragColor = finalColor;\n}"}}], "subMetas": {}}