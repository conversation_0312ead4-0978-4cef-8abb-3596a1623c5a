CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      depthStencilState:
        depthTest: false
        depthWrite: false
      properties:
        texture: { value: white }
        topColor: { value: [0.831, 0.992, 0.878, 1.0],, editor: color }         # 顶部 #FFFFFF
        
        bottomColor: { value: [0.902, 0.992, 1.0, 1.0], editor: color }  // 底部 #FFECDA
        
}%

CCProgram vs %{
  precision highp float;

  #include <cc-global>
  #include <cc-local>

  in vec3 a_position;
  in vec2 a_uv0;         // ✅ 正确的 UV 属性
  in vec4 a_color;

  out vec2 v_uv;
  out vec4 v_color;

  void main () {
    gl_Position = cc_matViewProj * cc_matWorld * vec4(a_position, 1.0);
    v_uv = a_uv0;       // ✅ 正确传 UV 坐标
    v_color = a_color;
  }
}%

CCProgram fs %{
  precision highp float;

  uniform sampler2D texture;

  uniform Properties {
    vec4 topColor;
    vec4 bottomColor;
  };

  in vec2 v_uv;
  in vec4 v_color;

  void main () {
    vec4 texColor = texture(texture, v_uv);
    vec4 gradient = mix(topColor, bottomColor, v_uv.y);  // 按 y 方向渐变
    vec4 finalColor = texColor * gradient;
    finalColor.a *= v_color.a;
    gl_FragColor = finalColor;
  }
}%