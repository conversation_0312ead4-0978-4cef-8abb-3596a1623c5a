export const language = {
//這部分是通用的
    kickout1: '您被請出房間',
    LeaveRoom: '房間已解散',
    InsufficientBalance: '餘額不足，去儲值',
    GameRouteNotFound: '遊戲路線異常',
    NetworkError: '網絡異常',
    RoomIsFull: '房間已滿',
    EnterRoomNumber: '輸入房間號',
    GetUserInfoFailed: '獲取用戶資訊失敗',
    RoomDoesNotExist: '房間不存在',
    FailedToDeductGoldCoins: '扣除金幣失敗',
    ExitApplication: '確定退出遊戲？',
    QuitTheGame: '退出後將無法返回遊戲。',

    NotEnoughPlayers: '玩家數量不足',
    TheGameIsFullOfPlayers: '玩家數量已滿',
    kickout2: '是否將 {0} 請出房間?',

    upSeat: '加入遊戲',
    downSeat: '退出遊戲',
    startGame: '開始',
    readyGame: '準備',
    cancelGame: '取消準備',
    cancel: '取消',
    confirm: '確定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音樂',
    sound: '音效',
    join:'進入', //加入
    create:'創建', //创建
    auto:'匹配',
    Room:'房間',
    room_number:'房號', //房间号
    copy:'複製', //复制
    game_amount:'遊戲費用', //游戏费用
    player_numbers:'玩家數量:', //玩家数量
    room_exist:'房間不存在',//房间不存在
    enter_room_number:'輸入房間號',//输入房间号
    free:'免費',
    players:'玩家', //玩家
    Player:'玩家',
    Tickets:'門票',
    Empty:'空位',

    nextlevel:'下一關', //下一關  
    relevel:'再玩一次', //再來一局 
   

   
};

const cocos = cc as any;
if (!cocos.Jou_i18n) cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_HK = language;