export const language = {
//这部分是通用的
    kickout1: '您被请出房间',
    LeaveRoom: '房间已解散',
    InsufficientBalance: '余额不足，去充值',
    GameRouteNotFound: '游戏线路异常',
    NetworkError: '网络异常',
    RoomIsFull: '房间已满',
    EnterRoomNumber: '输入房间号',
    GetUserInfoFailed: '获取用户信息失败',
    RoomDoesNotExist: '房间不存在',
    FailedToDeductGoldCoins: '扣除金币失败',
    ExitApplication: '确定退出游戏？',
    QuitTheGame: '退出后将无法返回游戏。',

    NotEnoughPlayers: '玩家数量不足',
    TheGameIsFullOfPlayers: '玩家数量已满',
    kickout2: '是否将 {0} 请出房间?',//踢出玩家文案

    upSeat:'加入游戏',
    downSeat:'退出游戏', 
    startGame:'开始', 
    readyGame:'准备', 
    cancelGame:'取消准备', 
    cancel:'取消', 
    confirm:'确定', 
    kickout3:'踢出', 
    back:'返回',//返回
    leave:'退出',
    music:'音乐',
    sound:'音效',
    join:'加入', //加入
    create:'创建', //创建
    auto:'匹配',
    Room:'房间',
    room_number:'房间号', //房间号
    copy:'复制', //复制
    game_amount:'游戏费用', //游戏费用
    player_numbers:'玩家数量:', //玩家数量
    room_exist:'房间不存在',//房间不存在
    enter_room_number:'输入房间号',//输入房间号
    free:'免费',
    players:'玩家', //玩家
    Player:'玩家',
    Tickets:'门票',
    Empty:'空位',

    nextlevel:'下一关',//下一关

    relevel:'再玩一次', //再来一局

};

const cocos = cc as any;
if (!cocos.Jou_i18n) cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_CN = language;