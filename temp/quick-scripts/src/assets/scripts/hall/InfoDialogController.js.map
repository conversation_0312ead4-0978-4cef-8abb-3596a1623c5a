{"version": 3, "sources": ["assets/scripts/hall/InfoDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAKtF,yCAAwC;AACxC,uCAAsC;AAEhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C,UAAU;AAEV;IAAkD,wCAAY;IAA9D;QAAA,qEAiWC;QA9VG,aAAO,GAAY,IAAI,CAAA;QAEvB,mBAAa,GAAY,IAAI,CAAA;QAE7B,gBAAU,GAAY,IAAI,CAAA;QAE1B,mBAAmB;QAEnB,gBAAU,GAAY,IAAI,CAAA;QAE1B,iBAAW,GAAY,IAAI,CAAA;QAE3B,uBAAuB;QAEvB,kBAAY,GAAY,IAAI,CAAA;QAE5B,wBAAwB;QAExB,qBAAe,GAAY,IAAI,CAAA;QAE/B,sBAAgB,GAAY,IAAI,CAAA;QAEhC,cAAQ,GAAc,IAAI,CAAC;QAE3B,eAAS,GAAc,IAAI,CAAC;QAG5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,gBAAU,GAAc,IAAI,CAAA;QAE5B,iBAAW,GAAc,IAAI,CAAA;QAE7B,iBAAW,GAAc,IAAI,CAAA;QAE7B,eAAS,GAAa,EAAE,CAAA,CAAA,WAAW;QACnC,cAAQ,GAAa,EAAE,CAAA;QACvB,0BAAoB,GAAa,EAAE,CAAA;QACnC,mBAAa,GAAa,EAAE,CAAA;QAC5B,gBAAU,GAAa,EAAE,CAAA;QACzB,gBAAU,GAAa,EAAE,CAAA;QACzB,aAAO,GAAa,EAAE,CAAA;QACtB,oBAAc,GAAa,EAAE,CAAA;QAE7B,mBAAa,GAAgB,EAAE,CAAA;QAG/B,kBAAY,GAAa,IAAI,CAAA,CAAC,SAAS;QAEvC,iCAAiC;QACzB,qBAAe,GAAW,CAAC,CAAA;QAEnC,YAAY;QACJ,uBAAiB,GAAW,GAAG,CAAA;;QA0RvC,iBAAiB;IACrB,CAAC;IAzRG,qCAAM,GAAN;QAEA,6BAA6B;QAC7B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,4BAA4B;QAC5B,QAAQ;QAGR,yBAAyB;QACzB,0CAA0C;QAC1C,uDAAuD;QACvD,oDAAoD;QACpD,iDAAiD;QACjD,4CAA4C;QAC5C,gDAAgD;QAChD,qDAAqD;QACrD,mBAAmB;QACnB,yBAAyB;QACzB,2CAA2C;QAC3C,2CAA2C;QAC3C,2CAA2C;QAC3C,2CAA2C;QAC3C,2CAA2C;QAC3C,QAAQ;QACR,oCAAoC;QACpC,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,wDAAwD;QACxD,QAAQ;QACR,6BAA6B;QAC7B,qDAAqD;QACrD,QAAQ;QACR,0BAA0B;QAC1B,kDAAkD;QAClD,kDAAkD;QAClD,kDAAkD;QAClD,kDAAkD;QAClD,kDAAkD;QAClD,kDAAkD;QAClD,kDAAkD;QAClD,QAAQ;QACR,0BAA0B;QAC1B,6CAA6C;QAC7C,6CAA6C;QAC7C,6CAA6C;QAC7C,QAAQ;QACR,uBAAuB;QACvB,iDAAiD;QACjD,iDAAiD;QACjD,iDAAiD;QACjD,QAAQ;QACR,8BAA8B;QAC9B,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,sDAAsD;QACtD,uDAAuD;QACvD,uDAAuD;QACvD,QAAQ;IAIR,CAAC;IAED,oCAAK,GAAL;QAAA,iBA+FC;QA7FG,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,eAAM,CAAC,SAAS,GAAG,wBAAwB,EAAE,eAAM,CAAC,SAAS,GAAG,yBAAyB,EAAE;YAClI,KAAI,CAAC,IAAI,EAAE,CAAA;QACf,CAAC,CAAC,CAAC;QAEH,kBAAkB;QAClB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,aAAK,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE;gBACjC,KAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;YACxC,CAAC,CAAC,CAAC;SACN;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,aAAK,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE;gBAClC,KAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;YACxC,CAAC,CAAC,CAAC;SACN;QAED,uBAAuB;QACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEjC,0CAA0C;QAE1C,2CAA2C;QAC3C,gDAAgD;QAChD,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QAET,2CAA2C;QAC3C,4DAA4D;QAC5D,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAE/C,kEAAkE;QAClE,oDAAoD;QAEpD,6CAA6C;QAC7C,SAAS;QACT,2CAA2C;QAC3C,qDAAqD;QACrD,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QACT,qDAAqD;QACrD,yCAAyC;QAGzC,2CAA2C;QAC3C,kDAAkD;QAClD,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QACT,qDAAqD;QACrD,yCAAyC;QAEzC,2CAA2C;QAC3C,kDAAkD;QAClD,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QACT,2CAA2C;QAC3C,+CAA+C;QAC/C,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QACT,2CAA2C;QAC3C,sDAAsD;QACtD,mEAAmE;QACnE,mFAAmF;QACnF,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS;QACT,IAAI;QAGJ,gCAAgC;QAChC,8DAA8D;QAC9D,yEAAyE;QACzE,2CAA2C;QAC3C,yCAAyC;QACzC,IAAI;IACJ,CAAC;IAED;;OAEG;IACK,oDAAqB,GAA7B;QACI,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC/C,kBAAkB;YAClB,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,KAAK,CAAC;YACrC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;YAEzB,iBAAiB;YACjB,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACnD;SACJ;IACL,CAAC;IAED;;;OAGG;IACK,+CAAgB,GAAxB,UAAyB,QAAgB;QACrC,IAAI,IAAI,CAAC,eAAe,KAAK,QAAQ,EAAE;YACnC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC9B,OAAO,CAAC,kBAAkB;SAC7B;QAED,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAEhC,OAAO,CAAC,GAAG,CAAC,0BAAQ,IAAI,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAE,CAAC,CAAC;QAEpE,eAAe;QACf,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,iBAAiB;QACjB,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,mDAAoB,GAA5B;QACI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO;SACV;QAED,6BAA6B;QAC7B,IAAM,YAAY,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,IAAM,aAAa,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAExC,IAAM,cAAc,GAAG,IAAI,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC;QAEjF,WAAW;QACX,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC;aACtB,EAAE,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;aAC7D,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,sDAAuB,GAA/B;QACI,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACjD,OAAO;SACV;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC,EAAE;YAC5B,SAAS;YACT,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,KAAK,CAAC;SACxC;aAAM;YACH,SAAS;YACT,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAC;YACpC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC;SACvC;IACL,CAAC;IAID,mCAAI,GAAJ,UAAK,YAAsB;QACvB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAA;QACtB,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,KAAK,EAAE,CAAC;IACjB,CAAC;IACD,mCAAI,GAAJ;QAAA,iBAWC;QAVG,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,EAAE,CAAA;SACtB;QACD,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,IAAI,CAAC;YACF,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IA3VD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;yDACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACQ;IAI1B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACQ;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACS;IAI3B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACU;IAI5B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;iEACa;IAE/B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;kEACc;IAEhC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;0DACO;IAE3B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;2DACQ;IAG5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACS;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACS;IAlDZ,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CAiWxC;IAAD,2BAAC;CAjWD,AAiWC,CAjWiD,EAAE,CAAC,SAAS,GAiW7D;kBAjWoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\n\nimport InfoItemController from \"../pfb/InfoItemController\";\nimport InfoItemOneController from \"../pfb/InfoItemOneController\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\n\n//游戏道具介绍页面\n@ccclass\nexport default class InfoDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null\n    @property(cc.Node)\n    boardBtnClose: cc.Node = null\n    @property(cc.Node)\n    contentLay: cc.Node = null\n\n    // 新增：单机规则和联机规则文字按钮\n    @property(cc.Node)\n    danjiLabel: cc.Node = null\n    @property(cc.Node)\n    lianjiLabel: cc.Node = null\n\n    // 新增：可移动的视觉效果按钮（不需要点击）\n    @property(cc.Node)\n    switchButton: cc.Node = null\n\n    // 新增：单机和联机规则的ScrollView\n    @property(cc.Node)\n    danjiScrollView: cc.Node = null\n    @property(cc.Node)\n    duorenScrollView: cc.Node = null\n    @property(cc.Prefab)\n    infoItem: cc.Prefab = null;\n    @property(cc.Prefab)\n    infoItem1: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    infoImage1: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage2: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage3: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage4: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage5: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage6: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage7: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage8: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage9: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage10: cc.Prefab = null\n    @property(cc.Prefab)\n    infoImage11: cc.Prefab = null\n\n    titleList: string[] = []//title 的列表\n    tipsList: string[] = []\n    generationMethodList: string[] = []\n    permanentList: string[] = []\n    randomList: string[] = []\n    chainsList: string[] = []\n    iceList: string[] = []\n    scoringDetails: string[] = []\n\n    infoImageList: cc.Prefab[] = []\n\n\n    backCallback: Function = null //隐藏弹窗的回调\n\n    // 新增：当前选中的规则类型（0: 单机规则, 1: 联机规则）\n    private currentRuleType: number = 0\n\n    // 新增：动画持续时间\n    private animationDuration: number = 0.3\n\n    onLoad() {\n\n    //     this.infoImageList = [\n    //         this.infoImage3,\n    //         this.infoImage4,\n    //         this.infoImage5,\n    //         this.infoImage6,\n    //         this.infoImage7,\n    //         this.infoImage8,\n    //         this.infoImage9,\n    //         this.infoImage10,\n    //         this.infoImage11,\n    //     ]\n\n\n    //     this.titleList = [\n    //         window.getLocalizedStr('Tips'),\n    //         window.getLocalizedStr('Generation_Method'),\n    //         window.getLocalizedStr('Permanent_Task'),\n    //         window.getLocalizedStr('Random_Task'),\n    //         window.getLocalizedStr('Chains'),\n    //         window.getLocalizedStr('Ice_Blocks'),\n    //         window.getLocalizedStr('Scoring_Details'),\n    //     ]//title 的列表\n    //     this. tipsList = [\n    //         window.getLocalizedStr('Tips1'),\n    //         window.getLocalizedStr('Tips2'),\n    //         window.getLocalizedStr('Tips3'),\n    //         window.getLocalizedStr('Tips4'),\n    //         window.getLocalizedStr('Tips5'),\n    //     ]\n    //     this.generationMethodList = [\n    //         window.getLocalizedStr('Generation_Method1'),\n    //         window.getLocalizedStr('Generation_Method2'),\n    //         window.getLocalizedStr('Generation_Method3'),\n    //         window.getLocalizedStr('Generation_Method4'),\n    //         window.getLocalizedStr('Generation_Method5'),\n    //         window.getLocalizedStr('Generation_Method6'),\n    //         window.getLocalizedStr('Generation_Method7'),\n    //         window.getLocalizedStr('Generation_Method8'),\n    //         window.getLocalizedStr('Generation_Method9'),\n    //     ]\n    //     this.permanentList = [\n    //         window.getLocalizedStr('Permanent_Task1'),\n    //     ]\n    //     this.randomList = [\n    //         window.getLocalizedStr('Random_Task1'),\n    //         window.getLocalizedStr('Random_Task2'),\n    //         window.getLocalizedStr('Random_Task3'),\n    //         window.getLocalizedStr('Random_Task4'),\n    //         window.getLocalizedStr('Random_Task5'),\n    //         window.getLocalizedStr('Random_Task6'),\n    //         window.getLocalizedStr('Random_Task7'),\n    //     ]\n    //     this.chainsList = [\n    //         window.getLocalizedStr('Chains1'),\n    //         window.getLocalizedStr('Chains2'),\n    //         window.getLocalizedStr('Chains3'),\n    //     ]\n    //     this.iceList = [\n    //         window.getLocalizedStr('Ice_Blocks1'),\n    //         window.getLocalizedStr('Ice_Blocks2'),\n    //         window.getLocalizedStr('Ice_Blocks3'),\n    //     ]\n    //     this.scoringDetails = [\n    //         window.getLocalizedStr('Scoring_Details1'),\n    //         window.getLocalizedStr('Scoring_Details2'),\n    //         window.getLocalizedStr('Scoring_Details3'),\n    //         window.getLocalizedStr('Scoring_Details4'),\n    //         window.getLocalizedStr('Scoring_Details5'),\n    //         window.getLocalizedStr('Scoring_Details6'),\n    //         window.getLocalizedStr('Scoring_Details7'),\n    //         window.getLocalizedStr('Scoring_Details8'),\n    //         window.getLocalizedStr('Scoring_Details9'),\n    //         window.getLocalizedStr('Scoring_Details10'),\n    //         window.getLocalizedStr('Scoring_Details11'),\n    //     ]\n\n\n\n    }\n\n    start() {\n\n        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {\n            this.hide()\n        });\n\n        // 新增：设置单机规则文字点击事件\n        if (this.danjiLabel) {\n            Tools.setTouchEvent(this.danjiLabel, () => {\n                this.switchToRuleType(0); // 切换到单机规则\n            });\n        }\n\n        // 新增：设置联机规则文字点击事件\n        if (this.lianjiLabel) {\n            Tools.setTouchEvent(this.lianjiLabel, () => {\n                this.switchToRuleType(1); // 切换到联机规则\n            });\n        }\n\n        // 新增：初始化显示状态（默认显示单机规则）\n        this.initializeRuleDisplay();\n\n    //     this.contentLay.removeAllChildren()\n\n    //     this.getTitleNode(this.titleList[0])\n    //     this.tipsList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n\n    //     this.getTitleNode(this.titleList[1])\n    //     this.generationMethodList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n\n    //         let infoImg = cc.instantiate(this.infoImageList[index])\n    //         infoItemOneController.setimgNode(infoImg)\n\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    //     this.getTitleNode(this.titleList[2])\n    //     this.permanentList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    //     let infoImg1 = cc.instantiate(this.infoImage1)\n    //     this.contentLay.addChild(infoImg1)\n\n\n    //     this.getTitleNode(this.titleList[3])\n    //     this.randomList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    //     let infoImg2 = cc.instantiate(this.infoImage2)\n    //     this.contentLay.addChild(infoImg2)\n\n    //     this.getTitleNode(this.titleList[4])\n    //     this.chainsList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    //     this.getTitleNode(this.titleList[5])\n    //     this.iceList.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    //     this.getTitleNode(this.titleList[6])\n    //     this.scoringDetails.forEach((title, index) => {\n    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体\n    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)\n    //         infoItemOneController.setData(title)\n    //         this.contentLay.addChild(infoItem)\n    //     })\n    // }\n\n\n    // getTitleNode(title: string) {\n    //     let infoItem = cc.instantiate(this.infoItem);//初始化一个预制体\n    //     let infoItemController = infoItem.getComponent(InfoItemController)\n    //     infoItemController.setContent(title)\n    //     this.contentLay.addChild(infoItem)\n    // }\n    }\n\n    /**\n     * 新增：初始化规则显示状态\n     */\n    private initializeRuleDisplay() {\n        if (this.danjiScrollView && this.duorenScrollView) {\n            // 默认显示单机规则，隐藏联机规则\n            this.danjiScrollView.active = true;\n            this.duorenScrollView.active = false;\n            this.currentRuleType = 0;\n\n            // 设置按钮初始位置（左边位置）\n            if (this.switchButton) {\n                this.switchButton.position = cc.v3(-150, -2, 0);\n            }\n        }\n    }\n\n    /**\n     * 新增：切换到指定规则类型\n     * @param ruleType 0: 单机规则, 1: 联机规则\n     */\n    private switchToRuleType(ruleType: number) {\n        if (this.currentRuleType === ruleType) {\n            console.log(\"已经是当前规则类型，无需切换\");\n            return; // 如果已经是当前类型，不需要切换\n        }\n\n        this.currentRuleType = ruleType;\n\n        console.log(`切换到: ${this.currentRuleType === 0 ? '单机规则' : '联机规则'}`);\n\n        // 移动按钮位置（视觉效果）\n        this.moveButtonToPosition();\n\n        // 切换ScrollView显示\n        this.switchScrollViewDisplay();\n    }\n\n    /**\n     * 新增：移动按钮到指定位置\n     */\n    private moveButtonToPosition() {\n        if (!this.switchButton) {\n            return;\n        }\n\n        // 按钮位置：左边（-150，-2）右边（142，-2）\n        const leftPosition = cc.v3(-150, -2, 0);\n        const rightPosition = cc.v3(142, -2, 0);\n\n        const targetPosition = this.currentRuleType === 0 ? leftPosition : rightPosition;\n\n        // 使用动画移动按钮\n        cc.tween(this.switchButton)\n            .to(0.3, { position: targetPosition }, { easing: 'quartOut' })\n            .start();\n    }\n\n    /**\n     * 新增：切换ScrollView显示\n     */\n    private switchScrollViewDisplay() {\n        if (!this.danjiScrollView || !this.duorenScrollView) {\n            return;\n        }\n\n        if (this.currentRuleType === 0) {\n            // 显示单机规则\n            this.danjiScrollView.active = true;\n            this.duorenScrollView.active = false;\n        } else {\n            // 显示联机规则\n            this.danjiScrollView.active = false;\n            this.duorenScrollView.active = true;\n        }\n    }\n\n\n\n    show(backCallback: Function) {\n        this.backCallback = backCallback\n        this.node.active = true\n        this.boardBg.scale = 0\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 1 })\n            .start();\n    }\n    hide() {\n        if (this.backCallback) {\n            this.backCallback()\n        }\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 0 })\n            .call(() => {\n                this.node.active = false\n            })\n            .start();\n    }\n\n    // update (dt) {}\n}"]}