{"version": 3, "sources": ["assets/scripts/pfb/PlayerGameController .ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,uCAAsC;AAEhC,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAG1C;IAAkD,wCAAY;IAA9D;QAAA,qEA2IC;QAxIG,YAAM,GAAY,IAAI,CAAC,CAAG,IAAI;QAE9B,cAAQ,GAAY,IAAI,CAAC,CAAE,MAAM;QAEjC,kBAAY,GAAY,IAAI,CAAC,CAAE,iBAAiB;QAEhD,kBAAY,GAAY,IAAI,CAAC,CAAE,oBAAoB;;QAiInD,iBAAiB;IACrB,CAAC;IA9HG,oCAAK,GAAL;IAEA,CAAC;IAED,sCAAO,GAAP,UAAQ,IAAc;QAAtB,iBASC;QARG,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,IAAI,IAAI,IAAI,EAAE;gBACd,KAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC;aAC9B;iBAAM;gBACH,aAAK,CAAC,qBAAqB,CAAC,KAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA,MAAM;gBAC5D,KAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;aAC7B;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAID;;;OAGG;IACH,2CAAY,GAAZ,UAAa,QAAgB;QAA7B,iBAuDC;QApDG,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,4BAA4B;YAC5B,IAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC1E,IAAI,gBAAgB,EAAE;gBAClB,IAAM,cAAc,GAAG,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC/D,IAAI,cAAc,EAAE;oBAChB,cAAc,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;iBAErD;aACJ;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;aAC9D;YAED,kBAAkB;YAClB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;YAEnD,UAAU;YACV,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE5C,SAAS;YACT,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC;YAC9B,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,CAAC;YAE9B,SAAS;YACT,IAAI,WAAS,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YAEpC,gBAAgB;YAChB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC;iBACtB,QAAQ,CACL,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,EACrC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EACnC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CACjC;iBACA,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;iBACvB,KAAK,CAAC,GAAG,CAAC;iBACV,QAAQ,CACL,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EACnC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EACnC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAChC;iBACA,IAAI,CAAC;gBACF,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;gBACjC,KAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,CAAC;gBAChC,KAAI,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,CAAC;gBAC9B,KAAI,CAAC,YAAY,CAAC,CAAC,GAAG,WAAS,CAAC;YAEpC,CAAC,CAAC;iBACD,KAAK,EAAE,CAAC;SAChB;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAC9C;IACL,CAAC;IAED;;;OAGG;IACH,2CAAY,GAAZ,UAAa,QAAgB;QAA7B,iBA+BC;QA5BG,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,4BAA4B;YAC5B,IAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC1E,IAAI,gBAAgB,EAAE;gBAClB,IAAM,cAAc,GAAG,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC/D,IAAI,cAAc,EAAE;oBAChB,cAAc,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;iBAErD;aACJ;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;aAC9D;YAED,kBAAkB;YAClB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;YAEnD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;YAEhC,QAAQ;YACR,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,KAAI,CAAC,YAAY,EAAE;oBACnB,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;iBAEpC;YACL,CAAC,EAAE,GAAG,CAAC,CAAC;SACX;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAC9C;IACL,CAAC;IAED,UAAU;IACV,+CAAgB,GAAhB;QACI,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SACpC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SACpC;IACL,CAAC;IArID;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;wDACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;0DACO;IAEzB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACW;IATZ,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CA2IxC;IAAD,2BAAC;CA3ID,AA2IC,CA3IiD,EAAE,CAAC,SAAS,GA2I7D;kBA3IoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { RoomUser } from \"../bean/GameBean\";\nimport { Tools } from \"../util/Tools\";\n\nconst {ccclass, property} = cc._decorator;\n\n@ccclass\nexport default class PlayerGameController extends cc.Component {\n\n    @property(cc.Node)\n    avatar: cc.Node = null;   //头像\n    @property(cc.Node)\n    flagNode: cc.Node = null;  //旗子节点\n    @property(cc.Node)\n    addScoreNode: cc.Node = null;  //加分背景节点 addscore\n    @property(cc.Node)\n    subScoreNode: cc.Node = null;  //减分背景节点 deductscore\n\n \n\n    start () {\n    \n    }\n\n    setData(user: RoomUser){\n        this.scheduleOnce(() => {\n            if (user == null) {\n                this.avatar.active = false;\n            } else {\n                Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);//添加头像\n                this.avatar.active = true;\n            }\n        }, 0.1);\n    }\n\n   \n\n    /**\n     * 显示加分效果，带动画\n     * @param addValue 加分数值\n     */\n    showAddScore(addValue: number) {\n\n\n        if (this.addScoreNode) {\n            // 获取change_score文本节点并设置加分文本\n            const changeScoreLabel = this.addScoreNode.getChildByName(\"change_score\");\n            if (changeScoreLabel) {\n                const labelComponent = changeScoreLabel.getComponent(cc.Label);\n                if (labelComponent) {\n                    labelComponent.string = \"+\" + addValue.toString();\n\n                }\n            } else {\n                console.warn(\"PlayerGame addScoreNode中找不到change_score子节点\");\n            }\n\n            // 设置最高层级，确保不被头像遮挡\n            this.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 5;\n\n            // 停止之前的动画\n            cc.Tween.stopAllByTarget(this.addScoreNode);\n\n            // 重置节点状态\n            this.addScoreNode.active = true;\n            this.addScoreNode.opacity = 0;\n            this.addScoreNode.scale = 0.8;\n\n            // 保存原始位置\n            let originalY = this.addScoreNode.y;\n\n            // 使用新的Tween API\n            cc.tween(this.addScoreNode)\n                .parallel(\n                    cc.tween().to(0.15, { opacity: 255 }),\n                    cc.tween().to(0.15, { scale: 1.1 }),\n                    cc.tween().by(0.15, { y: 15 })\n                )\n                .to(0.1, { scale: 1.0 })\n                .delay(0.8)\n                .parallel(\n                    cc.tween().to(0.25, { opacity: 0 }),\n                    cc.tween().to(0.25, { scale: 0.9 }),\n                    cc.tween().by(0.25, { y: 8 })\n                )\n                .call(() => {\n                    this.addScoreNode.active = false;\n                    this.addScoreNode.opacity = 255;\n                    this.addScoreNode.scale = 1.0;\n                    this.addScoreNode.y = originalY;\n                 \n                })\n                .start();\n        } else {\n            console.warn(\"PlayerGame addScoreNode未设置\");\n        }\n    }\n\n    /**\n     * 显示减分效果\n     * @param subValue 减分数值\n     */\n    showSubScore(subValue: number) {\n\n\n        if (this.subScoreNode) {\n            // 获取change_score文本节点并设置减分文本\n            const changeScoreLabel = this.subScoreNode.getChildByName(\"change_score\");\n            if (changeScoreLabel) {\n                const labelComponent = changeScoreLabel.getComponent(cc.Label);\n                if (labelComponent) {\n                    labelComponent.string = \"-\" + subValue.toString();\n\n                }\n            } else {\n                console.warn(\"PlayerGame subScoreNode中找不到change_score子节点\");\n            }\n\n            // 设置最高层级，确保不被头像遮挡\n            this.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 5;\n\n            this.subScoreNode.active = true;\n\n            // 1秒后隐藏\n            this.scheduleOnce(() => {\n                if (this.subScoreNode) {\n                    this.subScoreNode.active = false;\n                   \n                }\n            }, 1.0);\n        } else {\n            console.warn(\"PlayerGame subScoreNode未设置\");\n        }\n    }\n\n    // 隐藏加减分节点\n    hideScoreEffects() {\n        if (this.addScoreNode) {\n            this.addScoreNode.active = false;\n        }\n        if (this.subScoreNode) {\n            this.subScoreNode.active = false;\n        }\n    }\n\n    // update (dt) {}\n}\n"]}