"use strict";
cc._RF.push(module, 'f9b83/qkM9GlaEvLsvMwW8P', 'EventCenter');
// scripts/common/EventCenter.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventType = exports.EventCenter = void 0;
var Singleton_1 = require("../../meshTools/Singleton");
var GameMgr_1 = require("./GameMgr");
var EventCenter = /** @class */ (function (_super) {
    __extends(EventCenter, _super);
    function EventCenter() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._handle = {};
        return _this;
    }
    EventCenter.prototype.AddEventListener = function (eventName, cb, target) {
        GameMgr_1.GameMgr.Console.Log('添加监听：' + eventName);
        eventName = eventName.toString();
        if (!this._handle[eventName]) {
            this._handle[eventName] = [];
        }
        var eventData = {};
        eventData.cb = cb;
        eventData.target = target;
        this._handle[eventName].push(eventData);
    };
    EventCenter.prototype.RemoveEventListener = function (eventName, cb, target) {
        var _a;
        GameMgr_1.GameMgr.Console.Log('移除监听：' + eventName);
        eventName = eventName.toString();
        var list = (_a = this._handle[eventName]) !== null && _a !== void 0 ? _a : [];
        if (list.length <= 0) {
            return;
        }
        for (var i = 0; i < list.length; ++i) {
            var event = list[i];
            if (event.cb === cb && (!target || target === event.target)) {
                list.splice(i, 1);
            }
        }
    };
    EventCenter.prototype.Send = function (eventName) {
        var _a;
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        eventName = eventName.toString();
        var list = (_a = this._handle[eventName]) !== null && _a !== void 0 ? _a : [];
        if (list.length <= 0) {
            return;
        }
        for (var i = 0; i < list.length; ++i) {
            var event = list[i];
            event.cb.apply(event.target, params);
        }
    };
    return EventCenter;
}(Singleton_1.Singleton));
exports.EventCenter = EventCenter;
//通知的消息类型
var EventType;
(function (EventType) {
    EventType["ReceiveMessage"] = "receiveMessage";
    EventType["ReceiveErrorMessage"] = "receiveErrorMessage";
    EventType["AutoMessage"] = "autoMessage";
})(EventType = exports.EventType || (exports.EventType = {}));

cc._RF.pop();