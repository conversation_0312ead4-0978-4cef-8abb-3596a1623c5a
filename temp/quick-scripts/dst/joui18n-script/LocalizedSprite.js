
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/joui18n-script/LocalizedSprite.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '26d02ZV5VhNPaJdQ9+G+shN', 'LocalizedSprite');
// LocalizedSprite.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalizedSprite = exports.LocalizedSpriteData = void 0;
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, executeInEditMode = _a.executeInEditMode, menu = _a.menu;
var LocalizedSpriteData = /** @class */ (function () {
    function LocalizedSpriteData() {
        this.language = "";
        this.spriteFrame = null;
    }
    __decorate([
        property(cc.String)
    ], LocalizedSpriteData.prototype, "language", void 0);
    __decorate([
        property(cc.SpriteFrame)
    ], LocalizedSpriteData.prototype, "spriteFrame", void 0);
    LocalizedSpriteData = __decorate([
        ccclass("LocalizedSpriteData")
    ], LocalizedSpriteData);
    return LocalizedSpriteData;
}());
exports.LocalizedSpriteData = LocalizedSpriteData;
var LocalizedSprite = /** @class */ (function (_super) {
    __extends(LocalizedSprite, _super);
    function LocalizedSprite() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.spriteDataArray = new Array();
        _this.curSprite = null;
        return _this;
    }
    LocalizedSprite.prototype.onLoad = function () {
        this.curSprite = this.getComponent(cc.Sprite);
        if (this.curSprite == null) {
            cc.error("There is no cc.Sprite on LocalizedSprite, nodeName: " + this.node.name);
            this.enabled = false;
        }
        // 根据当前已有的语言，自动填充LocalizedSpriteDataArray
        if (CC_EDITOR && this.spriteDataArray.length === 0) {
            var cocos = cc;
            if (cocos.Jou_i18n !== null) {
                var nameArray = Object.keys(cocos.Jou_i18n);
                for (var i = 0, t = nameArray.length; i < t; i++) {
                    var name = nameArray[i];
                    if (name === "defaultLanguageName")
                        continue;
                    var data = new LocalizedSpriteData();
                    data.language = nameArray[i];
                    this.spriteDataArray.push(data);
                }
            }
        }
    };
    LocalizedSprite.prototype.onEnable = function () {
        this.refresh();
    };
    // 刷新cc.Sprite
    LocalizedSprite.prototype.refresh = function () {
        if (this.curSprite == null)
            return;
        var cocos = cc;
        var targetName;
        if (CC_EDITOR)
            targetName = cocos.Jou_i18n.defaultLanguageName;
        else
            targetName = window.languageName;
        for (var i = 0, t = this.spriteDataArray.length; i < t; i++) {
            var spriteData = this.spriteDataArray[i];
            if (spriteData.language === targetName) {
                this.curSprite.spriteFrame = spriteData.spriteFrame;
                return;
            }
        }
        this.curSprite.spriteFrame = null;
    };
    __decorate([
        property([LocalizedSpriteData])
    ], LocalizedSprite.prototype, "spriteDataArray", void 0);
    LocalizedSprite = __decorate([
        ccclass,
        executeInEditMode,
        menu("i18n/LocalizedSprite")
    ], LocalizedSprite);
    return LocalizedSprite;
}(cc.Component));
exports.LocalizedSprite = LocalizedSprite;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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