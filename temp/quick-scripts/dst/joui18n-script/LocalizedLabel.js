
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/joui18n-script/LocalizedLabel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '89242RzotlBU5Nw9GrIJCE0', 'LocalizedLabel');
// LocalizedLabel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalizedLabel = void 0;
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, executeInEditMode = _a.executeInEditMode, menu = _a.menu;
var LocalizedLabel = /** @class */ (function (_super) {
    __extends(LocalizedLabel, _super);
    function LocalizedLabel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._key = "";
        _this.curLabel = null;
        _this.paramStrArray = new Array();
        return _this;
    }
    Object.defineProperty(LocalizedLabel.prototype, "key", {
        get: function () { return this._key; },
        set: function (v) {
            this._key = v;
            this.refresh();
        },
        enumerable: false,
        configurable: true
    });
    LocalizedLabel.prototype.onLoad = function () {
        this.curLabel = this.getComponent(cc.Label);
        if (this.curLabel == null) {
            cc.error("There is no cc.Label on LocalizedLabel, nodeName: " + this.node.name);
            this.enabled = false;
        }
    };
    LocalizedLabel.prototype.onEnable = function () {
        this.refresh();
    };
    // 刷新cc.Label
    LocalizedLabel.prototype.refresh = function () {
        if (this.curLabel == null)
            return;
        var str = window.getLocalizedStr(this._key);
        // 填充参数
        for (var i = 0, t = this.paramStrArray.length; i < t; i++)
            str = str.replace("{" + i + "}", this.paramStrArray[i]);
        this.curLabel.string = str;
    };
    // 绑定参数
    LocalizedLabel.prototype.bindParam = function () {
        var paramStrArray = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            paramStrArray[_i] = arguments[_i];
        }
        this.bindParamStrArray(paramStrArray);
    };
    // 绑定参数StrArray
    LocalizedLabel.prototype.bindParamStrArray = function (paramStrArray) {
        if (paramStrArray == null)
            return;
        this.paramStrArray = paramStrArray;
        this.refresh();
    };
    __decorate([
        property({ serializable: true })
    ], LocalizedLabel.prototype, "_key", void 0);
    __decorate([
        property(cc.String)
    ], LocalizedLabel.prototype, "key", null);
    LocalizedLabel = __decorate([
        ccclass,
        executeInEditMode,
        menu("i18n/LocalizedLabel")
    ], LocalizedLabel);
    return LocalizedLabel;
}(cc.Component));
exports.LocalizedLabel = LocalizedLabel;
window.getLocalizedStr = function (key) {
    var cocos = cc;
    if (CC_EDITOR) {
        var defaultName = cocos.Jou_i18n.defaultLanguageName;
        var language = cocos.Jou_i18n[defaultName];
        if (language != null && language[key] != null)
            return language[key];
    }
    else {
        if (window.languageName != null && window.languageName !== "") {
            var language = cocos.Jou_i18n[window.languageName];
            if (language[key] != null)
                return language[key];
        }
        else
            console.error("LanguageName is null, please set it first.");
    }
    return key;
};
window.refreshAllLocalizedComp = function () {
    var rootNode = cc.director.getScene();
    for (var i = 0, t = rootNode.childrenCount; i < t; i++) {
        var curNode = rootNode.children[i];
        var labelArray = curNode.getComponentsInChildren("LocalizedLabel");
        for (var j = 0, tt = labelArray.length; j < tt; j++)
            labelArray[j].refresh();
        var spriteArray = curNode.getComponentsInChildren("LocalizedSprite");
        for (var j = 0, tt = spriteArray.length; j < tt; j++)
            spriteArray[j].refresh();
    }
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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