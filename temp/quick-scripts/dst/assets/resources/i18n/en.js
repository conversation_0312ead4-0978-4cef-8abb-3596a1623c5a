
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/resources/i18n/en.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cc55fhCTRdMjZxuQuVowyEX', 'en');
// resources/i18n/en.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: 'You have been asked to leave the room',
    LeaveRoom: 'The room is dissolved',
    InsufficientBalance: 'The current balance is insufficient, please go to purchase',
    GameRouteNotFound: 'Game route not found',
    NetworkError: 'network error',
    RoomIsFull: 'Room is full',
    EnterRoomNumber: 'Enter room number',
    GetUserInfoFailed: 'get user info failed',
    RoomDoesNotExist: 'Room does not exist',
    FailedToDeductGoldCoins: 'Failed to deduct gold coins',
    ExitApplication: 'Are you sure to leave?',
    QuitTheGame: 'Once you exit the game, you won’t be able to return to it.',
    NotEnoughPlayers: 'Not enough players',
    TheGameIsFullOfPlayers: 'The game is full of players',
    kickout2: 'Whether to kick {0} out of the room?',
    upSeat: 'Join',
    downSeat: 'Leave',
    startGame: 'Start',
    readyGame: 'Ready',
    cancelGame: 'Cancel',
    cancel: 'Cancel',
    confirm: 'Confirm',
    kickout3: 'Kick Out',
    back: 'Back',
    leave: 'Leave',
    music: 'Music',
    sound: 'Sound',
    join: 'Join',
    create: 'Create',
    auto: 'Auto',
    Room: 'Room',
    room_number: 'Room Number',
    copy: 'Copy',
    game_amount: 'Game Amount',
    player_numbers: 'Player Numbers:',
    room_exist: 'Room doesn’t exist',
    enter_room_number: 'Enter room number',
    free: 'Free',
    players: 'Players',
    Player: 'Player',
    Tickets: 'Tickets',
    Empty: 'Empty',
    nextlevel: 'Next',
    relevel: 'Play Again',
    rules: 'Rules',
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.en = exports.language;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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