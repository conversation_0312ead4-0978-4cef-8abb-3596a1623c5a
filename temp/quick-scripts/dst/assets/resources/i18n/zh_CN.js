
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/resources/i18n/zh_CN.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '83ee5xvZ1VBLb3UezEVIqou', 'zh_CN');
// resources/i18n/zh_CN.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: '您被请出房间',
    LeaveRoom: '房间已解散',
    InsufficientBalance: '余额不足，去充值',
    GameRouteNotFound: '游戏线路异常',
    NetworkError: '网络异常',
    RoomIsFull: '房间已满',
    EnterRoomNumber: '输入房间号',
    GetUserInfoFailed: '获取用户信息失败',
    RoomDoesNotExist: '房间不存在',
    FailedToDeductGoldCoins: '扣除金币失败',
    ExitApplication: '确定退出游戏？',
    QuitTheGame: '退出后将无法返回游戏。',
    NotEnoughPlayers: '玩家数量不足',
    TheGameIsFullOfPlayers: '玩家数量已满',
    kickout2: '是否将 {0} 请出房间?',
    upSeat: '加入游戏',
    downSeat: '退出游戏',
    startGame: '开始',
    readyGame: '准备',
    cancelGame: '取消准备',
    cancel: '取消',
    confirm: '确定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音乐',
    sound: '音效',
    join: '加入',
    create: '创建',
    auto: '匹配',
    Room: '房间',
    room_number: '房间号',
    copy: '复制',
    game_amount: '游戏费用',
    player_numbers: '玩家数量:',
    room_exist: '房间不存在',
    enter_room_number: '输入房间号',
    free: '免费',
    players: '玩家',
    Player: '玩家',
    Tickets: '门票',
    Empty: '空位',
    nextlevel: '下一关',
    relevel: '再玩一次',
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_CN = exports.language;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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