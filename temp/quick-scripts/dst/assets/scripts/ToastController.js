
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/ToastController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9aee1Hwx5RBJoqOKsbha9O+', 'ToastController');
// scripts/ToastController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ToastController = /** @class */ (function (_super) {
    __extends(ToastController, _super);
    function ToastController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.alertBg = null;
        _this.toastLabel = null;
        _this.isShow = false;
        return _this;
    }
    // onLoad () {}
    ToastController.prototype.start = function () {
    };
    //设置提示文案
    ToastController.prototype.showContent = function (content) {
        if (this.isShow) { //判断当前是否正在显示提示文案  如果显示的话 就把上一个的倒计时关掉
            this.unschedule(this.hideContent);
        }
        this.toastLabel.string = content;
        this.alertBg.active = true;
        this.isShow = true;
        this.scheduleOnce(this.hideContent, 1.5);
    };
    ToastController.prototype.hideContent = function () {
        this.alertBg.active = false;
        this.isShow = false;
        this.toastLabel.string = '';
    };
    __decorate([
        property(cc.Node)
    ], ToastController.prototype, "alertBg", void 0);
    __decorate([
        property(cc.Label)
    ], ToastController.prototype, "toastLabel", void 0);
    ToastController = __decorate([
        ccclass
    ], ToastController);
    return ToastController;
}(cc.Component));
exports.default = ToastController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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