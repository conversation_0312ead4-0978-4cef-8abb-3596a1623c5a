
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/SettingDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6caf8s2g5RK05G0nSu4NIPc', 'SettingDialogController');
// scripts/hall/SettingDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Publish_1 = require("../../meshTools/tools/Publish");
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var LocalStorageManager_1 = require("../util/LocalStorageManager");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var SettingDialogController = /** @class */ (function (_super) {
    __extends(SettingDialogController, _super);
    function SettingDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        _this.musicBtn = null;
        _this.soundBtn = null;
        _this.versoion = null; //版本号
        _this.music = true;
        _this.sound = true;
        _this.backCallback = null; //隐藏弹窗的回调
        return _this;
        // update (dt) {}
    }
    SettingDialogController.prototype.onLoad = function () {
        this.music = LocalStorageManager_1.LocalStorageManager.GetInstance().getMusicSwitch();
        this.sound = LocalStorageManager_1.LocalStorageManager.GetInstance().getSoundSwitch();
    };
    SettingDialogController.prototype.start = function () {
        var _this = this;
        this.versoion.string = "V " + Publish_1.Publish.GetInstance().getVersion();
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        this.setSwitch(this.musicBtn, this.music);
        this.setSwitch(this.soundBtn, this.sound);
        Tools_1.Tools.setTouchEvent(this.musicBtn, function (node) {
            _this.music = !_this.music;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setMusicSwitch(_this.music);
            _this.setSwitch(node, _this.music);
            if (_this.music) {
                AudioManager_1.AudioManager.playBgm();
            }
            else {
                AudioManager_1.AudioManager.stopBgm();
            }
        });
        Tools_1.Tools.setTouchEvent(this.soundBtn, function (node) {
            _this.sound = !_this.sound;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setSoundSwitch(_this.sound);
            _this.setSwitch(node, _this.sound);
        });
    };
    SettingDialogController.prototype.setSwitch = function (node, switchType) {
        if (switchType) {
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.buttonRes + 'btn_switch_02');
        }
        else {
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.buttonRes + 'btn_switch_01');
        }
    };
    SettingDialogController.prototype.show = function (backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    SettingDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], SettingDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], SettingDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], SettingDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], SettingDialogController.prototype, "musicBtn", void 0);
    __decorate([
        property(cc.Node)
    ], SettingDialogController.prototype, "soundBtn", void 0);
    __decorate([
        property(cc.Label)
    ], SettingDialogController.prototype, "versoion", void 0);
    SettingDialogController = __decorate([
        ccclass
    ], SettingDialogController);
    return SettingDialogController;
}(cc.Component));
exports.default = SettingDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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