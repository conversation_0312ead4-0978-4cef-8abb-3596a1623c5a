
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/HallPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '63546mbf2xAv6Hj/p5rHgOb', 'HallPageController');
// scripts/hall/HallPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HallOrMatch = void 0;
var GlobalBean_1 = require("../bean/GlobalBean");
var GameMgr_1 = require("../common/GameMgr");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var WebSocketTool_1 = require("../net/WebSocketTool");
var ToastController_1 = require("../ToastController");
var AudioManager_1 = require("../util/AudioManager");
var HallParentController_1 = require("./HallParentController");
var InfoDialogController_1 = require("./InfoDialogController");
var KickOutDialogController_1 = require("./KickOutDialogController");
var LeaveDialogController_1 = require("./LeaveDialogController");
var LevelSelectPageController_1 = require("./Level/LevelSelectPageController");
var MatchParentController_1 = require("./MatchParentController");
var SettingDialogController_1 = require("./SettingDialogController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HallOrMatch;
(function (HallOrMatch) {
    HallOrMatch[HallOrMatch["HALL_PARENT"] = 0] = "HALL_PARENT";
    HallOrMatch[HallOrMatch["MATCH_PARENT"] = 1] = "MATCH_PARENT";
})(HallOrMatch = exports.HallOrMatch || (exports.HallOrMatch = {}));
var HallPageController = /** @class */ (function (_super) {
    __extends(HallPageController, _super);
    function HallPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.hallParentNode = null;
        _this.matchParentNode = null;
        _this.infoDialogController = null; //道具简介弹窗
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.settingDialogController = null; //设置弹窗
        _this.kickOutDialogController = null; //踢出用户的 dialog
        _this.toastController = null; //toast 的布局
        _this.levelSelectPageController = null; //关卡选择页面控制器
        _this.hallOrMatch = null;
        return _this;
        // update (dt) {}
    }
    HallPageController.prototype.onLoad = function () {
        this.hallParentController = this.hallParentNode.getComponent(HallParentController_1.default);
        this.matchParentController = this.matchParentNode.getComponent(MatchParentController_1.default);
    };
    HallPageController.prototype.onEnable = function () {
        AudioManager_1.AudioManager.playBgm();
        this.setHallOrMatch(HallOrMatch.HALL_PARENT);
    };
    HallPageController.prototype.start = function () {
        var _this = this;
        this.hallParentController.setClick(function () {
            //返回键的回调
            _this.leaveDialogController.show(0, function () { });
        }, function () {
            //info 的回调
            _this.infoDialogController.show(function () { });
        }, function () {
            //设置键的回调
            _this.settingDialogController.show(function () { });
        }, function () {
            //start 按钮点击
            _this.startOrCreate(MessageId_1.MessageId.MsgTypePairRequest);
        }, function () {
            //create 点击创建房间
            _this.startOrCreate(MessageId_1.MessageId.MsgTypeCreateInvite);
        }, function (userId, nickname) {
            //点击创建房间内的 点击玩家头像 弹出的踢出房间弹窗
            _this.kickOutDialogController.show(userId, nickname);
        });
        this.matchParentController.setClick(function () {
            //匹配页面的返回键的回调
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelPair, {});
        });
    };
    HallPageController.prototype.updateGold = function () {
        this.hallParentController.updateGold();
    };
    //开始匹配 或者创建房间
    HallPageController.prototype.startOrCreate = function (msgId) {
        //判断是否链接成功，并且还得有登录成功的数据返回 ，不成功的话就不允许执行下面的操作
        if (WebSocketManager_1.WebSocketManager.GetInstance().webState != WebSocketTool_1.WebSocketToolState.Connected || GlobalBean_1.GlobalBean.GetInstance().loginData == null) {
            return;
        }
        //点击 快速开始游戏 start 的回调
        var pairRequest = {
            playerNum: GlobalBean_1.GlobalBean.GetInstance().players,
            fee: GlobalBean_1.GlobalBean.GetInstance().ticketsNum,
        };
        //发送请求开始游戏的消息
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(msgId, pairRequest);
    };
    //设置是大厅 还是匹配页面
    HallPageController.prototype.setHallOrMatch = function (hallOrMatch) {
        if (this.hallOrMatch === hallOrMatch) {
            return;
        }
        this.hallOrMatch = hallOrMatch;
        this.hallParentNode.active = false;
        this.matchParentNode.active = false;
        switch (hallOrMatch) {
            case HallOrMatch.HALL_PARENT:
                this.hallParentNode.active = true;
                break;
            case HallOrMatch.MATCH_PARENT:
                this.matchParentNode.active = true;
                break;
        }
    };
    HallPageController.prototype.LoginSuccess = function () {
        //登录成功后 执行的操作
        GameMgr_1.GameMgr.Console.Log("登录成功");
        var loginData = GlobalBean_1.GlobalBean.GetInstance().loginData;
        if (loginData) {
            if (loginData.roomId > 0) { //正在游戏中
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeEnterRoom, {}); //重连进来的 玩家请求进入房间
            }
            if (loginData.inviteCode > 0) { //正在私人房间
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeAcceptInvite, { 'inviteCode': Number(loginData.inviteCode) });
            }
            else {
                //房间已经解散了  但是我还留在私人房间
                this.hallParentController.exitTheRoom();
            }
            //重连的时候 被遗留在匹配页面的话 就回到大厅
            if (loginData.roomId == 0 && loginData.inviteCode == 0 && this.hallOrMatch == HallOrMatch.MATCH_PARENT) {
                this.setHallOrMatch(HallOrMatch.HALL_PARENT);
            }
        }
        this.setFees();
        // 登录成功后请求关卡进度
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelProgress, {});
    };
    //设置接受邀请成功
    HallPageController.prototype.setAcceptInvite = function (acceptInvite) {
        this.hallParentController.setAcceptInvite(acceptInvite);
    };
    //离开房间
    HallPageController.prototype.leaveRoom = function (noticeLeaveInvite) {
        this.hallParentController.leaveRoom(noticeLeaveInvite);
    };
    //设置门票
    HallPageController.prototype.setFees = function () {
        this.hallParentController.setFees();
    };
    //初始化 match 页面
    HallPageController.prototype.createMatchView = function () {
        this.matchParentController.createMatchView();
    };
    //设置匹配数据
    HallPageController.prototype.setGameData = function () {
        this.matchParentController.setGameData();
    };
    //进入私人房间
    HallPageController.prototype.joinCreateRoom = function () {
        this.hallParentController.joinCreateRoom();
    };
    //房间号无效
    HallPageController.prototype.joinError = function () {
        this.hallParentController.joinError();
    };
    //准备 取消准备
    HallPageController.prototype.setReadyState = function (noticeUserInviteStatus) {
        if (this.hallParentController) {
            this.hallParentController.setReadyState(noticeUserInviteStatus);
        }
    };
    /**
     * 设置关卡进度（从后端获取数据后调用）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    HallPageController.prototype.setLevelProgress = function (levelProgressData) {
        if (this.levelSelectPageController) {
            this.levelSelectPageController.setLevelProgress(levelProgressData);
        }
    };
    __decorate([
        property(cc.Node)
    ], HallPageController.prototype, "hallParentNode", void 0);
    __decorate([
        property(cc.Node)
    ], HallPageController.prototype, "matchParentNode", void 0);
    __decorate([
        property(InfoDialogController_1.default)
    ], HallPageController.prototype, "infoDialogController", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], HallPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(SettingDialogController_1.default)
    ], HallPageController.prototype, "settingDialogController", void 0);
    __decorate([
        property(KickOutDialogController_1.default)
    ], HallPageController.prototype, "kickOutDialogController", void 0);
    __decorate([
        property(ToastController_1.default)
    ], HallPageController.prototype, "toastController", void 0);
    __decorate([
        property(LevelSelectPageController_1.default)
    ], HallPageController.prototype, "levelSelectPageController", void 0);
    HallPageController = __decorate([
        ccclass
    ], HallPageController);
    return HallPageController;
}(cc.Component));
exports.default = HallPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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