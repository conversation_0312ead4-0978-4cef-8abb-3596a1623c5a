
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/HallAutoController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fc7bflryAdCHaxASp4dUNIP', 'HallAutoController');
// scripts/hall/HallAutoController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoOrRoom = void 0;
var GlobalBean_1 = require("../bean/GlobalBean");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var AutoOrRoom;
(function (AutoOrRoom) {
    AutoOrRoom[AutoOrRoom["AUTO"] = 0] = "AUTO";
    AutoOrRoom[AutoOrRoom["ROOM"] = 1] = "ROOM";
})(AutoOrRoom = exports.AutoOrRoom || (exports.AutoOrRoom = {}));
var HallAutoController = /** @class */ (function (_super) {
    __extends(HallAutoController, _super);
    function HallAutoController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.autoLay = null;
        _this.roomLay = null;
        _this.boardTicketLay = null;
        _this.startButton = null;
        _this.createButton = null;
        _this.joinButton = null;
        _this.boardTabAuto = null;
        _this.boardTabRoom = null;
        _this.boardTicketBtnMinus = null; //门票➖
        _this.boardTicketBtnPlus = null; //门票➕
        _this.ticketNumber = null; //门票价格
        _this.autoOrRoom = null;
        _this.startClick = null;
        _this.createClick = null;
        _this.joinClick = null;
        _this.autoFeesPosition = 0;
        _this.createPosition = 0;
        return _this;
        // update (dt) {}
    }
    HallAutoController.prototype.onLoad = function () {
        this.boardTabAuto = this.autoLay.getChildByName('board_tab_02');
        this.boardTabRoom = this.roomLay.getChildByName('board_tab_02');
        this.boardTicketBtnMinus = this.boardTicketLay.getChildByName('board_ticket_btn_minus_normal');
        this.boardTicketBtnPlus = this.boardTicketLay.getChildByName('board_ticket_btn_plus_normal');
        this.ticketNumber = this.boardTicketLay.getChildByName('ticket_number').getComponent(cc.Label);
    };
    //设置游戏开始数据
    HallAutoController.prototype.setFees = function () {
        var roomConfig = GlobalBean_1.GlobalBean.GetInstance().loginData.roomConfigs;
        //房间类型 1-普通场 2-私人场
        for (var i = 0; i < roomConfig.length; i++) {
            if (roomConfig[i].id == 1) {
                this.autoRoomConfig = roomConfig[i];
            }
            if (roomConfig[i].id == 2) {
                this.createRoomConfig = roomConfig[i];
            }
        }
        this.setAutoOrRoom(AutoOrRoom.AUTO); //设置初始值
    };
    HallAutoController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.setTouchEvent(this.autoLay, function () {
            _this.setAutoOrRoom(AutoOrRoom.AUTO);
        });
        Tools_1.Tools.setTouchEvent(this.roomLay, function () {
            _this.setAutoOrRoom(AutoOrRoom.ROOM);
        });
        //start 按钮点击事件
        Tools_1.Tools.greenButton(this.startButton, function () {
            if (_this.startClick) {
                _this.startClick();
            }
        });
        //create 按钮点击事件
        Tools_1.Tools.greenButton(this.createButton, function () {
            if (_this.createClick) {
                _this.createClick();
            }
        });
        //join 按钮点击事件
        Tools_1.Tools.yellowButton(this.joinButton, function () {
            if (_this.joinClick) {
                _this.joinClick();
            }
        });
        //点击按钮减
        Tools_1.Tools.imageButtonClick(this.boardTicketBtnMinus, Config_1.Config.buttonRes + 'board_ticket_btn_minus_normal', Config_1.Config.buttonRes + 'board_ticket_btn_minus_pressed', function () {
            if (GlobalBean_1.GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {
                if (_this.autoFeesPosition === 0) {
                    return;
                }
                _this.autoFeesPosition--;
                _this.setTicketsNum(_this.autoRoomConfig, _this.autoFeesPosition);
            }
            else {
                if (_this.createPosition === 0) {
                    return;
                }
                _this.createPosition--;
                _this.setTicketsNum(_this.createRoomConfig, _this.createPosition);
            }
        });
        //点击按钮加
        Tools_1.Tools.imageButtonClick(this.boardTicketBtnPlus, Config_1.Config.buttonRes + 'board_ticket_btn_plus_normal', Config_1.Config.buttonRes + 'board_ticket_btn_plus_pressed', function () {
            if (GlobalBean_1.GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {
                if (_this.autoFeesPosition === _this.autoRoomConfig.fees.length - 1) {
                    return;
                }
                _this.autoFeesPosition++;
                _this.setTicketsNum(_this.autoRoomConfig, _this.autoFeesPosition);
            }
            else {
                if (_this.createPosition === _this.createRoomConfig.fees.length - 1) {
                    return;
                }
                _this.createPosition++;
                _this.setTicketsNum(_this.createRoomConfig, _this.createPosition);
            }
        });
    };
    //赋值门票价格
    HallAutoController.prototype.setTicketsNum = function (roomConfig, position) {
        var fees = roomConfig.fees[position];
        this.ticketNumber.string = fees === 0 ? window.getLocalizedStr('free') : fees + '';
        GlobalBean_1.GlobalBean.GetInstance().ticketsNum = fees;
    };
    //设置展示 auto 还是 room 的view
    HallAutoController.prototype.setAutoOrRoom = function (autoOrRoom) {
        if (this.autoOrRoom === autoOrRoom) {
            return;
        }
        this.autoOrRoom = autoOrRoom;
        GlobalBean_1.GlobalBean.GetInstance().autoAndRoom = autoOrRoom;
        this.boardTabAuto.active = false;
        this.boardTabRoom.active = false;
        this.startButton.active = false;
        this.createButton.active = false;
        this.joinButton.active = false;
        switch (autoOrRoom) {
            case AutoOrRoom.AUTO:
                this.boardTabAuto.active = true;
                this.startButton.active = true;
                this.createButton.active = false;
                this.joinButton.active = false;
                break;
            case AutoOrRoom.ROOM:
                this.boardTabRoom.active = true;
                this.startButton.active = false;
                this.createButton.active = true;
                this.joinButton.active = true;
                break;
        }
        if (GlobalBean_1.GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {
            if (this.autoRoomConfig) {
                this.setTicketsNum(this.autoRoomConfig, this.autoFeesPosition);
            }
            else {
                this.ticketNumber.string = window.getLocalizedStr('free');
            }
        }
        else {
            if (this.createRoomConfig) {
                this.setTicketsNum(this.createRoomConfig, this.createPosition);
            }
            else {
                this.ticketNumber.string = window.getLocalizedStr('free');
            }
        }
    };
    //设置点击按钮的回调
    HallAutoController.prototype.setButtonClick = function (startClick, createClick, joinClick) {
        this.startClick = startClick;
        this.createClick = createClick;
        this.joinClick = joinClick;
    };
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "autoLay", void 0);
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "roomLay", void 0);
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "boardTicketLay", void 0);
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "startButton", void 0);
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "createButton", void 0);
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "joinButton", void 0);
    HallAutoController = __decorate([
        ccclass
    ], HallAutoController);
    return HallAutoController;
}(cc.Component));
exports.default = HallAutoController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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