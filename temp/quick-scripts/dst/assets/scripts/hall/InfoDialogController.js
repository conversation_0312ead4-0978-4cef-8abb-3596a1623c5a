
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/InfoDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '46d58hnp4tLf7B+lcQOkRXG', 'InfoDialogController');
// scripts/hall/InfoDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//游戏道具介绍页面
var InfoDialogController = /** @class */ (function (_super) {
    __extends(InfoDialogController, _super);
    function InfoDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        // 新增：单机规则和联机规则文字按钮
        _this.danjiLabel = null;
        _this.lianjiLabel = null;
        // 新增：可移动的视觉效果按钮（不需要点击）
        _this.switchButton = null;
        // 新增：单机和联机规则的ScrollView
        _this.danjiScrollView = null;
        _this.duorenScrollView = null;
        _this.infoItem = null;
        _this.infoItem1 = null;
        _this.infoImage1 = null;
        _this.infoImage2 = null;
        _this.infoImage3 = null;
        _this.infoImage4 = null;
        _this.infoImage5 = null;
        _this.infoImage6 = null;
        _this.infoImage7 = null;
        _this.infoImage8 = null;
        _this.infoImage9 = null;
        _this.infoImage10 = null;
        _this.infoImage11 = null;
        _this.titleList = []; //title 的列表
        _this.tipsList = [];
        _this.generationMethodList = [];
        _this.permanentList = [];
        _this.randomList = [];
        _this.chainsList = [];
        _this.iceList = [];
        _this.scoringDetails = [];
        _this.infoImageList = [];
        _this.backCallback = null; //隐藏弹窗的回调
        // 新增：当前选中的规则类型（0: 单机规则, 1: 联机规则）
        _this.currentRuleType = 0;
        // 新增：动画持续时间
        _this.animationDuration = 0.3;
        return _this;
        // update (dt) {}
    }
    InfoDialogController.prototype.onLoad = function () {
        //     this.infoImageList = [
        //         this.infoImage3,
        //         this.infoImage4,
        //         this.infoImage5,
        //         this.infoImage6,
        //         this.infoImage7,
        //         this.infoImage8,
        //         this.infoImage9,
        //         this.infoImage10,
        //         this.infoImage11,
        //     ]
        //     this.titleList = [
        //         window.getLocalizedStr('Tips'),
        //         window.getLocalizedStr('Generation_Method'),
        //         window.getLocalizedStr('Permanent_Task'),
        //         window.getLocalizedStr('Random_Task'),
        //         window.getLocalizedStr('Chains'),
        //         window.getLocalizedStr('Ice_Blocks'),
        //         window.getLocalizedStr('Scoring_Details'),
        //     ]//title 的列表
        //     this. tipsList = [
        //         window.getLocalizedStr('Tips1'),
        //         window.getLocalizedStr('Tips2'),
        //         window.getLocalizedStr('Tips3'),
        //         window.getLocalizedStr('Tips4'),
        //         window.getLocalizedStr('Tips5'),
        //     ]
        //     this.generationMethodList = [
        //         window.getLocalizedStr('Generation_Method1'),
        //         window.getLocalizedStr('Generation_Method2'),
        //         window.getLocalizedStr('Generation_Method3'),
        //         window.getLocalizedStr('Generation_Method4'),
        //         window.getLocalizedStr('Generation_Method5'),
        //         window.getLocalizedStr('Generation_Method6'),
        //         window.getLocalizedStr('Generation_Method7'),
        //         window.getLocalizedStr('Generation_Method8'),
        //         window.getLocalizedStr('Generation_Method9'),
        //     ]
        //     this.permanentList = [
        //         window.getLocalizedStr('Permanent_Task1'),
        //     ]
        //     this.randomList = [
        //         window.getLocalizedStr('Random_Task1'),
        //         window.getLocalizedStr('Random_Task2'),
        //         window.getLocalizedStr('Random_Task3'),
        //         window.getLocalizedStr('Random_Task4'),
        //         window.getLocalizedStr('Random_Task5'),
        //         window.getLocalizedStr('Random_Task6'),
        //         window.getLocalizedStr('Random_Task7'),
        //     ]
        //     this.chainsList = [
        //         window.getLocalizedStr('Chains1'),
        //         window.getLocalizedStr('Chains2'),
        //         window.getLocalizedStr('Chains3'),
        //     ]
        //     this.iceList = [
        //         window.getLocalizedStr('Ice_Blocks1'),
        //         window.getLocalizedStr('Ice_Blocks2'),
        //         window.getLocalizedStr('Ice_Blocks3'),
        //     ]
        //     this.scoringDetails = [
        //         window.getLocalizedStr('Scoring_Details1'),
        //         window.getLocalizedStr('Scoring_Details2'),
        //         window.getLocalizedStr('Scoring_Details3'),
        //         window.getLocalizedStr('Scoring_Details4'),
        //         window.getLocalizedStr('Scoring_Details5'),
        //         window.getLocalizedStr('Scoring_Details6'),
        //         window.getLocalizedStr('Scoring_Details7'),
        //         window.getLocalizedStr('Scoring_Details8'),
        //         window.getLocalizedStr('Scoring_Details9'),
        //         window.getLocalizedStr('Scoring_Details10'),
        //         window.getLocalizedStr('Scoring_Details11'),
        //     ]
    };
    InfoDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        // 新增：设置单机规则文字点击事件
        if (this.danjiLabel) {
            Tools_1.Tools.setTouchEvent(this.danjiLabel, function () {
                _this.switchToRuleType(0); // 切换到单机规则
            });
        }
        // 新增：设置联机规则文字点击事件
        if (this.lianjiLabel) {
            Tools_1.Tools.setTouchEvent(this.lianjiLabel, function () {
                _this.switchToRuleType(1); // 切换到联机规则
            });
        }
        // 新增：初始化显示状态（默认显示单机规则）
        this.initializeRuleDisplay();
        //     this.contentLay.removeAllChildren()
        //     this.getTitleNode(this.titleList[0])
        //     this.tipsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[1])
        //     this.generationMethodList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         let infoImg = cc.instantiate(this.infoImageList[index])
        //         infoItemOneController.setimgNode(infoImg)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[2])
        //     this.permanentList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg1 = cc.instantiate(this.infoImage1)
        //     this.contentLay.addChild(infoImg1)
        //     this.getTitleNode(this.titleList[3])
        //     this.randomList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg2 = cc.instantiate(this.infoImage2)
        //     this.contentLay.addChild(infoImg2)
        //     this.getTitleNode(this.titleList[4])
        //     this.chainsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[5])
        //     this.iceList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[6])
        //     this.scoringDetails.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        // }
        // getTitleNode(title: string) {
        //     let infoItem = cc.instantiate(this.infoItem);//初始化一个预制体
        //     let infoItemController = infoItem.getComponent(InfoItemController)
        //     infoItemController.setContent(title)
        //     this.contentLay.addChild(infoItem)
        // }
    };
    /**
     * 新增：初始化规则显示状态
     */
    InfoDialogController.prototype.initializeRuleDisplay = function () {
        if (this.danjiScrollView && this.duorenScrollView) {
            // 默认显示单机规则，隐藏联机规则
            this.danjiScrollView.active = true;
            this.duorenScrollView.active = false;
            this.currentRuleType = 0;
            // 设置按钮初始位置（左边位置）
            if (this.switchButton) {
                this.switchButton.position = cc.v3(-150, -2, 0);
            }
        }
    };
    /**
     * 新增：切换到指定规则类型
     * @param ruleType 0: 单机规则, 1: 联机规则
     */
    InfoDialogController.prototype.switchToRuleType = function (ruleType) {
        if (this.currentRuleType === ruleType) {
            console.log("已经是当前规则类型，无需切换");
            return; // 如果已经是当前类型，不需要切换
        }
        this.currentRuleType = ruleType;
        console.log("\u5207\u6362\u5230: " + (this.currentRuleType === 0 ? '单机规则' : '联机规则'));
        // 移动按钮位置（视觉效果）
        this.moveButtonToPosition();
        // 切换ScrollView显示
        this.switchScrollViewDisplay();
    };
    /**
     * 新增：移动按钮到指定位置
     */
    InfoDialogController.prototype.moveButtonToPosition = function () {
        if (!this.switchButton) {
            return;
        }
        // 按钮位置：左边（-150，-2）右边（142，-2）
        var leftPosition = cc.v3(-150, -2, 0);
        var rightPosition = cc.v3(142, -2, 0);
        var targetPosition = this.currentRuleType === 0 ? leftPosition : rightPosition;
        // 使用动画移动按钮
        cc.tween(this.switchButton)
            .to(0.3, { position: targetPosition }, { easing: 'quartOut' })
            .start();
    };
    /**
     * 新增：切换ScrollView显示
     */
    InfoDialogController.prototype.switchScrollViewDisplay = function () {
        if (!this.danjiScrollView || !this.duorenScrollView) {
            return;
        }
        if (this.currentRuleType === 0) {
            // 显示单机规则
            this.danjiScrollView.active = true;
            this.duorenScrollView.active = false;
        }
        else {
            // 显示联机规则
            this.danjiScrollView.active = false;
            this.duorenScrollView.active = true;
        }
    };
    InfoDialogController.prototype.show = function (backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    InfoDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "danjiLabel", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "lianjiLabel", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "switchButton", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "danjiScrollView", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "duorenScrollView", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage2", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage3", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage4", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage5", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage6", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage7", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage8", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage9", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage10", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage11", void 0);
    InfoDialogController = __decorate([
        ccclass
    ], InfoDialogController);
    return InfoDialogController;
}(cc.Component));
exports.default = InfoDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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