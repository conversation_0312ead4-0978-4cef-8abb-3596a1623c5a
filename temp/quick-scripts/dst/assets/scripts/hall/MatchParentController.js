
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/MatchParentController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7a33ccGFZVHdIRH0OyFmGFV', 'MatchParentController');
// scripts/hall/MatchParentController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Publish_1 = require("../../meshTools/tools/Publish");
var GlobalBean_1 = require("../bean/GlobalBean");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var MessageBaseBean_1 = require("../net/MessageBaseBean");
var MatchItemController_1 = require("../pfb/MatchItemController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var MatchParentController = /** @class */ (function (_super) {
    __extends(MatchParentController, _super);
    function MatchParentController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.backBtn = null;
        _this.matchUserLay = null;
        _this.matchUserLay2 = null;
        _this.matchItem = null;
        _this._userListCol = []; //显示用户布局的controller
        _this.normalTime = function () { };
        return _this;
        // update (dt) {}
    }
    MatchParentController.prototype.onLoad = function () {
        if (cc.sys.os === cc.sys.OS_IOS) {
            // 在iOS设备上执行的代码
            if (Publish_1.Publish.GetInstance().gameMode != '2') { //非半屏的话
                this.backBtn.getComponent(cc.Widget).top = 146;
            }
        }
        else {
            // 在其他设备上执行的代码
        }
    };
    MatchParentController.prototype.onEnable = function () {
        this.backBtn.active = true;
    };
    MatchParentController.prototype.start = function () {
        var _this = this;
        //设置返回键的点击事件
        Tools_1.Tools.imageButtonClick(this.backBtn, Config_1.Config.buttonRes + 'board_btn_back_normal', Config_1.Config.buttonRes + 'board_btn_back_pressed', function () {
            if (_this.backClick) {
                _this.backClick();
            }
        });
    };
    MatchParentController.prototype.createMatchView = function () {
        var user_s = [];
        //判断是几人游戏
        var peopleNumber = GlobalBean_1.GlobalBean.GetInstance().players;
        var userInfo = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo;
        for (var i = 0; i < peopleNumber; i++) {
            if (i == 0) {
                var user = {
                    userId: userInfo.userId,
                    nickName: userInfo.nickname,
                    avatar: userInfo.avatar,
                    pos: 0,
                    coin: 0,
                    status: 0,
                    score: 0,
                    rank: 0
                };
                user_s.push(user);
            }
            else {
                user_s.push(null);
            }
        }
        //这里是添加一个等待的页面匹配的占位
        this.matchUserLay.removeAllChildren();
        this.matchUserLay2.removeAllChildren();
        this._userListCol = [];
        if (user_s.length <= 4) {
            for (var i = 0; i < user_s.length; i++) {
                var item = cc.instantiate(this.matchItem);
                this.matchUserLay.addChild(item);
                var matchingItemController = item.getComponent(MatchItemController_1.default);
                this._userListCol.push(matchingItemController);
                matchingItemController.setData(user_s[i]);
            }
        }
        else {
            var arrayData = Tools_1.Tools.chunkArray(user_s, 3); //根据人数进行分块
            for (var i = 0; i < arrayData[0].length; i++) {
                var item = cc.instantiate(this.matchItem);
                this.matchUserLay.addChild(item);
                var matchingItemController = item.getComponent(MatchItemController_1.default);
                this._userListCol.push(matchingItemController);
                matchingItemController.setData(user_s[i]);
            }
            for (var i = 0; i < arrayData[1].length; i++) {
                var item = cc.instantiate(this.matchItem);
                this.matchUserLay2.addChild(item);
                var matchingItemController = item.getComponent(MatchItemController_1.default);
                this._userListCol.push(matchingItemController);
                matchingItemController.setData(user_s[3 + i]);
            }
        }
    };
    //设置游戏数据
    MatchParentController.prototype.setGameData = function () {
        this.backBtn.active = false; //匹配成功之后 隐藏掉返回键
        var user = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        var index = user.findIndex(function (item) { return item.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId; }); //搜索
        GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin = user[index].coin; //更新自己的最新金币
        //这里是匹配成功之后的真实数据
        for (var i = 0; i < user.length; i++) {
            if (i <= this._userListCol.length) {
                this._userListCol[i].setData(user[i]);
            }
        }
        this.normalTime = function () {
            var autoMessageBean = {
                'msgId': MessageBaseBean_1.AutoMessageId.SwitchGameSceneMsg,
                'data': {}
            };
            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean); //进入游戏的消息
        };
        this.scheduleOnce(this.normalTime, 1);
    };
    MatchParentController.prototype.onDisable = function () {
        this.unschedule(this.normalTime);
    };
    MatchParentController.prototype.setClick = function (backClick) {
        this.backClick = backClick;
    };
    __decorate([
        property(cc.Node)
    ], MatchParentController.prototype, "backBtn", void 0);
    __decorate([
        property(cc.Node)
    ], MatchParentController.prototype, "matchUserLay", void 0);
    __decorate([
        property(cc.Node)
    ], MatchParentController.prototype, "matchUserLay2", void 0);
    __decorate([
        property(cc.Prefab)
    ], MatchParentController.prototype, "matchItem", void 0);
    MatchParentController = __decorate([
        ccclass
    ], MatchParentController);
    return MatchParentController;
}(cc.Component));
exports.default = MatchParentController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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