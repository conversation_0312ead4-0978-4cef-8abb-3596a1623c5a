
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '40c9e3ykUFClLHG7s1cQEKm', 'LevelSelectController');
// scripts/hall/Level/LevelSelectController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelStatus = void 0;
var ScrollViewHelper_1 = require("./ScrollViewHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 关卡状态枚举
var LevelStatus;
(function (LevelStatus) {
    LevelStatus[LevelStatus["LOCKED"] = 0] = "LOCKED";
    LevelStatus[LevelStatus["CURRENT"] = 1] = "CURRENT";
    LevelStatus[LevelStatus["COMPLETED"] = 2] = "COMPLETED"; // 已通关（绿色）
})(LevelStatus = exports.LevelStatus || (exports.LevelStatus = {}));
var LevelSelectController = /** @class */ (function (_super) {
    __extends(LevelSelectController, _super);
    function LevelSelectController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scrollView = null;
        _this.content = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 30;
        _this.screenWidth = 650;
        _this.levelItemWidth = 150; // 关卡项的宽度（包括间距）
        _this.visibleLevels = 3; // 可见关卡数量
        // 连接线配置（公开，方便调试）
        _this.lineCount = 9; // 每两个关卡之间的连接线数量
        _this.lineSpacing = 16; // 连接线之间的间距
        _this.levelToLineDistance = 8; // 关卡到连接线的距离
        // 关卡节点列表
        _this.levelNodes = [];
        // 滑动状态标志
        _this.isAutoScrolling = false;
        // 关卡选择变化回调
        _this.onLevelSelectionChanged = null;
        return _this;
    }
    LevelSelectController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        this.initLevelData();
        this.createLevelItems();
        this.updateLevelDisplay();
        this.setupScrollEvents();
        // 设置初始滚动位置为第1关（最左边）
        if (this.scrollView) {
            this.scrollView.scrollToPercentHorizontal(0, 0);
        }
    };
    LevelSelectController.prototype.start = function () {
        var _this = this;
        // 延迟滚动到当前选中关卡，确保界面完全初始化
        this.scheduleOnce(function () {
            // 滚动到当前选中的关卡（可能已经通过ExtendLevelProgress设置了）
            cc.log("LevelSelectController start() - \u6EDA\u52A8\u5230\u5173\u5361 " + _this.currentSelectedLevel);
            _this.scrollToLevel(_this.currentSelectedLevel);
        }, 0.1);
    };
    LevelSelectController.prototype.onDestroy = function () {
        // 移除事件监听器
        if (this.scrollView) {
            this.scrollView.node.off('scrolling', this.onScrolling, this);
            this.scrollView.node.off('scroll-ended', this.onScrollEnded, this);
        }
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectController.prototype.initLevelData = function () {
        this.levelDataList = [];
        // 初始化关卡状态：第1关为当前可玩关卡，其他为锁定状态
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelStatus.CURRENT; // 第1关默认为当前可玩关卡（显示开始游戏按钮）
            }
            else {
                status = LevelStatus.LOCKED; // 其他关卡锁定
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
        // 默认选中第一关
        this.currentSelectedLevel = 1;
    };
    /**
     * 创建关卡项目
     */
    LevelSelectController.prototype.createLevelItems = function () {
        if (!this.content) {
            cc.error("Content node is not assigned!");
            return;
        }
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + this.screenWidth;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + this.screenWidth / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                this.createConnectionLines(i, posX);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectController.prototype.createLevelNode = function (levelData) {
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        var sprite = node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        // 设置字体样式
        label.fontSize = 30;
        label.node.color = cc.color(255, 255, 255); // #FFFFFF
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 添加外边框
        var outline = label.addComponent(cc.LabelOutline);
        this.updateLabelOutline(outline, levelData.status);
        labelNode.parent = node;
        labelNode.setPosition(0, 0); // 居中对齐
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        var eventHandler = new cc.Component.EventHandler();
        eventHandler.target = this.node;
        eventHandler.component = "LevelSelectController";
        eventHandler.handler = "onLevelClicked";
        eventHandler.customEventData = levelData.levelNumber.toString();
        button.clickEvents.push(eventHandler);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线组（9个连接线）
     */
    LevelSelectController.prototype.createConnectionLines = function (levelIndex, levelPosX) {
        var startX = levelPosX + 46 / 2 + this.levelToLineDistance; // 关卡右边缘 + 距离
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        var endX = levelPosX + this.levelItemWidth - 46 / 2 - this.levelToLineDistance; // 下一个关卡左边缘 - 距离
        var availableWidth = endX - startX;
        // 如果可用宽度小于需要的宽度，调整间距
        var actualSpacing = this.lineSpacing;
        if (totalLineWidth > availableWidth) {
            actualSpacing = availableWidth / (this.lineCount - 1);
        }
        for (var i = 0; i < this.lineCount; i++) {
            var lineNode = this.createSingleLineNode();
            this.content.addChild(lineNode);
            var lineX = startX + i * actualSpacing;
            lineNode.setPosition(lineX, 0);
        }
    };
    /**
     * 创建单个连接线节点
     */
    LevelSelectController.prototype.createSingleLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        // 设置连接线大小为6*6
        node.setContentSize(6, 6);
        // 加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后重新设置大小为6x6，确保不被图片原始大小覆盖
                node.setContentSize(6, 6);
            }
        });
        return node;
    };
    /**
     * 检查是否为特殊关卡（第5、10、15、20、25关）
     */
    LevelSelectController.prototype.isSpecialLevel = function (levelNumber) {
        return levelNumber % 5 === 0;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectController.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var sprite = node.getComponent(cc.Sprite);
        if (!sprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 检查是否为特殊关卡（第5、10、15、20、25关）
        var isSpecialLevel = this.isSpecialLevel(levelData.levelNumber);
        var uiSuffix = isSpecialLevel ? "01" : "";
        // 根据状态和是否选中确定图片路径
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix + "_choose";
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix + "_choose";
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix + "_choose";
                    break;
            }
        }
        else {
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix;
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix;
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix;
                    break;
            }
        }
        // 设置节点大小
        node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后强制设置正确的大小，覆盖图片原始大小
                node.setContentSize(size);
            }
        });
        // 更新标签外边框
        var labelNode = node.getChildByName("LevelLabel");
        if (labelNode) {
            var label = labelNode.getComponent(cc.Label);
            if (label) {
                var outline = label.getComponent(cc.LabelOutline);
                if (outline) {
                    this.updateLabelOutline(outline, levelData.status);
                }
            }
        }
    };
    /**
     * 更新关卡显示
     */
    LevelSelectController.prototype.updateLevelDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectController.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        // 设置自动滚动标志，防止滚动过程中触发位置更新
        this.isAutoScrolling = true;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        // 计算目标位置的偏移量
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        // 限制偏移量在有效范围内
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        // 设置滚动位置
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectController.prototype.onLevelClicked = function (event, customEventData) {
        var levelNumber = parseInt(customEventData);
        // 允许选择任何关卡（包括未解锁的）
        // 更新当前选中关卡
        this.currentSelectedLevel = levelNumber;
        this.updateLevelDisplay();
        // 滚动到选中关卡
        this.isAutoScrolling = true;
        this.scrollToLevel(levelNumber);
        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            this.onLevelSelectionChanged(levelNumber);
        }
        // 这里可以添加进入关卡的逻辑
        // this.enterLevel(levelNumber);
    };
    /**
     * 设置关卡状态
     */
    LevelSelectController.prototype.setLevelStatus = function (levelNumber, status) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        this.levelDataList[levelNumber - 1].status = status;
        this.updateLevelDisplay();
    };
    /**
     * 获取当前选中的关卡
     */
    LevelSelectController.prototype.getCurrentSelectedLevel = function () {
        return this.currentSelectedLevel;
    };
    /**
     * 获取指定关卡的数据
     */
    LevelSelectController.prototype.getLevelData = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return null;
        return this.levelDataList[levelNumber - 1];
    };
    /**
     * 设置关卡进度（从外部调用，比如从后端获取数据后）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    LevelSelectController.prototype.setLevelProgress = function (levelProgressData) {
        cc.log("\uD83D\uDCE8 LevelSelectController.setLevelProgress() \u88AB\u8C03\u7528");
        cc.log("   - \u63A5\u6536\u5230\u7684\u6570\u636E:", levelProgressData);
        if (!levelProgressData) {
            cc.warn("❌ levelProgressData 为空");
            return;
        }
        var clearedLevels = levelProgressData.clearedLevels, currentLevel = levelProgressData.currentLevel, totalLevels = levelProgressData.totalLevels;
        cc.log("   - clearedLevels: " + clearedLevels + ", currentLevel: " + currentLevel + ", totalLevels: " + totalLevels);
        // 验证数据有效性
        if (clearedLevels < 0 || currentLevel < 1 || currentLevel > totalLevels) {
            cc.warn("\u274C \u6570\u636E\u9A8C\u8BC1\u5931\u8D25: clearedLevels=" + clearedLevels + ", currentLevel=" + currentLevel + ", totalLevels=" + totalLevels);
            return;
        }
        // 更新总关卡数（如果后端传了的话）
        if (totalLevels && totalLevels > 0) {
            this.totalLevels = totalLevels;
        }
        // 重新设置所有关卡状态
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i < currentLevel) {
                status = LevelStatus.COMPLETED; // 当前关卡之前的都是已完成（绿色）
            }
            else if (i === currentLevel) {
                status = LevelStatus.CURRENT; // 当前关卡（黄色选择UI）
            }
            else {
                status = LevelStatus.LOCKED; // 当前关卡之后的都是未解锁（灰色）
            }
            this.levelDataList[i - 1].status = status;
        }
        // 更新当前选中关卡为后端指定的currentLevel
        cc.log("\uD83C\uDFAF \u8BBE\u7F6E\u5F53\u524D\u9009\u4E2D\u5173\u5361: " + this.currentSelectedLevel + " -> " + currentLevel);
        this.currentSelectedLevel = currentLevel;
        this.updateLevelDisplay();
        cc.log("\uD83D\uDCDC \u6EDA\u52A8\u5230\u5173\u5361 " + currentLevel);
        // scrollToLevel 方法内部会设置 isAutoScrolling = true
        this.scrollToLevel(currentLevel);
        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            cc.log("\uD83D\uDD14 \u901A\u77E5\u5173\u5361\u9009\u62E9\u53D8\u5316: " + currentLevel);
            this.onLevelSelectionChanged(currentLevel);
        }
    };
    /**
     * 设置关卡进度（兼容旧接口）
     * @param completedLevels 已完成的关卡数
     */
    LevelSelectController.prototype.setLevelProgressLegacy = function (completedLevels) {
        // 转换为新的数据格式
        var levelProgressData = {
            clearedLevels: completedLevels,
            currentLevel: Math.min(completedLevels + 1, this.totalLevels),
            totalLevels: this.totalLevels
        };
        this.setLevelProgress(levelProgressData);
    };
    /**
     * 解锁下一关
     */
    LevelSelectController.prototype.unlockNextLevel = function () {
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (this.levelDataList[i].status === LevelStatus.LOCKED) {
                this.levelDataList[i].status = LevelStatus.CURRENT;
                this.updateLevelDisplay();
                break;
            }
        }
    };
    /**
     * 完成当前关卡
     */
    LevelSelectController.prototype.completeCurrentLevel = function () {
        var currentLevelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (currentLevelData.status === LevelStatus.CURRENT) {
            currentLevelData.status = LevelStatus.COMPLETED;
            this.unlockNextLevel();
            this.updateLevelDisplay();
        }
    };
    /**
     * 设置滑动事件监听
     */
    LevelSelectController.prototype.setupScrollEvents = function () {
        if (!this.scrollView)
            return;
        // 监听滑动中事件
        this.scrollView.node.on('scrolling', this.onScrolling, this);
        // 监听滑动结束事件
        this.scrollView.node.on('scroll-ended', this.onScrollEnded, this);
    };
    /**
     * 滑动中事件处理
     */
    LevelSelectController.prototype.onScrolling = function () {
        // 如果是自动滚动，不要更新选中关卡
        if (this.isAutoScrolling) {
            cc.log("\u23F8\uFE0F onScrolling() - \u8DF3\u8FC7\uFF0C\u56E0\u4E3A\u6B63\u5728\u81EA\u52A8\u6EDA\u52A8");
            return;
        }
        cc.log("\uD83D\uDC46 onScrolling() - \u7528\u6237\u624B\u52A8\u6EDA\u52A8\uFF0C\u66F4\u65B0\u9009\u4E2D\u5173\u5361");
        this.updateSelectedLevelByPosition();
    };
    /**
     * 滑动结束事件处理
     */
    LevelSelectController.prototype.onScrollEnded = function () {
        // 如果是自动滚动触发的事件，忽略
        if (this.isAutoScrolling) {
            this.isAutoScrolling = false;
            return;
        }
        this.updateSelectedLevelByPosition();
        // 滑动结束后，将选中的关卡固定居中
        this.isAutoScrolling = true;
        this.scrollToLevel(this.currentSelectedLevel);
    };
    /**
     * 根据当前位置更新选中的关卡
     */
    LevelSelectController.prototype.updateSelectedLevelByPosition = function () {
        if (!this.scrollView || !this.content)
            return;
        // 获取ScrollView的中心位置（相对于ScrollView节点）
        var scrollViewCenterX = 0; // ScrollView的中心就是x=0
        // 找到最接近ScrollView中心位置的关卡
        var closestLevel = 1;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.levelNodes.length; i++) {
            var levelNode = this.levelNodes[i];
            // 将关卡节点的本地坐标转换为ScrollView坐标系
            var levelWorldPos = this.content.convertToWorldSpaceAR(levelNode.position);
            var levelScrollViewPos = this.scrollView.node.convertToNodeSpaceAR(levelWorldPos);
            // 计算关卡与ScrollView中心的距离
            var distance = Math.abs(levelScrollViewPos.x - scrollViewCenterX);
            if (distance < minDistance) {
                minDistance = distance;
                closestLevel = i + 1;
            }
        }
        // 如果选中的关卡发生变化，更新显示
        if (closestLevel !== this.currentSelectedLevel) {
            cc.log("\uD83D\uDD04 updateSelectedLevelByPosition: " + this.currentSelectedLevel + " -> " + closestLevel);
            this.currentSelectedLevel = closestLevel;
            this.updateLevelDisplay();
            // 通知关卡选择变化
            if (this.onLevelSelectionChanged) {
                this.onLevelSelectionChanged(closestLevel);
            }
        }
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectController.prototype.fixScrollViewScrollbar = function () {
        if (this.scrollView && this.content) {
            ScrollViewHelper_1.ScrollViewHelper.setupHorizontalScrollView(this.scrollView, this.content);
            ScrollViewHelper_1.ScrollViewHelper.debugScrollView(this.scrollView, "LevelSelectController");
        }
    };
    /**
     * 调试参数信息
     */
    LevelSelectController.prototype.debugParameters = function () {
        // 计算连接线总宽度
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        // 计算可用宽度
        var availableWidth = this.levelItemWidth - 46 - (this.levelToLineDistance * 2);
        if (totalLineWidth > availableWidth) {
            var adjustedSpacing = availableWidth / (this.lineCount - 1);
        }
        else {
        }
    };
    /**
     * 更新标签外边框
     */
    LevelSelectController.prototype.updateLabelOutline = function (outline, status) {
        var outlineColor;
        switch (status) {
            case LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }
        outline.color = outlineColor;
        outline.width = 1;
    };
    /**
     * 重新创建关卡项目（用于参数调整后刷新）
     */
    LevelSelectController.prototype.refreshLevelItems = function () {
        this.createLevelItems();
        this.updateLevelDisplay();
        this.scrollToLevel(this.currentSelectedLevel);
    };
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Node)
    ], LevelSelectController.prototype, "content", void 0);
    LevelSelectController = __decorate([
        ccclass
    ], LevelSelectController);
    return LevelSelectController;
}(cc.Component));
exports.default = LevelSelectController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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