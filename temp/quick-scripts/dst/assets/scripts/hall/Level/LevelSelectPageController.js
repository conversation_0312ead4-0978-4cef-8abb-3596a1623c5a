
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'eed91BOnAtLKYkInYtuL76o', 'LevelSelectPageController');
// scripts/hall/Level/LevelSelectPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalManagerController_1 = require("../../GlobalManagerController");
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelSelectPageController = /** @class */ (function (_super) {
    __extends(LevelSelectPageController, _super);
    function LevelSelectPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.levelSelectController = null;
        _this.scrollView = null;
        // 新增的游戏按钮
        _this.startGameButton = null;
        _this.lockedButton = null;
        return _this;
    }
    LevelSelectPageController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        // 设置按钮初始状态 - 默认显示开始游戏按钮，避免状态切换闪烁
        if (this.startGameButton) {
            this.startGameButton.node.active = true;
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = false;
            this.lockedButton.node.on('click', this.onLockedButtonClick, this);
        }
    };
    LevelSelectPageController.prototype.start = function () {
        var _this = this;
        // 设置关卡选择变化回调
        if (this.levelSelectController) {
            this.levelSelectController.onLevelSelectionChanged = function (levelNumber) {
                _this.onLevelSelectionChanged(levelNumber);
            };
        }
        // 延迟更新UI，确保在关卡数据加载后再更新按钮状态
        this.scheduleOnce(function () {
            _this.updateUIDisplay();
        }, 0.1);
    };
    /**
     * 更新UI显示状态
     */
    LevelSelectPageController.prototype.updateUIDisplay = function () {
        // 更新游戏按钮状态
        this.updateGameButtons();
    };
    /**
     * 进入选中的关卡
     */
    LevelSelectPageController.prototype.enterSelectedLevel = function () {
        if (this.levelSelectController) {
            var selectedLevel = this.levelSelectController.getCurrentSelectedLevel();
            cc.log("\u8FDB\u5165\u5173\u5361 " + selectedLevel);
            // 尝试多种方式获取全局管理器
            var globalManager = null;
            // 方法1: 尝试查找 global_node 节点（根目录）
            var globalManagerNode = cc.find("global_node");
            if (globalManagerNode) {
                globalManager = globalManagerNode.getComponent(GlobalManagerController_1.default);
                cc.log("✅ 找到 global_node 节点（根目录）");
            }
            else {
                cc.log("❌ 未找到 global_node 节点（根目录）");
            }
            // 方法1.1: 尝试查找 Canvas/global_node 节点
            if (!globalManager) {
                globalManagerNode = cc.find("Canvas/global_node");
                if (globalManagerNode) {
                    cc.log("✅ 找到 Canvas/global_node 节点");
                    globalManager = globalManagerNode.getComponent(GlobalManagerController_1.default);
                    if (globalManager) {
                        cc.log("✅ 成功获取 GlobalManagerController 组件");
                    }
                    else {
                        cc.log("❌ Canvas/global_node 节点上没有 GlobalManagerController 组件");
                        // 列出节点上的所有组件
                        var components = globalManagerNode.getComponents(cc.Component);
                        cc.log("节点上的组件列表：", components.map(function (comp) { return comp.constructor.name; }));
                    }
                }
                else {
                    cc.log("❌ 未找到 Canvas/global_node 节点");
                }
            }
            if (globalManager) {
                // 设置关卡页面控制器的当前关卡
                if (globalManager.levelPageController) {
                    globalManager.levelPageController.setCurrentLevel(selectedLevel);
                }
                // 切换到关卡页面
                globalManager.setCurrentPage(GlobalManagerController_1.PageType.LEVEL_PAGE);
            }
            else {
                cc.error("无法找到 GlobalManagerController 组件！请检查场景配置。");
                cc.error("请确保场景中有节点挂载了 GlobalManagerController 组件。");
                // 调试信息：列出场景中的所有根节点名称
                var scene = cc.director.getScene();
                if (scene) {
                    cc.log("场景中的根节点：");
                    for (var i = 0; i < scene.children.length; i++) {
                        cc.log("- " + scene.children[i].name);
                    }
                }
            }
        }
    };
    /**
     * 设置关卡进度（从外部调用）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    LevelSelectPageController.prototype.setLevelProgress = function (levelProgressData) {
        var _this = this;
        if (!this.levelSelectController)
            return;
        // 使用 LevelSelectController 的新方法来设置关卡进度
        this.levelSelectController.setLevelProgress(levelProgressData);
        // 立即更新UI显示，确保按钮状态正确
        this.scheduleOnce(function () {
            _this.updateUIDisplay();
        }, 0.05); // 很短的延迟，确保关卡数据已经更新
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectPageController.prototype.fixScrollViewScrollbar = function () {
        // 如果有ScrollView引用，清除Scrollbar引用以避免错误
        if (this.scrollView) {
            this.scrollView.horizontalScrollBar = null;
            this.scrollView.verticalScrollBar = null;
        }
        // 如果levelSelectController有ScrollView，也进行修复
        if (this.levelSelectController && this.levelSelectController.scrollView) {
            this.levelSelectController.scrollView.horizontalScrollBar = null;
            this.levelSelectController.scrollView.verticalScrollBar = null;
        }
    };
    /**
     * 更新游戏按钮显示状态
     */
    LevelSelectPageController.prototype.updateGameButtons = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (!levelData)
            return;
        // 根据关卡状态显示不同的按钮
        var isLocked = levelData.status === LevelSelectController_1.LevelStatus.LOCKED;
        if (this.startGameButton) {
            this.startGameButton.node.active = !isLocked;
            // 设置按钮文本 - 统一显示"开始游戏"
            var buttonLabel = this.startGameButton.getComponentInChildren(cc.Label);
            if (buttonLabel) {
                buttonLabel.string = "开始游戏";
            }
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = isLocked;
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelSelectPageController.prototype.onStartGameButtonClick = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (levelData && levelData.status !== LevelSelectController_1.LevelStatus.LOCKED) {
            cc.log("\u5F00\u59CB\u6E38\u620F - \u5173\u5361 " + currentLevel);
            // 这里添加进入游戏的逻辑
            this.enterSelectedLevel();
        }
    };
    /**
     * 未解锁按钮点击事件
     */
    LevelSelectPageController.prototype.onLockedButtonClick = function () {
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        cc.log("\u5173\u5361 " + currentLevel + " \u5C1A\u672A\u89E3\u9501");
        // 这里可以添加提示用户关卡未解锁的逻辑
        // 例如显示提示弹窗等
    };
    /**
     * 关卡选择变化回调
     */
    LevelSelectPageController.prototype.onLevelSelectionChanged = function (levelNumber) {
        this.updateUIDisplay();
    };
    __decorate([
        property(LevelSelectController_1.default)
    ], LevelSelectPageController.prototype, "levelSelectController", void 0);
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectPageController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "lockedButton", void 0);
    LevelSelectPageController = __decorate([
        ccclass
    ], LevelSelectPageController);
    return LevelSelectPageController;
}(cc.Component));
exports.default = LevelSelectPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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