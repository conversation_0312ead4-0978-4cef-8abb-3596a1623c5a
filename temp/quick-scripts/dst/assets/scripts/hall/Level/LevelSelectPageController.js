
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'eed91BOnAtLKYkInYtuL76o', 'LevelSelectPageController');
// scripts/hall/Level/LevelSelectPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalManagerController_1 = require("../../GlobalManagerController");
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelSelectPageController = /** @class */ (function (_super) {
    __extends(LevelSelectPageController, _super);
    function LevelSelectPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.levelSelectController = null;
        _this.scrollView = null;
        // 新增的游戏按钮
        _this.startGameButton = null;
        _this.lockedButton = null;
        return _this;
    }
    LevelSelectPageController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        // 设置按钮初始状态 - 默认显示开始游戏按钮，避免状态切换闪烁
        if (this.startGameButton) {
            this.startGameButton.node.active = true;
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = false;
            this.lockedButton.node.on('click', this.onLockedButtonClick, this);
        }
    };
    LevelSelectPageController.prototype.start = function () {
        var _this = this;
        // 设置关卡选择变化回调
        if (this.levelSelectController) {
            this.levelSelectController.onLevelSelectionChanged = function (levelNumber) {
                _this.onLevelSelectionChanged(levelNumber);
            };
        }
        // 延迟更新UI，确保在关卡数据加载后再更新按钮状态
        this.scheduleOnce(function () {
            _this.updateUIDisplay();
        }, 0.1);
    };
    /**
     * 更新UI显示状态
     */
    LevelSelectPageController.prototype.updateUIDisplay = function () {
        // 更新游戏按钮状态
        this.updateGameButtons();
    };
    /**
     * 进入选中的关卡
     */
    LevelSelectPageController.prototype.enterSelectedLevel = function () {
        if (this.levelSelectController) {
            var selectedLevel_1 = this.levelSelectController.getCurrentSelectedLevel();
            cc.log("\u8FDB\u5165\u5173\u5361 " + selectedLevel_1);
            // 尝试多种方式获取全局管理器
            var globalManager_1 = null;
            // 方法1: 尝试查找 global_node 节点（根目录）
            var globalManagerNode = cc.find("global_node");
            if (globalManagerNode) {
                globalManager_1 = globalManagerNode.getComponent(GlobalManagerController_1.default);
                cc.log("✅ 找到 global_node 节点（根目录）");
            }
            else {
                cc.log("❌ 未找到 global_node 节点（根目录）");
            }
            // 方法1.1: 尝试查找 Canvas/global_node 节点
            if (!globalManager_1) {
                globalManagerNode = cc.find("Canvas/global_node");
                if (globalManagerNode) {
                    cc.log("✅ 找到 Canvas/global_node 节点");
                    globalManager_1 = globalManagerNode.getComponent(GlobalManagerController_1.default);
                    if (globalManager_1) {
                        cc.log("✅ 成功获取 GlobalManagerController 组件");
                    }
                    else {
                        cc.log("❌ Canvas/global_node 节点上没有 GlobalManagerController 组件");
                        // 列出节点上的所有组件
                        var components = globalManagerNode.getComponents(cc.Component);
                        cc.log("节点上的组件列表：", components.map(function (comp) { return comp.constructor.name; }));
                    }
                }
                else {
                    cc.log("❌ 未找到 Canvas/global_node 节点");
                }
            }
            if (globalManager_1) {
                // 先切换到关卡页面
                globalManager_1.setCurrentPage(GlobalManagerController_1.PageType.LEVEL_PAGE);
                // 延迟设置关卡，确保页面切换完成后再设置
                this.scheduleOnce(function () {
                    if (globalManager_1.levelPageController) {
                        cc.log("\uD83C\uDFAF LevelSelectPageController \u5EF6\u8FDF\u8BBE\u7F6E\u5173\u5361: " + selectedLevel_1);
                        globalManager_1.levelPageController.setCurrentLevel(selectedLevel_1);
                    }
                }, 0.1);
            }
            else {
                cc.error("无法找到 GlobalManagerController 组件！请检查场景配置。");
                cc.error("请确保场景中有节点挂载了 GlobalManagerController 组件。");
                // 调试信息：列出场景中的所有根节点名称
                var scene = cc.director.getScene();
                if (scene) {
                    cc.log("场景中的根节点：");
                    for (var i = 0; i < scene.children.length; i++) {
                        cc.log("- " + scene.children[i].name);
                    }
                }
            }
        }
    };
    /**
     * 设置关卡进度（从外部调用）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    LevelSelectPageController.prototype.setLevelProgress = function (levelProgressData) {
        var _this = this;
        if (!this.levelSelectController)
            return;
        // 使用 LevelSelectController 的新方法来设置关卡进度
        this.levelSelectController.setLevelProgress(levelProgressData);
        // 立即更新UI显示，确保按钮状态正确
        this.scheduleOnce(function () {
            _this.updateUIDisplay();
        }, 0.05); // 很短的延迟，确保关卡数据已经更新
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectPageController.prototype.fixScrollViewScrollbar = function () {
        // 如果有ScrollView引用，清除Scrollbar引用以避免错误
        if (this.scrollView) {
            this.scrollView.horizontalScrollBar = null;
            this.scrollView.verticalScrollBar = null;
        }
        // 如果levelSelectController有ScrollView，也进行修复
        if (this.levelSelectController && this.levelSelectController.scrollView) {
            this.levelSelectController.scrollView.horizontalScrollBar = null;
            this.levelSelectController.scrollView.verticalScrollBar = null;
        }
    };
    /**
     * 更新游戏按钮显示状态
     */
    LevelSelectPageController.prototype.updateGameButtons = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (!levelData)
            return;
        // 根据关卡状态显示不同的按钮
        var isLocked = levelData.status === LevelSelectController_1.LevelStatus.LOCKED;
        if (this.startGameButton) {
            this.startGameButton.node.active = !isLocked;
            // 设置按钮文本 - 统一显示"开始游戏"
            var buttonLabel = this.startGameButton.getComponentInChildren(cc.Label);
            if (buttonLabel) {
                buttonLabel.string = "开始游戏";
            }
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = isLocked;
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelSelectPageController.prototype.onStartGameButtonClick = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (levelData && levelData.status !== LevelSelectController_1.LevelStatus.LOCKED) {
            cc.log("\u5F00\u59CB\u6E38\u620F - \u5173\u5361 " + currentLevel);
            // 这里添加进入游戏的逻辑
            this.enterSelectedLevel();
        }
    };
    /**
     * 未解锁按钮点击事件
     */
    LevelSelectPageController.prototype.onLockedButtonClick = function () {
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        cc.log("\u5173\u5361 " + currentLevel + " \u5C1A\u672A\u89E3\u9501");
        // 这里可以添加提示用户关卡未解锁的逻辑
        // 例如显示提示弹窗等
    };
    /**
     * 关卡选择变化回调
     */
    LevelSelectPageController.prototype.onLevelSelectionChanged = function (levelNumber) {
        this.updateUIDisplay();
    };
    __decorate([
        property(LevelSelectController_1.default)
    ], LevelSelectPageController.prototype, "levelSelectController", void 0);
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectPageController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "lockedButton", void 0);
    LevelSelectPageController = __decorate([
        ccclass
    ], LevelSelectPageController);
    return LevelSelectPageController;
}(cc.Component));
exports.default = LevelSelectPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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