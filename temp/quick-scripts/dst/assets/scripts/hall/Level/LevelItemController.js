
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelItemController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5e3930KM/5Lr5e2Eq/8nGah', 'LevelItemController');
// scripts/hall/Level/LevelItemController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelItemController = /** @class */ (function (_super) {
    __extends(LevelItemController, _super);
    function LevelItemController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.levelSprite = null;
        _this.levelLabel = null;
        _this.levelButton = null;
        _this.levelNumber = 1;
        _this.levelStatus = LevelSelectController_1.LevelStatus.LOCKED;
        _this.isSelected = false;
        return _this;
    }
    LevelItemController.prototype.onLoad = function () {
        // 初始化组件引用
        if (!this.levelSprite) {
            this.levelSprite = this.getComponent(cc.Sprite);
        }
        if (!this.levelLabel) {
            this.levelLabel = this.getComponentInChildren(cc.Label);
        }
        if (!this.levelButton) {
            this.levelButton = this.getComponent(cc.Button);
        }
    };
    LevelItemController.prototype.start = function () {
        // 确保在start时更新外观
        this.updateAppearance();
    };
    /**
     * 设置关卡数据
     */
    LevelItemController.prototype.setLevelData = function (levelNumber, status, selected) {
        if (selected === void 0) { selected = false; }
        this.levelNumber = levelNumber;
        this.levelStatus = status;
        this.isSelected = selected;
        // 更新标签文本
        if (this.levelLabel) {
            this.levelLabel.string = levelNumber.toString();
        }
        this.updateAppearance();
    };
    /**
     * 检查是否为特殊关卡（第5、10、15、20、25关）
     */
    LevelItemController.prototype.isSpecialLevel = function (levelNumber) {
        return levelNumber % 5 === 0;
    };
    /**
     * 更新外观
     */
    LevelItemController.prototype.updateAppearance = function () {
        var _this = this;
        if (!this.levelSprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 检查是否为特殊关卡（第5、10、15、20、25关）
        var isSpecialLevel = this.isSpecialLevel(this.levelNumber);
        var uiSuffix = isSpecialLevel ? "01" : "";
        // 根据状态和是否选中确定图片路径和大小
        if (this.isSelected) {
            size = cc.size(86, 86);
            switch (this.levelStatus) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix + "_choose";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix + "_choose";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix + "_choose";
                    break;
            }
        }
        else {
            switch (this.levelStatus) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix;
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix;
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix;
                    break;
            }
        }
        // 设置节点大小
        this.node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame && _this.levelSprite) {
                _this.levelSprite.spriteFrame = spriteFrame;
            }
            else if (err) {
                cc.error("Failed to load sprite: " + imagePath, err);
            }
        });
        // 设置按钮交互状态
        if (this.levelButton) {
            this.levelButton.interactable = (this.levelStatus !== LevelSelectController_1.LevelStatus.LOCKED);
        }
        // 设置标签样式
        this.setupLabelStyle();
    };
    /**
     * 设置选中状态
     */
    LevelItemController.prototype.setSelected = function (selected) {
        if (this.isSelected !== selected) {
            this.isSelected = selected;
            this.updateAppearance();
        }
    };
    /**
     * 获取关卡号
     */
    LevelItemController.prototype.getLevelNumber = function () {
        return this.levelNumber;
    };
    /**
     * 获取关卡状态
     */
    LevelItemController.prototype.getLevelStatus = function () {
        return this.levelStatus;
    };
    /**
     * 是否选中
     */
    LevelItemController.prototype.getIsSelected = function () {
        return this.isSelected;
    };
    /**
     * 设置标签样式
     */
    LevelItemController.prototype.setupLabelStyle = function () {
        if (!this.levelLabel) {
            cc.warn("LevelItemController: levelLabel is null");
            return;
        }
        // 设置字体大小为30
        this.levelLabel.fontSize = 30;
        // 设置颜色为白色 #FFFFFF
        this.levelLabel.node.color = cc.color(255, 255, 255);
        // 设置居中对齐
        this.levelLabel.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        this.levelLabel.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 注意：Cocos Creator的Label组件不支持直接设置加粗
        // 如需加粗效果，需要使用加粗字体文件
        // 添加外边框 LabelOutline
        var outline = this.levelLabel.getComponent(cc.LabelOutline);
        if (!outline) {
            outline = this.levelLabel.addComponent(cc.LabelOutline);
        }
        // 根据关卡状态设置边框颜色
        var outlineColor;
        switch (this.levelStatus) {
            case LevelSelectController_1.LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelSelectController_1.LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelSelectController_1.LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }
        outline.color = outlineColor;
        outline.width = 1;
        // 确保标签位置居中
        this.levelLabel.node.setPosition(0, 0);
    };
    /**
     * 关卡点击事件
     */
    LevelItemController.prototype.onLevelClick = function () {
        if (this.levelStatus === LevelSelectController_1.LevelStatus.LOCKED) {
            cc.log("Level " + this.levelNumber + " is locked!");
            return;
        }
        // 发送关卡选择事件
        this.node.emit('level-selected', this.levelNumber);
        cc.log("Level " + this.levelNumber + " clicked!");
    };
    __decorate([
        property(cc.Sprite)
    ], LevelItemController.prototype, "levelSprite", void 0);
    __decorate([
        property(cc.Label)
    ], LevelItemController.prototype, "levelLabel", void 0);
    __decorate([
        property(cc.Button)
    ], LevelItemController.prototype, "levelButton", void 0);
    LevelItemController = __decorate([
        ccclass
    ], LevelItemController);
    return LevelItemController;
}(cc.Component));
exports.default = LevelItemController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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