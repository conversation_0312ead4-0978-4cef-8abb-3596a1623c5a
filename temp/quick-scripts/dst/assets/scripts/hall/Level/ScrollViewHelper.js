
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e35d5kF1ylHHqnHP0EmgtFI', 'ScrollViewHelper');
// scripts/hall/Level/ScrollViewHelper.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScrollViewHelper = void 0;
/**
 * ScrollView辅助工具类
 * 用于解决ScrollView常见的配置问题
 */
var ScrollViewHelper = /** @class */ (function () {
    function ScrollViewHelper() {
    }
    /**
     * 修复ScrollView的Scrollbar引用问题
     * 这个方法会清除ScrollView的Scrollbar引用，避免"Scrollbar referenced by component is invalid"错误
     */
    ScrollViewHelper.fixScrollbarReference = function (scrollView) {
        if (!scrollView) {
            cc.warn("ScrollView is null or undefined");
            return;
        }
        // 清除Scrollbar引用
        scrollView.horizontalScrollBar = null;
        scrollView.verticalScrollBar = null;
    };
    /**
     * 配置水平滚动的ScrollView
     */
    ScrollViewHelper.setupHorizontalScrollView = function (scrollView, content) {
        if (!scrollView || !content) {
            cc.error("ScrollView or content is null");
            return;
        }
        // 基本配置
        scrollView.content = content;
        scrollView.horizontal = true;
        scrollView.vertical = false;
        scrollView.inertia = true;
        scrollView.elastic = true;
        scrollView.bounceDuration = 0.3;
        // 清除Scrollbar引用
        this.fixScrollbarReference(scrollView);
    };
    /**
     * 配置垂直滚动的ScrollView
     */
    ScrollViewHelper.setupVerticalScrollView = function (scrollView, content) {
        if (!scrollView || !content) {
            cc.error("ScrollView or content is null");
            return;
        }
        // 基本配置
        scrollView.content = content;
        scrollView.horizontal = false;
        scrollView.vertical = true;
        scrollView.inertia = true;
        scrollView.elastic = true;
        scrollView.bounceDuration = 0.3;
        // 清除Scrollbar引用
        this.fixScrollbarReference(scrollView);
        cc.log("Vertical ScrollView configured successfully");
    };
    /**
     * 创建一个完整的水平ScrollView结构
     */
    ScrollViewHelper.createHorizontalScrollView = function (parent, size) {
        // 创建ScrollView节点
        var scrollViewNode = new cc.Node("ScrollView");
        var scrollView = scrollViewNode.addComponent(cc.ScrollView);
        scrollViewNode.setContentSize(size);
        scrollViewNode.parent = parent;
        // 创建Viewport节点
        var viewport = new cc.Node("Viewport");
        var mask = viewport.addComponent(cc.Mask);
        viewport.setContentSize(size);
        viewport.parent = scrollViewNode;
        // 创建Content节点
        var content = new cc.Node("Content");
        content.setContentSize(size);
        content.parent = viewport;
        // 配置ScrollView
        this.setupHorizontalScrollView(scrollView, content);
        return { scrollView: scrollView, viewport: viewport, content: content };
    };
    /**
     * 创建一个完整的垂直ScrollView结构
     */
    ScrollViewHelper.createVerticalScrollView = function (parent, size) {
        // 创建ScrollView节点
        var scrollViewNode = new cc.Node("ScrollView");
        var scrollView = scrollViewNode.addComponent(cc.ScrollView);
        scrollViewNode.setContentSize(size);
        scrollViewNode.parent = parent;
        // 创建Viewport节点
        var viewport = new cc.Node("Viewport");
        var mask = viewport.addComponent(cc.Mask);
        viewport.setContentSize(size);
        viewport.parent = scrollViewNode;
        // 创建Content节点
        var content = new cc.Node("Content");
        content.setContentSize(size);
        content.parent = viewport;
        // 配置ScrollView
        this.setupVerticalScrollView(scrollView, content);
        return { scrollView: scrollView, viewport: viewport, content: content };
    };
    /**
     * 滚动到指定位置（水平）
     */
    ScrollViewHelper.scrollToHorizontalPercent = function (scrollView, percent, duration) {
        if (duration === void 0) { duration = 0.3; }
        if (!scrollView) {
            cc.error("ScrollView is null");
            return;
        }
        var clampedPercent = cc.misc.clampf(percent, 0, 1);
        scrollView.scrollToPercentHorizontal(clampedPercent, duration);
    };
    /**
     * 滚动到指定位置（垂直）
     */
    ScrollViewHelper.scrollToVerticalPercent = function (scrollView, percent, duration) {
        if (duration === void 0) { duration = 0.3; }
        if (!scrollView) {
            cc.error("ScrollView is null");
            return;
        }
        var clampedPercent = cc.misc.clampf(percent, 0, 1);
        scrollView.scrollToPercentVertical(clampedPercent, duration);
    };
    /**
     * 获取当前滚动位置（水平）
     */
    ScrollViewHelper.getHorizontalScrollPercent = function (scrollView) {
        if (!scrollView) {
            cc.error("ScrollView is null");
            return 0;
        }
        var offset = scrollView.getScrollOffset();
        var maxScrollOffset = scrollView.getMaxScrollOffset();
        if (maxScrollOffset.x === 0)
            return 0;
        return Math.abs(offset.x) / Math.abs(maxScrollOffset.x);
    };
    /**
     * 获取当前滚动位置（垂直）
     */
    ScrollViewHelper.getVerticalScrollPercent = function (scrollView) {
        if (!scrollView) {
            cc.error("ScrollView is null");
            return 0;
        }
        var offset = scrollView.getScrollOffset();
        var maxScrollOffset = scrollView.getMaxScrollOffset();
        if (maxScrollOffset.y === 0)
            return 0;
        return Math.abs(offset.y) / Math.abs(maxScrollOffset.y);
    };
    /**
     * 检查ScrollView是否可以滚动
     */
    ScrollViewHelper.canScroll = function (scrollView) {
        if (!scrollView || !scrollView.content) {
            return { horizontal: false, vertical: false };
        }
        var viewSize = scrollView.node.getContentSize();
        var contentSize = scrollView.content.getContentSize();
        return {
            horizontal: contentSize.width > viewSize.width,
            vertical: contentSize.height > viewSize.height
        };
    };
    /**
     * 调试ScrollView信息
     */
    ScrollViewHelper.debugScrollView = function (scrollView, name) {
        if (name === void 0) { name = "ScrollView"; }
        if (!scrollView) {
            cc.log(name + ": ScrollView is null");
            return;
        }
        var viewSize = scrollView.node.getContentSize();
        var contentSize = scrollView.content ? scrollView.content.getContentSize() : cc.Size.ZERO;
        var offset = scrollView.getScrollOffset();
        var maxOffset = scrollView.getMaxScrollOffset();
        var canScrollInfo = this.canScroll(scrollView);
    };
    return ScrollViewHelper;
}());
exports.ScrollViewHelper = ScrollViewHelper;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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