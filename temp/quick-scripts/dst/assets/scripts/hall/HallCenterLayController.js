
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/HallCenterLayController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e30e9U3AbZJ1Yq4QqLLdUm7', 'HallCenterLayController');
// scripts/hall/HallCenterLayController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CenterLaytouType = void 0;
var GlobalBean_1 = require("../bean/GlobalBean");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var ToastController_1 = require("../ToastController");
var HallAutoController_1 = require("./HallAutoController");
var HallCreateRoomController_1 = require("./HallCreateRoomController");
var HallJoinRoomController_1 = require("./HallJoinRoomController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var CenterLaytouType;
(function (CenterLaytouType) {
    CenterLaytouType[CenterLaytouType["HALL_AUTO_VIEW"] = 0] = "HALL_AUTO_VIEW";
    CenterLaytouType[CenterLaytouType["HALL_CREAT_ROOM_VIEW"] = 1] = "HALL_CREAT_ROOM_VIEW";
    CenterLaytouType[CenterLaytouType["HALL_JOIN_ROOM_VIEW"] = 2] = "HALL_JOIN_ROOM_VIEW";
})(CenterLaytouType = exports.CenterLaytouType || (exports.CenterLaytouType = {}));
var HallCenterLayController = /** @class */ (function (_super) {
    __extends(HallCenterLayController, _super);
    function HallCenterLayController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.hallAutoView = null; //快速开始view
        _this.hallCreateRoomView = null; //创建房间 view
        _this.hallJoinRoomView = null; //加入房间的 view
        _this.toastController = null; //toast 的布局
        _this.hallAutoController = null;
        _this.hallCreateRoomController = null;
        _this.hallJoinRoomController = null;
        _this.centerLaytouType = null; //当前展示的哪一个 view
        return _this;
        // update (dt) {}
    }
    HallCenterLayController.prototype.onLoad = function () {
        this.hallAutoController = this.hallAutoView.getComponent(HallAutoController_1.default);
        this.hallCreateRoomController = this.hallCreateRoomView.getComponent(HallCreateRoomController_1.default);
        this.hallJoinRoomController = this.hallJoinRoomView.getComponent(HallJoinRoomController_1.default);
    };
    HallCenterLayController.prototype.onEnable = function () {
        this.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW);
    };
    HallCenterLayController.prototype.start = function () {
        var _this = this;
        this.hallAutoController.setButtonClick(function () {
            //start 按钮的点击回调
            if (_this.startClick) {
                _this.startClick();
            }
        }, function () {
            //create 按钮的点击回调
            if (_this.createClick) {
                _this.createClick();
            }
        }, function () {
            //join 按钮的点击回调
            _this.setCenterLaytouType(CenterLaytouType.HALL_JOIN_ROOM_VIEW);
        });
        this.hallCreateRoomController.setClick(function (userId, nickname) {
            //房间内点击玩家头像
            if (_this.seatCallback) {
                _this.seatCallback(userId, nickname);
            }
        }, function () {
            //点击 start 的回调
            //发送开始游戏的消息
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeInviteStart, {});
        }, function () {
            //ready的回调
            //发送游戏准备的消息
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeInviteReady, { 'ready': true });
        }, function () {
            //cancel 的回调
            //发送取消游戏准备的消息
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeInviteReady, { 'ready': false });
        });
        this.hallJoinRoomController.setButtonClick(function (inviteCode) {
            //加入房间的按钮
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeAcceptInvite, { 'inviteCode': Number(inviteCode) });
        });
    };
    //设置显示哪一个 view
    HallCenterLayController.prototype.setCenterLaytouType = function (centerLaytouType) {
        if (this.centerLaytouType === centerLaytouType) {
            return;
        }
        this.centerLaytouType = centerLaytouType;
        this.hallAutoView.active = false;
        this.hallCreateRoomView.active = false;
        this.hallJoinRoomView.active = false;
        switch (centerLaytouType) {
            case CenterLaytouType.HALL_AUTO_VIEW:
                this.hallAutoView.active = true;
                break;
            case CenterLaytouType.HALL_CREAT_ROOM_VIEW:
                this.hallCreateRoomView.active = true;
                break;
            case CenterLaytouType.HALL_JOIN_ROOM_VIEW:
                this.hallJoinRoomView.active = true;
                break;
        }
    };
    //获取当前正在显示的那个页面
    HallCenterLayController.prototype.getCenterLaytouType = function () {
        return this.centerLaytouType;
    };
    //设置按钮的点击回调
    HallCenterLayController.prototype.setClick = function (startClick, createClick, seatCallback) {
        this.startClick = startClick;
        this.createClick = createClick;
        this.seatCallback = seatCallback;
    };
    //设置接受邀请成功
    HallCenterLayController.prototype.setAcceptInvite = function (acceptInvite) {
        //如果正在，接受邀请页面的话
        if (this.centerLaytouType == CenterLaytouType.HALL_JOIN_ROOM_VIEW) {
            //进入创建房间页面
            this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW);
        }
        else if (this.centerLaytouType == CenterLaytouType.HALL_CREAT_ROOM_VIEW) {
            //如果已经在房间页面，就刷新数据
            this.hallCreateRoomController.refreshPlayer(acceptInvite.inviteInfo);
        }
        else {
            //还在大厅收到重链接的消息
            //进入创建房间页面
            this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW);
        }
    };
    //离开房间
    HallCenterLayController.prototype.leaveRoom = function (noticeLeaveInvite) {
        //判断是不是自己离开了房间，或者是房主离开（房主离开就是解散房间了）
        if (noticeLeaveInvite.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId || noticeLeaveInvite.isCreator) {
            if (noticeLeaveInvite.userId != GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
                this.toastController.showContent(window.getLocalizedStr('LeaveRoom'));
            }
            //当前展示的是加入房间 或者创建房间的话  返回键返回大厅
            this.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW);
        }
        else {
            //刷新数据
            this.hallCreateRoomController.leavePlayer(noticeLeaveInvite);
        }
    };
    //设置门票
    HallCenterLayController.prototype.setFees = function () {
        this.hallAutoController.setFees();
    };
    //进入私人房间
    HallCenterLayController.prototype.joinCreateRoom = function () {
        this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW);
    };
    HallCenterLayController.prototype.joinError = function () {
        this.hallJoinRoomController.joinError();
    };
    //准备 取消准备
    HallCenterLayController.prototype.setReadyState = function (noticeUserInviteStatus) {
        this.hallCreateRoomController.setReadyState(noticeUserInviteStatus);
    };
    __decorate([
        property(cc.Node)
    ], HallCenterLayController.prototype, "hallAutoView", void 0);
    __decorate([
        property(cc.Node)
    ], HallCenterLayController.prototype, "hallCreateRoomView", void 0);
    __decorate([
        property(cc.Node)
    ], HallCenterLayController.prototype, "hallJoinRoomView", void 0);
    __decorate([
        property(ToastController_1.default)
    ], HallCenterLayController.prototype, "toastController", void 0);
    HallCenterLayController = __decorate([
        ccclass
    ], HallCenterLayController);
    return HallCenterLayController;
}(cc.Component));
exports.default = HallCenterLayController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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