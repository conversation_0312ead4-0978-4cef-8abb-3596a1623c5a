
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/KickOutDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0a188/DymxMVpmLudNIvq/G', 'KickOutDialogController');
// scripts/hall/KickOutDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var KickOutDialogController = /** @class */ (function (_super) {
    __extends(KickOutDialogController, _super);
    function KickOutDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.content = null;
        _this.cancelBtn = null;
        _this.kickOutBtn = null;
        _this.brownText = null;
        _this.userId = '';
        return _this;
        // update (dt) {}
    }
    KickOutDialogController.prototype.onLoad = function () {
        this.brownText = this.content.getComponent(cc.Label);
        this.localizedLabel = this.content.getComponent('LocalizedLabel');
    };
    KickOutDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        //cancel 按钮点击事件
        Tools_1.Tools.yellowButton(this.cancelBtn, function () {
            _this.hide();
        });
        //kickOut 按钮点击事件
        Tools_1.Tools.redButton(this.kickOutBtn, function () {
            _this.hide();
            //发送玩家被踢出的消息
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeInviteKickOut, { 'userId': _this.userId });
        });
    };
    KickOutDialogController.prototype.show = function (userId, nickname) {
        this.node.active = true;
        this.boardBg.scale = 0;
        this.userId = userId;
        this.brownText.string = window.getLocalizedStr('kickout2');
        this.localizedLabel.bindParam(this.truncateString(nickname));
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    KickOutDialogController.prototype.hide = function () {
        var _this = this;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    KickOutDialogController.prototype.truncateString = function (str) {
        if (str.length > 10) {
            return str.slice(0, 10) + '...';
        }
        else {
            return str;
        }
    };
    __decorate([
        property(cc.Node)
    ], KickOutDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], KickOutDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], KickOutDialogController.prototype, "content", void 0);
    __decorate([
        property(cc.Node)
    ], KickOutDialogController.prototype, "cancelBtn", void 0);
    __decorate([
        property(cc.Node)
    ], KickOutDialogController.prototype, "kickOutBtn", void 0);
    KickOutDialogController = __decorate([
        ccclass
    ], KickOutDialogController);
    return KickOutDialogController;
}(cc.Component));
exports.default = KickOutDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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