
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/HallJoinRoomController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1a7eDE6kBEA66nRqV1LA92', 'HallJoinRoomController');
// scripts/hall/HallJoinRoomController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HallJoinRoomController = /** @class */ (function (_super) {
    __extends(HallJoinRoomController, _super);
    function HallJoinRoomController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.errorLabel = null;
        _this.editBox = null; //文本输入框
        _this.buttonNoClick = null; //join 按钮（不可点击）
        _this.buttonReady = null; //join 按钮（可点击）
        _this.newText = '';
        _this.originalPlaceholder = '';
        _this.skillsClick = null;
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    HallJoinRoomController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.yellowButton(this.buttonReady, function () {
            //点击加入房间
            if (_this.callback) {
                _this.callback(_this.newText);
            }
        });
        Tools_1.Tools.grayButton(this.buttonNoClick, function () { });
    };
    HallJoinRoomController.prototype.onEnable = function () {
        this.destroyEditBox();
        this.editBox.node.on('text-changed', this.onTextChanged, this);
        this.editBox.node.on('editing-did-began', this.onEditingBegan, this);
        this.editBox.node.on('editing-did-ended', this.onEditingEnded, this);
        this.buttonStyle(false);
    };
    HallJoinRoomController.prototype.onDisable = function () {
        this.destroyEditBox();
        this.editBox.string = '';
        this.errorLabel.active = false;
        // 恢复 placeholder 显示
        if (this.editBox['_N$placeholderLabel']) {
            this.editBox['_N$placeholderLabel'].node.opacity = 255;
        }
        // 恢复 placeholder 文本
        if (this.originalPlaceholder) {
            this.editBox.placeholder = this.originalPlaceholder;
        }
    };
    HallJoinRoomController.prototype.onTextChanged = function () {
        // 当 EditBox 的文本发生变化时执行这里的代码
        this.newText = this.editBox.string;
        this.errorLabel.active = false;
        if (this.newText.length > 0) {
            this.buttonStyle(true);
        }
        else {
            this.buttonStyle(false);
        }
    };
    HallJoinRoomController.prototype.onEditingBegan = function () {
        // 开始编辑时隐藏 placeholder
        console.log("EditBox editing began");
        // 方法1: 通过设置透明度隐藏
        var placeholderLabel = this.editBox['_N$placeholderLabel'];
        if (placeholderLabel) {
            console.log("Hiding placeholder via opacity");
            placeholderLabel.node.opacity = 0;
        }
        // 方法2: 通过设置 placeholder 文本为空
        if (this.editBox.placeholder && this.originalPlaceholder === '') {
            this.originalPlaceholder = this.editBox.placeholder;
        }
        this.editBox.placeholder = '';
        console.log("Placeholder text cleared");
    };
    HallJoinRoomController.prototype.onEditingEnded = function () {
        // 结束编辑时，如果没有文本则显示 placeholder
        console.log("EditBox editing ended, text length:", this.editBox.string.length);
        if (this.editBox.string.length === 0) {
            // 方法1: 恢复透明度
            var placeholderLabel = this.editBox['_N$placeholderLabel'];
            if (placeholderLabel) {
                console.log("Showing placeholder via opacity");
                placeholderLabel.node.opacity = 255;
            }
            // 方法2: 恢复 placeholder 文本
            if (this.originalPlaceholder) {
                this.editBox.placeholder = this.originalPlaceholder;
                console.log("Placeholder text restored");
            }
        }
    };
    HallJoinRoomController.prototype.destroyEditBox = function () {
        this.editBox.node.off('text-changed', this.onTextChanged, this);
        this.editBox.node.off('editing-did-began', this.onEditingBegan, this);
        this.editBox.node.off('editing-did-ended', this.onEditingEnded, this);
    };
    HallJoinRoomController.prototype.buttonStyle = function (isEnter) {
        if (isEnter) {
            this.buttonReady.active = true;
            this.buttonNoClick.active = false;
        }
        else {
            this.buttonReady.active = false;
            this.buttonNoClick.active = true;
        }
    };
    //加入房间失败
    HallJoinRoomController.prototype.joinError = function () {
        this.errorLabel.active = true;
    };
    //设置点击道具 item 按钮的回调
    HallJoinRoomController.prototype.setButtonClick = function (callback) {
        this.callback = callback; //点击加入房间的按钮
    };
    __decorate([
        property(cc.Node)
    ], HallJoinRoomController.prototype, "errorLabel", void 0);
    __decorate([
        property(cc.EditBox)
    ], HallJoinRoomController.prototype, "editBox", void 0);
    __decorate([
        property(cc.Node)
    ], HallJoinRoomController.prototype, "buttonNoClick", void 0);
    __decorate([
        property(cc.Node)
    ], HallJoinRoomController.prototype, "buttonReady", void 0);
    HallJoinRoomController = __decorate([
        ccclass
    ], HallJoinRoomController);
    return HallJoinRoomController;
}(cc.Component));
exports.default = HallJoinRoomController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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