
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/HallCreateRoomController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2c0a4vmyEBEzJ0UUy+JIUO4', 'HallCreateRoomController');
// scripts/hall/HallCreateRoomController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var SeatItemController_1 = require("../pfb/SeatItemController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ButtonStyle;
(function (ButtonStyle) {
    ButtonStyle[ButtonStyle["StartGray"] = 1] = "StartGray";
    ButtonStyle[ButtonStyle["StartGreen"] = 2] = "StartGreen";
    ButtonStyle[ButtonStyle["Ready"] = 3] = "Ready";
    ButtonStyle[ButtonStyle["Cancel"] = 4] = "Cancel";
})(ButtonStyle || (ButtonStyle = {}));
var HallCreateRoomController = /** @class */ (function (_super) {
    __extends(HallCreateRoomController, _super);
    function HallCreateRoomController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardTab = null;
        _this.boardBgInner = null;
        _this.seatLayout = null;
        _this.seatTwoLayout = null; //第二排的座位
        _this.buttonNoClick = null; //灰色的 start 按钮
        _this.buttonStart = null; //可点击的 start 按钮
        _this.buttonReady = null; //准备按钮
        _this.buttonCancel = null; // 取消按钮
        _this.setaItemPfb = null;
        _this.btnCopyNormal = null; //copy按钮  
        _this.btnCopyLabel = null; //copy按钮文案   
        _this.roomNumber = null; //房间号   
        _this.ticketNumber = null; //门票价格 
        _this.countdownTimeLabel = null;
        _this.countdownInterval = null; //倒计时的 id
        _this.seconds = 10; //倒计时 10 秒
        _this._seatListCol = []; //显示用户布局controller
        return _this;
        // update (dt) {}
    }
    HallCreateRoomController.prototype.onLoad = function () {
        this.btnCopyNormal = this.boardTab.getChildByName('btn_copy_normal');
        this.btnCopyLabel = this.btnCopyNormal.getChildByName('label');
        this.roomNumber = this.boardTab.getChildByName('label').getComponent(cc.Label);
        this.ticketNumber = this.boardBgInner.getChildByName('ticket_number').getComponent(cc.Label);
        this.countdownTimeLabel = this.buttonReady.getChildByName('buttonLabel_time').getComponent(cc.Label);
    };
    HallCreateRoomController.prototype.onEnable = function () {
        var _this = this;
        this.scheduleOnce(function () {
            _this.updateCountdownLabel(_this.seconds);
            Tools_1.Tools.setCountDownTimeLabel(_this.buttonReady);
            _this.refreshData(GlobalBean_1.GlobalBean.GetInstance().inviteInfo);
        });
    };
    HallCreateRoomController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.grayButton(this.buttonNoClick, function () { });
        Tools_1.Tools.greenButton(this.buttonStart, function () {
            if (_this.startCallBack) {
                _this.startCallBack();
            }
        });
        Tools_1.Tools.greenButton(this.buttonReady, function () {
            if (_this.readyCallBack) {
                _this.readyCallBack();
            }
        });
        Tools_1.Tools.redButton(this.buttonCancel, function () {
            if (_this.cancelCallBack) {
                _this.cancelCallBack();
            }
        });
        //copy 按钮的点击事件
        Tools_1.Tools.setTouchEvent(this.btnCopyNormal, function (node) {
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.hallRes + 'btn_copy_pressed');
            var color = cc.Color.fromHEX(new cc.Color(), '#925333');
            _this.btnCopyLabel.color = color;
            _this.btnCopyLabel.getComponent(cc.Label).fontSize = 26;
            Tools_1.Tools.copyToClipboard(_this.roomNumber.string);
        }, function (node) {
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.hallRes + 'btn_copy_normal');
            var color = cc.Color.fromHEX(new cc.Color(), '#D07649');
            _this.btnCopyLabel.color = color;
            _this.btnCopyLabel.getComponent(cc.Label).fontSize = 28;
        }, function (node) {
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.hallRes + 'btn_copy_normal');
            var color = cc.Color.fromHEX(new cc.Color(), '#D07649');
            _this.btnCopyLabel.color = color;
            _this.btnCopyLabel.getComponent(cc.Label).fontSize = 28;
        });
    };
    //刷新数据
    HallCreateRoomController.prototype.refreshData = function (inviteInfo) {
        if (inviteInfo == null) {
            return;
        }
        //这里是门票价格的赋值
        var fees = inviteInfo.fee;
        this.ticketNumber.string = fees === 0 ? window.getLocalizedStr('free') : fees + '';
        this._seatListCol = [];
        this.refreshPlayer(inviteInfo);
        this.roomNumber.string = inviteInfo.inviteCode + ''; //邀请码
        //初始进来就两种状态 房主是准备开始按钮  玩家是 ready 按钮
        if (inviteInfo.creatorId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) { //判断自己是不是房主
            this.btnCopyNormal.active = true;
        }
        else {
            this.btnCopyNormal.active = false;
        }
    };
    //刷新座位玩家
    HallCreateRoomController.prototype.refreshPlayer = function (inviteInfo) {
        var _this = this;
        var peopleNumber = inviteInfo.playerNum;
        if (this._seatListCol.length < 1) {
            this.seatLayout.removeAllChildren();
            this.seatTwoLayout.removeAllChildren();
            if (peopleNumber <= 4) {
                this.schedule(function () {
                    _this.seatLayout.setPosition(_this.seatLayout.x, -90);
                }, 0);
                this.seatTwoLayout.active = false;
                var _loop_1 = function (i) {
                    var item = cc.instantiate(this_1.setaItemPfb);
                    var seatEmptyItemController = item.getComponent(SeatItemController_1.default);
                    this_1._seatListCol.push(seatEmptyItemController);
                    this_1.seatLayout.addChild(item);
                    Tools_1.Tools.setTouchEvent(item, function () {
                        if (seatEmptyItemController.getUsers() != null) {
                            _this.setSetaItemClick(seatEmptyItemController.getUsers().userId, seatEmptyItemController.getUsers().nickname);
                        }
                    });
                };
                var this_1 = this;
                for (var i = 0; i < peopleNumber; i++) {
                    _loop_1(i);
                }
            }
            else {
                this.schedule(function () {
                    _this.seatLayout.setPosition(_this.seatLayout.x, -10);
                }, 0);
                this.seatTwoLayout.active = true;
                var _loop_2 = function (i) {
                    var item = cc.instantiate(this_2.setaItemPfb);
                    var seatEmptyItemController = item.getComponent(SeatItemController_1.default);
                    this_2._seatListCol.push(seatEmptyItemController);
                    this_2.seatLayout.addChild(item);
                    Tools_1.Tools.setTouchEvent(item, function () {
                        if (seatEmptyItemController.getUsers() != null) {
                            _this.setSetaItemClick(seatEmptyItemController.getUsers().userId, seatEmptyItemController.getUsers().nickname);
                        }
                    });
                };
                var this_2 = this;
                for (var i = 0; i < 3; i++) {
                    _loop_2(i);
                }
                var _loop_3 = function (i) {
                    var item = cc.instantiate(this_3.setaItemPfb);
                    var seatEmptyItemController = item.getComponent(SeatItemController_1.default);
                    this_3._seatListCol.push(seatEmptyItemController);
                    this_3.seatTwoLayout.addChild(item);
                    Tools_1.Tools.setTouchEvent(item, function () {
                        if (seatEmptyItemController.getUsers() != null) {
                            _this.setSetaItemClick(seatEmptyItemController.getUsers().userId, seatEmptyItemController.getUsers().nickname);
                        }
                    });
                };
                var this_3 = this;
                for (var i = 0; i < peopleNumber - 3; i++) {
                    _loop_3(i);
                }
            }
        }
        for (var i = 0; i < this._seatListCol.length; i++) {
            if (i < inviteInfo.users.length) {
                this._seatListCol[i].setData(inviteInfo.users[i]);
            }
            else {
                this._seatListCol[i].setData(null);
            }
        }
        if (GlobalBean_1.GlobalBean.GetInstance().inviteInfo.creatorId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) { //判断自己是不是房主
            var readyCount = GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users.filter(function (user) { return user.ready; }).length; //查找准备好的玩家数量
            if (GlobalBean_1.GlobalBean.GetInstance().inviteInfo.playerNum === readyCount) { //准备好的玩家数量和总玩家数量一致的话就可以开始游戏了
                this.setButtonType(ButtonStyle.StartGreen);
            }
            else {
                this.setButtonType(ButtonStyle.StartGray);
            }
        }
        else {
            var index = GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users.findIndex(function (item) { return item.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId; }); //搜索
            if (GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users[index].ready) {
                this.setButtonType(ButtonStyle.Cancel);
            }
            else {
                this.setButtonType(ButtonStyle.Ready);
                this.startCountdown(this.seconds);
            }
        }
    };
    HallCreateRoomController.prototype.onDisable = function () {
        var _this = this;
        setTimeout(function () {
            // this.ticketNumber.string = ''
            _this.roomNumber.string = '';
            _this.seatLayout.removeAllChildren();
            _this.seatTwoLayout.removeAllChildren();
        }, 100);
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    //设置按钮显示状态
    HallCreateRoomController.prototype.setButtonType = function (buttonStyle) {
        this.buttonNoClick.active = false;
        this.buttonStart.active = false;
        this.buttonReady.active = false;
        this.buttonCancel.active = false;
        switch (buttonStyle) {
            case ButtonStyle.StartGray:
                this.buttonNoClick.active = true;
                break;
            case ButtonStyle.StartGreen:
                this.buttonStart.active = true;
                break;
            case ButtonStyle.Ready:
                this.buttonReady.active = true;
                break;
            case ButtonStyle.Cancel:
                this.buttonCancel.active = true;
                break;
        }
    };
    //启动倒计时
    HallCreateRoomController.prototype.startCountdown = function (seconds) {
        var _this = this;
        if (this.countdownInterval) {
            // GameMgr.Console.Error('当前存在的定时器 先销毁id：'+this.countdownInterval)
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        var remainingSeconds = seconds;
        this.updateCountdownLabel(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            if (remainingSeconds <= 0) {
                // GameMgr.Console.Error('自动销毁的定时器 id：'+this.countdownInterval)
                clearInterval(_this.countdownInterval);
                _this.countdownInterval = null;
                // 倒计时结束时的处理逻辑
                if (_this.readyCallBack) {
                    _this.readyCallBack();
                }
                return;
            }
            // GameMgr.Console.Error('执行的定时器 id：'+this.countdownInterval)
            _this.updateCountdownLabel(remainingSeconds);
        }, 1000);
        // GameMgr.Console.Error('创建的定时器 id：'+this.countdownInterval)
    };
    HallCreateRoomController.prototype.updateCountdownLabel = function (seconds) {
        if (this.countdownTimeLabel) {
            this.countdownTimeLabel.string = "\uFF08" + seconds + "s\uFF09";
        }
    };
    //有玩家离开
    HallCreateRoomController.prototype.leavePlayer = function (noticeLeaveInvite) {
        GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users = GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users.filter(function (user) { return user.userId !== noticeLeaveInvite.userId; });
        this.refreshPlayer(GlobalBean_1.GlobalBean.GetInstance().inviteInfo);
    };
    //准备 取消准备
    HallCreateRoomController.prototype.setReadyState = function (noticeUserInviteStatus) {
        if (GlobalBean_1.GlobalBean.GetInstance().inviteInfo == null || GlobalBean_1.GlobalBean.GetInstance().loginData == null) {
            return;
        }
        GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users.forEach(function (user) {
            if (user.userId === noticeUserInviteStatus.userId) {
                user.ready = noticeUserInviteStatus.ready;
            }
        });
        this.refreshPlayer(GlobalBean_1.GlobalBean.GetInstance().inviteInfo);
    };
    //设置 item 的点击事件
    HallCreateRoomController.prototype.setSetaItemClick = function (userId, nickname) {
        //得先判断当前的房间我是不是房主  并且要提出的不是我自己
        if (GlobalBean_1.GlobalBean.GetInstance().inviteInfo.creatorId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId
            && userId != GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
            if (this.seatCallback) {
                this.seatCallback(userId, nickname);
            }
        }
    };
    //设置点击的回调
    HallCreateRoomController.prototype.setClick = function (seatCallback, startCallBack, readyCallBack, cancelCallBack) {
        this.seatCallback = seatCallback;
        this.startCallBack = startCallBack;
        this.readyCallBack = readyCallBack;
        this.cancelCallBack = cancelCallBack;
    };
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "boardTab", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "boardBgInner", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "seatLayout", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "seatTwoLayout", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "buttonNoClick", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "buttonStart", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "buttonReady", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "buttonCancel", void 0);
    __decorate([
        property(cc.Prefab)
    ], HallCreateRoomController.prototype, "setaItemPfb", void 0);
    HallCreateRoomController = __decorate([
        ccclass
    ], HallCreateRoomController);
    return HallCreateRoomController;
}(cc.Component));
exports.default = HallCreateRoomController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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