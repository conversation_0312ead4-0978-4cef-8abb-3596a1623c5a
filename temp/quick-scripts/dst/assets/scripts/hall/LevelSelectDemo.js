
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/LevelSelectDemo.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fb002PTGrFERKCYkhthVy56', 'LevelSelectDemo');
// scripts/hall/LevelSelectDemo.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./Level/LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 关卡选择演示控制器
 * 这个脚本展示了如何创建一个完整的关卡选择界面
 */
var LevelSelectDemo = /** @class */ (function (_super) {
    __extends(LevelSelectDemo, _super);
    function LevelSelectDemo() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scrollView = null;
        _this.content = null;
        _this.infoLabel = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 30;
        _this.levelItemWidth = 150;
        // 关卡节点列表
        _this.levelNodes = [];
        return _this;
    }
    LevelSelectDemo.prototype.onLoad = function () {
        this.initLevelData();
        this.createLevelSelectUI();
    };
    LevelSelectDemo.prototype.start = function () {
        this.scrollToLevel(this.currentSelectedLevel);
        this.updateInfoDisplay();
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectDemo.prototype.initLevelData = function () {
        this.levelDataList = [];
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelSelectController_1.LevelStatus.CURRENT; // 第一关为当前关卡
            }
            else if (i <= 3) {
                status = LevelSelectController_1.LevelStatus.COMPLETED; // 前3关已通关（示例）
            }
            else {
                status = LevelSelectController_1.LevelStatus.LOCKED; // 其他关卡未解锁
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
    };
    /**
     * 创建关卡选择UI
     */
    LevelSelectDemo.prototype.createLevelSelectUI = function () {
        if (!this.content) {
            cc.error("Content node is not assigned!");
            return;
        }
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                var lineNode = this.createLineNode();
                this.content.addChild(lineNode);
                lineNode.setPosition(posX + this.levelItemWidth / 2, 0);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectDemo.prototype.createLevelNode = function (levelData) {
        var _this = this;
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        label.fontSize = 20;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = node;
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        node.on('click', function () {
            _this.onLevelClicked(levelData.levelNumber);
        }, this);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线节点
     */
    LevelSelectDemo.prototype.createLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        // 加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
        });
        return node;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectDemo.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var sprite = node.getComponent(cc.Sprite);
        if (!sprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 根据状态和是否选中确定图片路径
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray_choose";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow_choose";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green_choose";
                    break;
            }
        }
        else {
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green";
                    break;
            }
        }
        // 设置节点大小
        node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
        });
    };
    /**
     * 更新所有关卡显示
     */
    LevelSelectDemo.prototype.updateAllLevelsDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectDemo.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        // 计算目标位置的偏移量
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        // 限制偏移量在有效范围内
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        // 设置滚动位置
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectDemo.prototype.onLevelClicked = function (levelNumber) {
        // 检查关卡是否可以选择（未锁定）
        var levelData = this.levelDataList[levelNumber - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
            cc.log("\u5173\u5361 " + levelNumber + " \u672A\u89E3\u9501\uFF01");
            return;
        }
        // 更新当前选中关卡
        this.currentSelectedLevel = levelNumber;
        this.updateAllLevelsDisplay();
        // 滚动到选中关卡
        this.scrollToLevel(levelNumber);
        this.updateInfoDisplay();
        cc.log("\u9009\u62E9\u5173\u5361: " + levelNumber);
    };
    /**
     * 更新信息显示
     */
    LevelSelectDemo.prototype.updateInfoDisplay = function () {
        if (this.infoLabel) {
            var levelData = this.levelDataList[this.currentSelectedLevel - 1];
            var statusText = "";
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    statusText = "未解锁";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    statusText = "进行中";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    statusText = "已通关";
                    break;
            }
            this.infoLabel.string = "\u5F53\u524D\u9009\u4E2D: \u5173\u5361" + this.currentSelectedLevel + " (" + statusText + ")";
        }
    };
    /**
     * 完成当前关卡（测试用）
     */
    LevelSelectDemo.prototype.completeCurrentLevel = function () {
        var levelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.CURRENT) {
            levelData.status = LevelSelectController_1.LevelStatus.COMPLETED;
            // 解锁下一关
            if (this.currentSelectedLevel < this.totalLevels) {
                var nextLevelData = this.levelDataList[this.currentSelectedLevel];
                if (nextLevelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
                    nextLevelData.status = LevelSelectController_1.LevelStatus.CURRENT;
                }
            }
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    };
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectDemo.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Node)
    ], LevelSelectDemo.prototype, "content", void 0);
    __decorate([
        property(cc.Label)
    ], LevelSelectDemo.prototype, "infoLabel", void 0);
    LevelSelectDemo = __decorate([
        ccclass
    ], LevelSelectDemo);
    return LevelSelectDemo;
}(cc.Component));
exports.default = LevelSelectDemo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL2hhbGwvTGV2ZWxTZWxlY3REZW1vLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxvQkFBb0I7QUFDcEIsNEVBQTRFO0FBQzVFLG1CQUFtQjtBQUNuQixzRkFBc0Y7QUFDdEYsOEJBQThCO0FBQzlCLHNGQUFzRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXRGLHVFQUE0RDtBQUV0RCxJQUFBLEtBQXdCLEVBQUUsQ0FBQyxVQUFVLEVBQW5DLE9BQU8sYUFBQSxFQUFFLFFBQVEsY0FBa0IsQ0FBQztBQUU1Qzs7O0dBR0c7QUFFSDtJQUE2QyxtQ0FBWTtJQUF6RDtRQUFBLHFFQTZSQztRQTFSRyxnQkFBVSxHQUFrQixJQUFJLENBQUM7UUFHakMsYUFBTyxHQUFZLElBQUksQ0FBQztRQUd4QixlQUFTLEdBQWEsSUFBSSxDQUFDO1FBRTNCLE9BQU87UUFDQyxtQkFBYSxHQUFVLEVBQUUsQ0FBQztRQUMxQiwwQkFBb0IsR0FBVyxDQUFDLENBQUM7UUFDakMsaUJBQVcsR0FBVyxFQUFFLENBQUM7UUFDekIsb0JBQWMsR0FBVyxHQUFHLENBQUM7UUFFckMsU0FBUztRQUNELGdCQUFVLEdBQWMsRUFBRSxDQUFDOztJQTJRdkMsQ0FBQztJQXpRRyxnQ0FBTSxHQUFOO1FBQ0ksSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1FBQ3JCLElBQUksQ0FBQyxtQkFBbUIsRUFBRSxDQUFDO0lBQy9CLENBQUM7SUFFRCwrQkFBSyxHQUFMO1FBQ0ksSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsQ0FBQztRQUM5QyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztJQUM3QixDQUFDO0lBRUQ7O09BRUc7SUFDSyx1Q0FBYSxHQUFyQjtRQUNJLElBQUksQ0FBQyxhQUFhLEdBQUcsRUFBRSxDQUFDO1FBQ3hCLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQ3hDLElBQUksTUFBTSxTQUFhLENBQUM7WUFDeEIsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFO2dCQUNULE1BQU0sR0FBRyxtQ0FBVyxDQUFDLE9BQU8sQ0FBQyxDQUFDLFdBQVc7YUFDNUM7aUJBQU0sSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUNmLE1BQU0sR0FBRyxtQ0FBVyxDQUFDLFNBQVMsQ0FBQyxDQUFDLGFBQWE7YUFDaEQ7aUJBQU07Z0JBQ0gsTUFBTSxHQUFHLG1DQUFXLENBQUMsTUFBTSxDQUFDLENBQUMsVUFBVTthQUMxQztZQUVELElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDO2dCQUNwQixXQUFXLEVBQUUsQ0FBQztnQkFDZCxNQUFNLEVBQUUsTUFBTTthQUNqQixDQUFDLENBQUM7U0FDTjtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNLLDZDQUFtQixHQUEzQjtRQUNJLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFO1lBQ2YsRUFBRSxDQUFDLEtBQUssQ0FBQywrQkFBK0IsQ0FBQyxDQUFDO1lBQzFDLE9BQU87U0FDVjtRQUVELFNBQVM7UUFDVCxJQUFJLENBQUMsT0FBTyxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFDakMsSUFBSSxDQUFDLFVBQVUsR0FBRyxFQUFFLENBQUM7UUFFckIsUUFBUTtRQUNSLElBQU0sVUFBVSxHQUFHLENBQUMsSUFBSSxDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsY0FBYyxHQUFHLEdBQUcsQ0FBQztRQUN0RSxJQUFJLENBQUMsT0FBTyxDQUFDLEtBQUssR0FBRyxVQUFVLENBQUM7UUFFaEMsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDdkMsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUV4QyxTQUFTO1lBQ1QsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUNsRCxJQUFJLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUNqQyxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUVoQyxPQUFPO1lBQ1AsSUFBTSxJQUFJLEdBQUcsQ0FBQyxHQUFHLElBQUksQ0FBQyxjQUFjLEdBQUcsVUFBVSxHQUFHLENBQUMsR0FBRyxHQUFHLEdBQUcsQ0FBQyxDQUFDO1lBQ2hFLFNBQVMsQ0FBQyxXQUFXLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDO1lBRS9CLGtCQUFrQjtZQUNsQixJQUFJLENBQUMsR0FBRyxJQUFJLENBQUMsV0FBVyxHQUFHLENBQUMsRUFBRTtnQkFDMUIsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFDO2dCQUN2QyxJQUFJLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsQ0FBQztnQkFDaEMsUUFBUSxDQUFDLFdBQVcsQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLGNBQWMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7YUFDM0Q7U0FDSjtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNLLHlDQUFlLEdBQXZCLFVBQXdCLFNBQWM7UUFBdEMsaUJBMkJDO1FBMUJHLElBQU0sSUFBSSxHQUFHLElBQUksRUFBRSxDQUFDLElBQUksQ0FBQyxXQUFTLFNBQVMsQ0FBQyxXQUFhLENBQUMsQ0FBQztRQUUzRCxhQUFhO1FBQ2IsSUFBSSxDQUFDLFlBQVksQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUM7UUFFN0Isa0JBQWtCO1FBQ2xCLElBQU0sU0FBUyxHQUFHLElBQUksRUFBRSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztRQUM1QyxJQUFNLEtBQUssR0FBRyxTQUFTLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUMvQyxLQUFLLENBQUMsTUFBTSxHQUFHLFNBQVMsQ0FBQyxXQUFXLENBQUMsUUFBUSxFQUFFLENBQUM7UUFDaEQsS0FBSyxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUM7UUFDcEIsS0FBSyxDQUFDLElBQUksQ0FBQyxLQUFLLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUM7UUFDbEMsU0FBUyxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUM7UUFFeEIsYUFBYTtRQUNiLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzVDLE1BQU0sQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO1FBRXJCLFNBQVM7UUFDVCxJQUFJLENBQUMsRUFBRSxDQUFDLE9BQU8sRUFBRTtZQUNiLEtBQUksQ0FBQyxjQUFjLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDO1FBQy9DLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUVULFNBQVM7UUFDVCxJQUFJLENBQUMseUJBQXlCLENBQUMsSUFBSSxFQUFFLFNBQVMsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUV2RCxPQUFPLElBQUksQ0FBQztJQUNoQixDQUFDO0lBRUQ7O09BRUc7SUFDSyx3Q0FBYyxHQUF0QjtRQUNJLElBQU0sSUFBSSxHQUFHLElBQUksRUFBRSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUNqQyxJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUU1QyxVQUFVO1FBQ1YsRUFBRSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsa0NBQWtDLEVBQUUsRUFBRSxDQUFDLFdBQVcsRUFBRSxVQUFDLEdBQUcsRUFBRSxXQUFXO1lBQ25GLElBQUksQ0FBQyxHQUFHLElBQUksV0FBVyxFQUFFO2dCQUNyQixNQUFNLENBQUMsV0FBVyxHQUFHLFdBQTZCLENBQUM7YUFDdEQ7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILE9BQU8sSUFBSSxDQUFDO0lBQ2hCLENBQUM7SUFFRDs7T0FFRztJQUNLLG1EQUF5QixHQUFqQyxVQUFrQyxJQUFhLEVBQUUsU0FBYyxFQUFFLFVBQW1CO1FBQ2hGLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzVDLElBQUksQ0FBQyxNQUFNO1lBQUUsT0FBTztRQUVwQixJQUFJLFNBQVMsR0FBRyxFQUFFLENBQUM7UUFDbkIsSUFBSSxJQUFJLEdBQUcsRUFBRSxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFFM0Isa0JBQWtCO1FBQ2xCLElBQUksVUFBVSxFQUFFO1lBQ1osSUFBSSxHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBQ3ZCLFFBQVEsU0FBUyxDQUFDLE1BQU0sRUFBRTtnQkFDdEIsS0FBSyxtQ0FBVyxDQUFDLE1BQU07b0JBQ25CLFNBQVMsR0FBRyx5Q0FBeUMsQ0FBQztvQkFDdEQsTUFBTTtnQkFDVixLQUFLLG1DQUFXLENBQUMsT0FBTztvQkFDcEIsU0FBUyxHQUFHLDJDQUEyQyxDQUFDO29CQUN4RCxNQUFNO2dCQUNWLEtBQUssbUNBQVcsQ0FBQyxTQUFTO29CQUN0QixTQUFTLEdBQUcsMENBQTBDLENBQUM7b0JBQ3ZELE1BQU07YUFDYjtTQUNKO2FBQU07WUFDSCxRQUFRLFNBQVMsQ0FBQyxNQUFNLEVBQUU7Z0JBQ3RCLEtBQUssbUNBQVcsQ0FBQyxNQUFNO29CQUNuQixTQUFTLEdBQUcsa0NBQWtDLENBQUM7b0JBQy9DLE1BQU07Z0JBQ1YsS0FBSyxtQ0FBVyxDQUFDLE9BQU87b0JBQ3BCLFNBQVMsR0FBRyxvQ0FBb0MsQ0FBQztvQkFDakQsTUFBTTtnQkFDVixLQUFLLG1DQUFXLENBQUMsU0FBUztvQkFDdEIsU0FBUyxHQUFHLG1DQUFtQyxDQUFDO29CQUNoRCxNQUFNO2FBQ2I7U0FDSjtRQUVELFNBQVM7UUFDVCxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxDQUFDO1FBRTFCLFVBQVU7UUFDVixFQUFFLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxTQUFTLEVBQUUsRUFBRSxDQUFDLFdBQVcsRUFBRSxVQUFDLEdBQUcsRUFBRSxXQUFXO1lBQzFELElBQUksQ0FBQyxHQUFHLElBQUksV0FBVyxFQUFFO2dCQUNyQixNQUFNLENBQUMsV0FBVyxHQUFHLFdBQTZCLENBQUM7YUFDdEQ7UUFDTCxDQUFDLENBQUMsQ0FBQztJQUNQLENBQUM7SUFFRDs7T0FFRztJQUNLLGdEQUFzQixHQUE5QjtRQUNJLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUM3QyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2hDLElBQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDeEMsSUFBTSxVQUFVLEdBQUcsQ0FBQyxTQUFTLENBQUMsV0FBVyxLQUFLLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1lBRXpFLElBQUksQ0FBQyx5QkFBeUIsQ0FBQyxJQUFJLEVBQUUsU0FBUyxFQUFFLFVBQVUsQ0FBQyxDQUFDO1NBQy9EO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssdUNBQWEsR0FBckIsVUFBc0IsV0FBbUI7UUFDckMsSUFBSSxXQUFXLEdBQUcsQ0FBQyxJQUFJLFdBQVcsR0FBRyxJQUFJLENBQUMsV0FBVztZQUFFLE9BQU87UUFFOUQsSUFBTSxXQUFXLEdBQUcsV0FBVyxHQUFHLENBQUMsQ0FBQztRQUNwQyxJQUFNLFlBQVksR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQztRQUN4QyxJQUFNLGVBQWUsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUM7UUFFbkQsYUFBYTtRQUNiLElBQU0sWUFBWSxHQUFHLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUMsR0FBRyxDQUFDLFlBQVksR0FBRyxlQUFlLENBQUMsQ0FBQztRQUU1RixjQUFjO1FBQ2QsSUFBTSxhQUFhLEdBQUcsRUFBRSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUV6RCxTQUFTO1FBQ1QsSUFBSSxDQUFDLFVBQVUsQ0FBQyx5QkFBeUIsQ0FBQyxhQUFhLEVBQUUsR0FBRyxDQUFDLENBQUM7SUFDbEUsQ0FBQztJQUVEOztPQUVHO0lBQ0ssd0NBQWMsR0FBdEIsVUFBdUIsV0FBbUI7UUFDdEMsa0JBQWtCO1FBQ2xCLElBQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBQ3RELElBQUksU0FBUyxDQUFDLE1BQU0sS0FBSyxtQ0FBVyxDQUFDLE1BQU0sRUFBRTtZQUN6QyxFQUFFLENBQUMsR0FBRyxDQUFDLGtCQUFNLFdBQVcsOEJBQU8sQ0FBQyxDQUFDO1lBQ2pDLE9BQU87U0FDVjtRQUVELFdBQVc7UUFDWCxJQUFJLENBQUMsb0JBQW9CLEdBQUcsV0FBVyxDQUFDO1FBQ3hDLElBQUksQ0FBQyxzQkFBc0IsRUFBRSxDQUFDO1FBRTlCLFVBQVU7UUFDVixJQUFJLENBQUMsYUFBYSxDQUFDLFdBQVcsQ0FBQyxDQUFDO1FBRWhDLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1FBQ3pCLEVBQUUsQ0FBQyxHQUFHLENBQUMsK0JBQVMsV0FBYSxDQUFDLENBQUM7SUFDbkMsQ0FBQztJQUVEOztPQUVHO0lBQ0ssMkNBQWlCLEdBQXpCO1FBQ0ksSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFO1lBQ2hCLElBQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLG9CQUFvQixHQUFHLENBQUMsQ0FBQyxDQUFDO1lBQ3BFLElBQUksVUFBVSxHQUFHLEVBQUUsQ0FBQztZQUNwQixRQUFRLFNBQVMsQ0FBQyxNQUFNLEVBQUU7Z0JBQ3RCLEtBQUssbUNBQVcsQ0FBQyxNQUFNO29CQUNuQixVQUFVLEdBQUcsS0FBSyxDQUFDO29CQUNuQixNQUFNO2dCQUNWLEtBQUssbUNBQVcsQ0FBQyxPQUFPO29CQUNwQixVQUFVLEdBQUcsS0FBSyxDQUFDO29CQUNuQixNQUFNO2dCQUNWLEtBQUssbUNBQVcsQ0FBQyxTQUFTO29CQUN0QixVQUFVLEdBQUcsS0FBSyxDQUFDO29CQUNuQixNQUFNO2FBQ2I7WUFDRCxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBRywyQ0FBVyxJQUFJLENBQUMsb0JBQW9CLFVBQUssVUFBVSxNQUFHLENBQUM7U0FDbEY7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSSw4Q0FBb0IsR0FBM0I7UUFDSSxJQUFNLFNBQVMsR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxDQUFDLENBQUMsQ0FBQztRQUNwRSxJQUFJLFNBQVMsQ0FBQyxNQUFNLEtBQUssbUNBQVcsQ0FBQyxPQUFPLEVBQUU7WUFDMUMsU0FBUyxDQUFDLE1BQU0sR0FBRyxtQ0FBVyxDQUFDLFNBQVMsQ0FBQztZQUV6QyxRQUFRO1lBQ1IsSUFBSSxJQUFJLENBQUMsb0JBQW9CLEdBQUcsSUFBSSxDQUFDLFdBQVcsRUFBRTtnQkFDOUMsSUFBTSxhQUFhLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsQ0FBQztnQkFDcEUsSUFBSSxhQUFhLENBQUMsTUFBTSxLQUFLLG1DQUFXLENBQUMsTUFBTSxFQUFFO29CQUM3QyxhQUFhLENBQUMsTUFBTSxHQUFHLG1DQUFXLENBQUMsT0FBTyxDQUFDO2lCQUM5QzthQUNKO1lBRUQsSUFBSSxDQUFDLHNCQUFzQixFQUFFLENBQUM7WUFDOUIsSUFBSSxDQUFDLGlCQUFpQixFQUFFLENBQUM7U0FDNUI7SUFDTCxDQUFDO0lBelJEO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxVQUFVLENBQUM7dURBQ1M7SUFHakM7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQztvREFDTTtJQUd4QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsS0FBSyxDQUFDO3NEQUNRO0lBVFYsZUFBZTtRQURuQyxPQUFPO09BQ2EsZUFBZSxDQTZSbkM7SUFBRCxzQkFBQztDQTdSRCxBQTZSQyxDQTdSNEMsRUFBRSxDQUFDLFNBQVMsR0E2UnhEO2tCQTdSb0IsZUFBZSIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIi8vIExlYXJuIFR5cGVTY3JpcHQ6XG4vLyAgLSBodHRwczovL2RvY3MuY29jb3MuY29tL2NyZWF0b3IvMi40L21hbnVhbC9lbi9zY3JpcHRpbmcvdHlwZXNjcmlwdC5odG1sXG4vLyBMZWFybiBBdHRyaWJ1dGU6XG4vLyAgLSBodHRwczovL2RvY3MuY29jb3MuY29tL2NyZWF0b3IvMi40L21hbnVhbC9lbi9zY3JpcHRpbmcvcmVmZXJlbmNlL2F0dHJpYnV0ZXMuaHRtbFxuLy8gTGVhcm4gbGlmZS1jeWNsZSBjYWxsYmFja3M6XG4vLyAgLSBodHRwczovL2RvY3MuY29jb3MuY29tL2NyZWF0b3IvMi40L21hbnVhbC9lbi9zY3JpcHRpbmcvbGlmZS1jeWNsZS1jYWxsYmFja3MuaHRtbFxuXG5pbXBvcnQgeyBMZXZlbFN0YXR1cyB9IGZyb20gXCIuL0xldmVsL0xldmVsU2VsZWN0Q29udHJvbGxlclwiO1xuXG5jb25zdCB7IGNjY2xhc3MsIHByb3BlcnR5IH0gPSBjYy5fZGVjb3JhdG9yO1xuXG4vKipcbiAqIOWFs+WNoemAieaLqea8lOekuuaOp+WItuWZqFxuICog6L+Z5Liq6ISa5pys5bGV56S65LqG5aaC5L2V5Yib5bu65LiA5Liq5a6M5pW055qE5YWz5Y2h6YCJ5oup55WM6Z2iXG4gKi9cbkBjY2NsYXNzXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBMZXZlbFNlbGVjdERlbW8gZXh0ZW5kcyBjYy5Db21wb25lbnQge1xuXG4gICAgQHByb3BlcnR5KGNjLlNjcm9sbFZpZXcpXG4gICAgc2Nyb2xsVmlldzogY2MuU2Nyb2xsVmlldyA9IG51bGw7XG5cbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBjb250ZW50OiBjYy5Ob2RlID0gbnVsbDtcblxuICAgIEBwcm9wZXJ0eShjYy5MYWJlbClcbiAgICBpbmZvTGFiZWw6IGNjLkxhYmVsID0gbnVsbDtcblxuICAgIC8vIOWFs+WNoeaVsOaNrlxuICAgIHByaXZhdGUgbGV2ZWxEYXRhTGlzdDogYW55W10gPSBbXTtcbiAgICBwcml2YXRlIGN1cnJlbnRTZWxlY3RlZExldmVsOiBudW1iZXIgPSAxO1xuICAgIHByaXZhdGUgdG90YWxMZXZlbHM6IG51bWJlciA9IDMwO1xuICAgIHByaXZhdGUgbGV2ZWxJdGVtV2lkdGg6IG51bWJlciA9IDE1MDtcblxuICAgIC8vIOWFs+WNoeiKgueCueWIl+ihqFxuICAgIHByaXZhdGUgbGV2ZWxOb2RlczogY2MuTm9kZVtdID0gW107XG5cbiAgICBvbkxvYWQoKSB7XG4gICAgICAgIHRoaXMuaW5pdExldmVsRGF0YSgpO1xuICAgICAgICB0aGlzLmNyZWF0ZUxldmVsU2VsZWN0VUkoKTtcbiAgICB9XG5cbiAgICBzdGFydCgpIHtcbiAgICAgICAgdGhpcy5zY3JvbGxUb0xldmVsKHRoaXMuY3VycmVudFNlbGVjdGVkTGV2ZWwpO1xuICAgICAgICB0aGlzLnVwZGF0ZUluZm9EaXNwbGF5KCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5Yid5aeL5YyW5YWz5Y2h5pWw5o2uXG4gICAgICovXG4gICAgcHJpdmF0ZSBpbml0TGV2ZWxEYXRhKCkge1xuICAgICAgICB0aGlzLmxldmVsRGF0YUxpc3QgPSBbXTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDE7IGkgPD0gdGhpcy50b3RhbExldmVsczsgaSsrKSB7XG4gICAgICAgICAgICBsZXQgc3RhdHVzOiBMZXZlbFN0YXR1cztcbiAgICAgICAgICAgIGlmIChpID09PSAxKSB7XG4gICAgICAgICAgICAgICAgc3RhdHVzID0gTGV2ZWxTdGF0dXMuQ1VSUkVOVDsgLy8g56ys5LiA5YWz5Li65b2T5YmN5YWz5Y2hXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGkgPD0gMykge1xuICAgICAgICAgICAgICAgIHN0YXR1cyA9IExldmVsU3RhdHVzLkNPTVBMRVRFRDsgLy8g5YmNM+WFs+W3sumAmuWFs++8iOekuuS+i++8iVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBzdGF0dXMgPSBMZXZlbFN0YXR1cy5MT0NLRUQ7IC8vIOWFtuS7luWFs+WNoeacquino+mUgVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICB0aGlzLmxldmVsRGF0YUxpc3QucHVzaCh7XG4gICAgICAgICAgICAgICAgbGV2ZWxOdW1iZXI6IGksXG4gICAgICAgICAgICAgICAgc3RhdHVzOiBzdGF0dXNcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5Yib5bu65YWz5Y2h6YCJ5oupVUlcbiAgICAgKi9cbiAgICBwcml2YXRlIGNyZWF0ZUxldmVsU2VsZWN0VUkoKSB7XG4gICAgICAgIGlmICghdGhpcy5jb250ZW50KSB7XG4gICAgICAgICAgICBjYy5lcnJvcihcIkNvbnRlbnQgbm9kZSBpcyBub3QgYXNzaWduZWQhXCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5riF56m6546w5pyJ5YaF5a65XG4gICAgICAgIHRoaXMuY29udGVudC5yZW1vdmVBbGxDaGlsZHJlbigpO1xuICAgICAgICB0aGlzLmxldmVsTm9kZXMgPSBbXTtcblxuICAgICAgICAvLyDorqHnrpfmgLvlrr3luqZcbiAgICAgICAgY29uc3QgdG90YWxXaWR0aCA9ICh0aGlzLnRvdGFsTGV2ZWxzIC0gMSkgKiB0aGlzLmxldmVsSXRlbVdpZHRoICsgNjUwO1xuICAgICAgICB0aGlzLmNvbnRlbnQud2lkdGggPSB0b3RhbFdpZHRoO1xuXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy50b3RhbExldmVsczsgaSsrKSB7XG4gICAgICAgICAgICBjb25zdCBsZXZlbERhdGEgPSB0aGlzLmxldmVsRGF0YUxpc3RbaV07XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIOWIm+W7uuWFs+WNoeiKgueCuVxuICAgICAgICAgICAgY29uc3QgbGV2ZWxOb2RlID0gdGhpcy5jcmVhdGVMZXZlbE5vZGUobGV2ZWxEYXRhKTtcbiAgICAgICAgICAgIHRoaXMuY29udGVudC5hZGRDaGlsZChsZXZlbE5vZGUpO1xuICAgICAgICAgICAgdGhpcy5sZXZlbE5vZGVzLnB1c2gobGV2ZWxOb2RlKTtcblxuICAgICAgICAgICAgLy8g6K6+572u5L2N572uXG4gICAgICAgICAgICBjb25zdCBwb3NYID0gaSAqIHRoaXMubGV2ZWxJdGVtV2lkdGggLSB0b3RhbFdpZHRoIC8gMiArIDY1MCAvIDI7XG4gICAgICAgICAgICBsZXZlbE5vZGUuc2V0UG9zaXRpb24ocG9zWCwgMCk7XG5cbiAgICAgICAgICAgIC8vIOWIm+W7uui/nuaOpee6v++8iOmZpOS6huacgOWQjuS4gOS4quWFs+WNoe+8iVxuICAgICAgICAgICAgaWYgKGkgPCB0aGlzLnRvdGFsTGV2ZWxzIC0gMSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGxpbmVOb2RlID0gdGhpcy5jcmVhdGVMaW5lTm9kZSgpO1xuICAgICAgICAgICAgICAgIHRoaXMuY29udGVudC5hZGRDaGlsZChsaW5lTm9kZSk7XG4gICAgICAgICAgICAgICAgbGluZU5vZGUuc2V0UG9zaXRpb24ocG9zWCArIHRoaXMubGV2ZWxJdGVtV2lkdGggLyAyLCAwKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWIm+W7uuWFs+WNoeiKgueCuVxuICAgICAqL1xuICAgIHByaXZhdGUgY3JlYXRlTGV2ZWxOb2RlKGxldmVsRGF0YTogYW55KTogY2MuTm9kZSB7XG4gICAgICAgIGNvbnN0IG5vZGUgPSBuZXcgY2MuTm9kZShgTGV2ZWxfJHtsZXZlbERhdGEubGV2ZWxOdW1iZXJ9YCk7XG4gICAgICAgIFxuICAgICAgICAvLyDmt7vliqBTcHJpdGXnu4Tku7ZcbiAgICAgICAgbm9kZS5hZGRDb21wb25lbnQoY2MuU3ByaXRlKTtcbiAgICAgICAgXG4gICAgICAgIC8vIOa3u+WKoExhYmVs57uE5Lu25pi+56S65YWz5Y2h5pWw5a2XXG4gICAgICAgIGNvbnN0IGxhYmVsTm9kZSA9IG5ldyBjYy5Ob2RlKFwiTGV2ZWxMYWJlbFwiKTtcbiAgICAgICAgY29uc3QgbGFiZWwgPSBsYWJlbE5vZGUuYWRkQ29tcG9uZW50KGNjLkxhYmVsKTtcbiAgICAgICAgbGFiZWwuc3RyaW5nID0gbGV2ZWxEYXRhLmxldmVsTnVtYmVyLnRvU3RyaW5nKCk7XG4gICAgICAgIGxhYmVsLmZvbnRTaXplID0gMjA7XG4gICAgICAgIGxhYmVsLm5vZGUuY29sb3IgPSBjYy5Db2xvci5XSElURTtcbiAgICAgICAgbGFiZWxOb2RlLnBhcmVudCA9IG5vZGU7XG5cbiAgICAgICAgLy8g5re75YqgQnV0dG9u57uE5Lu2XG4gICAgICAgIGNvbnN0IGJ1dHRvbiA9IG5vZGUuYWRkQ29tcG9uZW50KGNjLkJ1dHRvbik7XG4gICAgICAgIGJ1dHRvbi50YXJnZXQgPSBub2RlO1xuXG4gICAgICAgIC8vIOiuvue9rueCueWHu+S6i+S7tlxuICAgICAgICBub2RlLm9uKCdjbGljaycsICgpID0+IHtcbiAgICAgICAgICAgIHRoaXMub25MZXZlbENsaWNrZWQobGV2ZWxEYXRhLmxldmVsTnVtYmVyKTtcbiAgICAgICAgfSwgdGhpcyk7XG5cbiAgICAgICAgLy8g5pu05paw5YWz5Y2h5aSW6KeCXG4gICAgICAgIHRoaXMudXBkYXRlTGV2ZWxOb2RlQXBwZWFyYW5jZShub2RlLCBsZXZlbERhdGEsIGZhbHNlKTtcblxuICAgICAgICByZXR1cm4gbm9kZTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDliJvlu7rov57mjqXnur/oioLngrlcbiAgICAgKi9cbiAgICBwcml2YXRlIGNyZWF0ZUxpbmVOb2RlKCk6IGNjLk5vZGUge1xuICAgICAgICBjb25zdCBub2RlID0gbmV3IGNjLk5vZGUoXCJMaW5lXCIpO1xuICAgICAgICBjb25zdCBzcHJpdGUgPSBub2RlLmFkZENvbXBvbmVudChjYy5TcHJpdGUpO1xuICAgICAgICBcbiAgICAgICAgLy8g5Yqg6L296L+e5o6l57q/5Zu+54mHXG4gICAgICAgIGNjLnJlc291cmNlcy5sb2FkKFwiaGFsbF9wYWdlX3Jlcy9MZXZlbF9CdG4vcG9wX2xpbmVcIiwgY2MuU3ByaXRlRnJhbWUsIChlcnIsIHNwcml0ZUZyYW1lKSA9PiB7XG4gICAgICAgICAgICBpZiAoIWVyciAmJiBzcHJpdGVGcmFtZSkge1xuICAgICAgICAgICAgICAgIHNwcml0ZS5zcHJpdGVGcmFtZSA9IHNwcml0ZUZyYW1lIGFzIGNjLlNwcml0ZUZyYW1lO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcblxuICAgICAgICByZXR1cm4gbm9kZTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmm7TmlrDlhbPljaHoioLngrnlpJbop4JcbiAgICAgKi9cbiAgICBwcml2YXRlIHVwZGF0ZUxldmVsTm9kZUFwcGVhcmFuY2Uobm9kZTogY2MuTm9kZSwgbGV2ZWxEYXRhOiBhbnksIGlzU2VsZWN0ZWQ6IGJvb2xlYW4pIHtcbiAgICAgICAgY29uc3Qgc3ByaXRlID0gbm9kZS5nZXRDb21wb25lbnQoY2MuU3ByaXRlKTtcbiAgICAgICAgaWYgKCFzcHJpdGUpIHJldHVybjtcblxuICAgICAgICBsZXQgaW1hZ2VQYXRoID0gXCJcIjtcbiAgICAgICAgbGV0IHNpemUgPSBjYy5zaXplKDQ2LCA0Nik7XG5cbiAgICAgICAgLy8g5qC55o2u54q25oCB5ZKM5piv5ZCm6YCJ5Lit56Gu5a6a5Zu+54mH6Lev5b6EXG4gICAgICAgIGlmIChpc1NlbGVjdGVkKSB7XG4gICAgICAgICAgICBzaXplID0gY2Muc2l6ZSg4NiwgODYpO1xuICAgICAgICAgICAgc3dpdGNoIChsZXZlbERhdGEuc3RhdHVzKSB7XG4gICAgICAgICAgICAgICAgY2FzZSBMZXZlbFN0YXR1cy5MT0NLRUQ6XG4gICAgICAgICAgICAgICAgICAgIGltYWdlUGF0aCA9IFwiaGFsbF9wYWdlX3Jlcy9MZXZlbF9CdG4vcG9wX2dyYXlfY2hvb3NlXCI7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgTGV2ZWxTdGF0dXMuQ1VSUkVOVDpcbiAgICAgICAgICAgICAgICAgICAgaW1hZ2VQYXRoID0gXCJoYWxsX3BhZ2VfcmVzL0xldmVsX0J0bi9wb3BfeWVsbG93X2Nob29zZVwiO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIExldmVsU3RhdHVzLkNPTVBMRVRFRDpcbiAgICAgICAgICAgICAgICAgICAgaW1hZ2VQYXRoID0gXCJoYWxsX3BhZ2VfcmVzL0xldmVsX0J0bi9wb3BfZ3JlZW5fY2hvb3NlXCI7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgc3dpdGNoIChsZXZlbERhdGEuc3RhdHVzKSB7XG4gICAgICAgICAgICAgICAgY2FzZSBMZXZlbFN0YXR1cy5MT0NLRUQ6XG4gICAgICAgICAgICAgICAgICAgIGltYWdlUGF0aCA9IFwiaGFsbF9wYWdlX3Jlcy9MZXZlbF9CdG4vcG9wX2dyYXlcIjtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSBMZXZlbFN0YXR1cy5DVVJSRU5UOlxuICAgICAgICAgICAgICAgICAgICBpbWFnZVBhdGggPSBcImhhbGxfcGFnZV9yZXMvTGV2ZWxfQnRuL3BvcF95ZWxsb3dcIjtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSBMZXZlbFN0YXR1cy5DT01QTEVURUQ6XG4gICAgICAgICAgICAgICAgICAgIGltYWdlUGF0aCA9IFwiaGFsbF9wYWdlX3Jlcy9MZXZlbF9CdG4vcG9wX2dyZWVuXCI7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8g6K6+572u6IqC54K55aSn5bCPXG4gICAgICAgIG5vZGUuc2V0Q29udGVudFNpemUoc2l6ZSk7XG5cbiAgICAgICAgLy8g5Yqg6L295bm26K6+572u5Zu+54mHXG4gICAgICAgIGNjLnJlc291cmNlcy5sb2FkKGltYWdlUGF0aCwgY2MuU3ByaXRlRnJhbWUsIChlcnIsIHNwcml0ZUZyYW1lKSA9PiB7XG4gICAgICAgICAgICBpZiAoIWVyciAmJiBzcHJpdGVGcmFtZSkge1xuICAgICAgICAgICAgICAgIHNwcml0ZS5zcHJpdGVGcmFtZSA9IHNwcml0ZUZyYW1lIGFzIGNjLlNwcml0ZUZyYW1lO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmm7TmlrDmiYDmnInlhbPljaHmmL7npLpcbiAgICAgKi9cbiAgICBwcml2YXRlIHVwZGF0ZUFsbExldmVsc0Rpc3BsYXkoKSB7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5sZXZlbE5vZGVzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBjb25zdCBub2RlID0gdGhpcy5sZXZlbE5vZGVzW2ldO1xuICAgICAgICAgICAgY29uc3QgbGV2ZWxEYXRhID0gdGhpcy5sZXZlbERhdGFMaXN0W2ldO1xuICAgICAgICAgICAgY29uc3QgaXNTZWxlY3RlZCA9IChsZXZlbERhdGEubGV2ZWxOdW1iZXIgPT09IHRoaXMuY3VycmVudFNlbGVjdGVkTGV2ZWwpO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICB0aGlzLnVwZGF0ZUxldmVsTm9kZUFwcGVhcmFuY2Uobm9kZSwgbGV2ZWxEYXRhLCBpc1NlbGVjdGVkKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOa7muWKqOWIsOaMh+WumuWFs+WNoVxuICAgICAqL1xuICAgIHByaXZhdGUgc2Nyb2xsVG9MZXZlbChsZXZlbE51bWJlcjogbnVtYmVyKSB7XG4gICAgICAgIGlmIChsZXZlbE51bWJlciA8IDEgfHwgbGV2ZWxOdW1iZXIgPiB0aGlzLnRvdGFsTGV2ZWxzKSByZXR1cm47XG5cbiAgICAgICAgY29uc3QgdGFyZ2V0SW5kZXggPSBsZXZlbE51bWJlciAtIDE7XG4gICAgICAgIGNvbnN0IGNvbnRlbnRXaWR0aCA9IHRoaXMuY29udGVudC53aWR0aDtcbiAgICAgICAgY29uc3Qgc2Nyb2xsVmlld1dpZHRoID0gdGhpcy5zY3JvbGxWaWV3Lm5vZGUud2lkdGg7XG4gICAgICAgIFxuICAgICAgICAvLyDorqHnrpfnm67moIfkvY3nva7nmoTlgY/np7vph49cbiAgICAgICAgY29uc3QgdGFyZ2V0T2Zmc2V0ID0gKHRhcmdldEluZGV4ICogdGhpcy5sZXZlbEl0ZW1XaWR0aCkgLyAoY29udGVudFdpZHRoIC0gc2Nyb2xsVmlld1dpZHRoKTtcbiAgICAgICAgXG4gICAgICAgIC8vIOmZkOWItuWBj+enu+mHj+WcqOacieaViOiMg+WbtOWGhVxuICAgICAgICBjb25zdCBjbGFtcGVkT2Zmc2V0ID0gY2MubWlzYy5jbGFtcGYodGFyZ2V0T2Zmc2V0LCAwLCAxKTtcbiAgICAgICAgXG4gICAgICAgIC8vIOiuvue9rua7muWKqOS9jee9rlxuICAgICAgICB0aGlzLnNjcm9sbFZpZXcuc2Nyb2xsVG9QZXJjZW50SG9yaXpvbnRhbChjbGFtcGVkT2Zmc2V0LCAwLjMpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWFs+WNoeeCueWHu+S6i+S7tuWkhOeQhlxuICAgICAqL1xuICAgIHByaXZhdGUgb25MZXZlbENsaWNrZWQobGV2ZWxOdW1iZXI6IG51bWJlcikge1xuICAgICAgICAvLyDmo4Dmn6XlhbPljaHmmK/lkKblj6/ku6XpgInmi6nvvIjmnKrplIHlrprvvIlcbiAgICAgICAgY29uc3QgbGV2ZWxEYXRhID0gdGhpcy5sZXZlbERhdGFMaXN0W2xldmVsTnVtYmVyIC0gMV07XG4gICAgICAgIGlmIChsZXZlbERhdGEuc3RhdHVzID09PSBMZXZlbFN0YXR1cy5MT0NLRUQpIHtcbiAgICAgICAgICAgIGNjLmxvZyhg5YWz5Y2hICR7bGV2ZWxOdW1iZXJ9IOacquino+mUge+8gWApO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5pu05paw5b2T5YmN6YCJ5Lit5YWz5Y2hXG4gICAgICAgIHRoaXMuY3VycmVudFNlbGVjdGVkTGV2ZWwgPSBsZXZlbE51bWJlcjtcbiAgICAgICAgdGhpcy51cGRhdGVBbGxMZXZlbHNEaXNwbGF5KCk7XG4gICAgICAgIFxuICAgICAgICAvLyDmu5rliqjliLDpgInkuK3lhbPljaFcbiAgICAgICAgdGhpcy5zY3JvbGxUb0xldmVsKGxldmVsTnVtYmVyKTtcblxuICAgICAgICB0aGlzLnVwZGF0ZUluZm9EaXNwbGF5KCk7XG4gICAgICAgIGNjLmxvZyhg6YCJ5oup5YWz5Y2hOiAke2xldmVsTnVtYmVyfWApO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOabtOaWsOS/oeaBr+aYvuekulxuICAgICAqL1xuICAgIHByaXZhdGUgdXBkYXRlSW5mb0Rpc3BsYXkoKSB7XG4gICAgICAgIGlmICh0aGlzLmluZm9MYWJlbCkge1xuICAgICAgICAgICAgY29uc3QgbGV2ZWxEYXRhID0gdGhpcy5sZXZlbERhdGFMaXN0W3RoaXMuY3VycmVudFNlbGVjdGVkTGV2ZWwgLSAxXTtcbiAgICAgICAgICAgIGxldCBzdGF0dXNUZXh0ID0gXCJcIjtcbiAgICAgICAgICAgIHN3aXRjaCAobGV2ZWxEYXRhLnN0YXR1cykge1xuICAgICAgICAgICAgICAgIGNhc2UgTGV2ZWxTdGF0dXMuTE9DS0VEOlxuICAgICAgICAgICAgICAgICAgICBzdGF0dXNUZXh0ID0gXCLmnKrop6PplIFcIjtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSBMZXZlbFN0YXR1cy5DVVJSRU5UOlxuICAgICAgICAgICAgICAgICAgICBzdGF0dXNUZXh0ID0gXCLov5vooYzkuK1cIjtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSBMZXZlbFN0YXR1cy5DT01QTEVURUQ6XG4gICAgICAgICAgICAgICAgICAgIHN0YXR1c1RleHQgPSBcIuW3sumAmuWFs1wiO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuaW5mb0xhYmVsLnN0cmluZyA9IGDlvZPliY3pgInkuK06IOWFs+WNoSR7dGhpcy5jdXJyZW50U2VsZWN0ZWRMZXZlbH0gKCR7c3RhdHVzVGV4dH0pYDtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWujOaIkOW9k+WJjeWFs+WNoe+8iOa1i+ivleeUqO+8iVxuICAgICAqL1xuICAgIHB1YmxpYyBjb21wbGV0ZUN1cnJlbnRMZXZlbCgpIHtcbiAgICAgICAgY29uc3QgbGV2ZWxEYXRhID0gdGhpcy5sZXZlbERhdGFMaXN0W3RoaXMuY3VycmVudFNlbGVjdGVkTGV2ZWwgLSAxXTtcbiAgICAgICAgaWYgKGxldmVsRGF0YS5zdGF0dXMgPT09IExldmVsU3RhdHVzLkNVUlJFTlQpIHtcbiAgICAgICAgICAgIGxldmVsRGF0YS5zdGF0dXMgPSBMZXZlbFN0YXR1cy5DT01QTEVURUQ7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIOino+mUgeS4i+S4gOWFs1xuICAgICAgICAgICAgaWYgKHRoaXMuY3VycmVudFNlbGVjdGVkTGV2ZWwgPCB0aGlzLnRvdGFsTGV2ZWxzKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgbmV4dExldmVsRGF0YSA9IHRoaXMubGV2ZWxEYXRhTGlzdFt0aGlzLmN1cnJlbnRTZWxlY3RlZExldmVsXTtcbiAgICAgICAgICAgICAgICBpZiAobmV4dExldmVsRGF0YS5zdGF0dXMgPT09IExldmVsU3RhdHVzLkxPQ0tFRCkge1xuICAgICAgICAgICAgICAgICAgICBuZXh0TGV2ZWxEYXRhLnN0YXR1cyA9IExldmVsU3RhdHVzLkNVUlJFTlQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgXG4gICAgICAgICAgICB0aGlzLnVwZGF0ZUFsbExldmVsc0Rpc3BsYXkoKTtcbiAgICAgICAgICAgIHRoaXMudXBkYXRlSW5mb0Rpc3BsYXkoKTtcbiAgICAgICAgfVxuICAgIH1cbn1cbiJdfQ==