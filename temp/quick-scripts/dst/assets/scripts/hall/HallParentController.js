
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/HallParentController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c6e47rrbmlLFbrYAuFsjy0x', 'HallParentController');
// scripts/hall/HallParentController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Publish_1 = require("../../meshTools/tools/Publish");
var GlobalBean_1 = require("../bean/GlobalBean");
var GameMgr_1 = require("../common/GameMgr");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var ToastController_1 = require("../ToastController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var HallCenterLayController_1 = require("./HallCenterLayController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HallParentController = /** @class */ (function (_super) {
    __extends(HallParentController, _super);
    function HallParentController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardTitle = null;
        _this.boardIcon = null;
        _this.beansNumber = null;
        _this.boardBtnBack = null; //返回按钮
        _this.boardBtnInfo = null; //游戏简介按钮
        _this.boardBtnSetting = null; //设置按钮
        _this.hallCenterLayController = null;
        _this.toastController = null; //toast 的布局
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    HallParentController.prototype.onEnable = function () {
        this.updateGold();
    };
    HallParentController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.setTouchEvent(this.boardTitle, function () {
            GameMgr_1.GameMgr.H5SDK.ShowAppShop();
        });
        if (Publish_1.Publish.GetInstance().currencyIcon != null && Publish_1.Publish.GetInstance().currencyIcon !== '') {
            Tools_1.Tools.setNodeSpriteFrameUrl(this.boardIcon, Publish_1.Publish.GetInstance().currencyIcon);
        }
        Tools_1.Tools.imageButtonClick(this.boardBtnBack, Config_1.Config.buttonRes + 'board_btn_back_normal', Config_1.Config.buttonRes + 'board_btn_back_pressed', function () {
            var type = _this.hallCenterLayController.getCenterLaytouType();
            switch (type) {
                case HallCenterLayController_1.CenterLaytouType.HALL_AUTO_VIEW:
                    if (_this.backClick) {
                        _this.backClick();
                    }
                    break;
                case HallCenterLayController_1.CenterLaytouType.HALL_CREAT_ROOM_VIEW:
                    // this.hallCenterLayController.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)
                    //发送离开房间的信息
                    WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLeaveInvite, {});
                    break;
                case HallCenterLayController_1.CenterLaytouType.HALL_JOIN_ROOM_VIEW:
                    _this.hallCenterLayController.setCenterLaytouType(HallCenterLayController_1.CenterLaytouType.HALL_AUTO_VIEW);
                    break;
            }
        });
        Tools_1.Tools.imageButtonClick(this.boardBtnInfo, Config_1.Config.buttonRes + 'board_btn_info_normal', Config_1.Config.buttonRes + 'board_btn_info_pressed', function () {
            if (_this.infoClick) {
                _this.infoClick();
            }
        });
        Tools_1.Tools.imageButtonClick(this.boardBtnSetting, Config_1.Config.buttonRes + 'board_btn_setting_normal', Config_1.Config.buttonRes + 'board_btn_setting_pressed', function () {
            if (_this.settingClick) {
                _this.settingClick();
            }
        });
        this.hallCenterLayController.setClick(function () {
            //start 按钮的点击回调
            if (_this.startClick) {
                _this.startClick();
            }
        }, function () {
            if (_this.createClick) {
                _this.createClick();
            }
        }, function (userId, nickname) {
            _this.seatCallback(userId, nickname);
        });
    };
    //设置按钮点击事件的回调的
    HallParentController.prototype.setClick = function (backClick, infoClick, settingClick, startClick, createClick, seatCallback) {
        this.backClick = backClick;
        this.infoClick = infoClick;
        this.settingClick = settingClick;
        this.startClick = startClick;
        this.createClick = createClick;
        this.seatCallback = seatCallback;
    };
    //更新金币数量
    HallParentController.prototype.updateGold = function () {
        if (GlobalBean_1.GlobalBean.GetInstance().loginData) {
            this.beansNumber.string = Tools_1.Tools.NumToTBMK(GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin);
        }
    };
    //房间已经解散但是自己重连回来被遗留在 房间中的处理
    HallParentController.prototype.exitTheRoom = function () {
        var type = this.hallCenterLayController.getCenterLaytouType();
        if (type === HallCenterLayController_1.CenterLaytouType.HALL_CREAT_ROOM_VIEW) {
            this.toastController.showContent(window.getLocalizedStr('LeaveRoom'));
            this.hallCenterLayController.setCenterLaytouType(HallCenterLayController_1.CenterLaytouType.HALL_AUTO_VIEW);
        }
    };
    //设置接受邀请成功
    HallParentController.prototype.setAcceptInvite = function (acceptInvite) {
        this.hallCenterLayController.setAcceptInvite(acceptInvite);
    };
    //离开房间
    HallParentController.prototype.leaveRoom = function (noticeLeaveInvite) {
        this.hallCenterLayController.leaveRoom(noticeLeaveInvite);
    };
    //设置门票
    HallParentController.prototype.setFees = function () {
        this.hallCenterLayController.setFees();
    };
    //进入私人房间
    HallParentController.prototype.joinCreateRoom = function () {
        this.hallCenterLayController.joinCreateRoom();
    };
    HallParentController.prototype.joinError = function () {
        this.hallCenterLayController.joinError();
    };
    //准备 取消准备
    HallParentController.prototype.setReadyState = function (noticeUserInviteStatus) {
        this.hallCenterLayController.setReadyState(noticeUserInviteStatus);
    };
    __decorate([
        property(cc.Node)
    ], HallParentController.prototype, "boardTitle", void 0);
    __decorate([
        property(cc.Node)
    ], HallParentController.prototype, "boardIcon", void 0);
    __decorate([
        property(cc.Label)
    ], HallParentController.prototype, "beansNumber", void 0);
    __decorate([
        property(cc.Node)
    ], HallParentController.prototype, "boardBtnBack", void 0);
    __decorate([
        property(cc.Node)
    ], HallParentController.prototype, "boardBtnInfo", void 0);
    __decorate([
        property(cc.Node)
    ], HallParentController.prototype, "boardBtnSetting", void 0);
    __decorate([
        property(HallCenterLayController_1.default)
    ], HallParentController.prototype, "hallCenterLayController", void 0);
    __decorate([
        property(ToastController_1.default)
    ], HallParentController.prototype, "toastController", void 0);
    HallParentController = __decorate([
        ccclass
    ], HallParentController);
    return HallParentController;
}(cc.Component));
exports.default = HallParentController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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