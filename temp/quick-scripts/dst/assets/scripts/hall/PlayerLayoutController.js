
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/PlayerLayoutController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '22e43laKY5NvZOL2nMmyHfN', 'PlayerLayoutController');
// scripts/hall/PlayerLayoutController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerLayoutController = /** @class */ (function (_super) {
    __extends(PlayerLayoutController, _super);
    function PlayerLayoutController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.player2 = null; //玩家人数是 2 的节点
        _this.player3 = null; //玩家人数是 3 的节点
        _this.player4 = null; //玩家人数是 4 的节点
        _this.player5 = null; //玩家人数是 5 的节点
        _this.sliding = null; //选中玩家人数的滑动条
        //移动时间
        _this.moveTime = 0.1;
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    PlayerLayoutController.prototype.onEnable = function () {
        var players = GlobalBean_1.GlobalBean.GetInstance().players;
        switch (players) {
            case 2:
                this.movePlayersliding(this.player2);
                break;
            case 3:
                this.movePlayersliding(this.player3);
                break;
            case 4:
                this.movePlayersliding(this.player4);
                break;
            case 5:
                this.movePlayersliding(this.player5);
                break;
        }
    };
    PlayerLayoutController.prototype.start = function () {
        this.setListener(this.player2, 2);
        this.setListener(this.player3, 3);
        this.setListener(this.player4, 4);
        this.setListener(this.player5, 5);
    };
    //设置监听
    PlayerLayoutController.prototype.setListener = function (playerNode, players) {
        var _this = this;
        Tools_1.Tools.setTouchEvent(playerNode, function () {
            GlobalBean_1.GlobalBean.GetInstance().players = players;
            _this.movePlayersliding(playerNode);
        });
    };
    //移动滑动条
    PlayerLayoutController.prototype.movePlayersliding = function (playerNode) {
        cc.tween(this.sliding)
            .to(this.moveTime, { position: playerNode.position })
            .start(); // 开始动画
    };
    __decorate([
        property(cc.Node)
    ], PlayerLayoutController.prototype, "player2", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerLayoutController.prototype, "player3", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerLayoutController.prototype, "player4", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerLayoutController.prototype, "player5", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerLayoutController.prototype, "sliding", void 0);
    PlayerLayoutController = __decorate([
        ccclass
    ], PlayerLayoutController);
    return PlayerLayoutController;
}(cc.Component));
exports.default = PlayerLayoutController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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