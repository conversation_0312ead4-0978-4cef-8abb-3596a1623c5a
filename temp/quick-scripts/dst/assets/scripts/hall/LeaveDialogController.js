
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/LeaveDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'dae83aDtN5Oe6VqQSxT8XBO', 'LeaveDialogController');
// scripts/hall/LeaveDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LeaveDialogController = /** @class */ (function (_super) {
    __extends(LeaveDialogController, _super);
    function LeaveDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        _this.cancelBtn = null;
        _this.leaveBtn = null;
        _this.tipContent = null;
        _this.type = 0; //0是完全退出这个游戏 1是退出本局游戏
        _this.backCallback = null; //隐藏弹窗的回调
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    LeaveDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        //cancel 按钮点击事件
        Tools_1.Tools.yellowButton(this.cancelBtn, function () {
            _this.hide();
        });
        //leave 按钮点击事件
        Tools_1.Tools.redButton(this.leaveBtn, function () {
            if (_this.type === 0) {
                GameMgr_1.GameMgr.H5SDK.CloseWebView(); //这个是退出游戏的
            }
            else {
                //这个是退出本局游戏的
                _this.hide();
                //退出当前房间游戏
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLeaveRoom, { 'isConfirmLeave': true });
            }
        });
    };
    LeaveDialogController.prototype.show = function (type, backCallback) {
        this.type = type;
        this.backCallback = backCallback;
        if (type === 0) {
            this.tipContent.string = window.getLocalizedStr('ExitApplication'); //显示完全退出的文案退出
        }
        else {
            this.tipContent.string = window.getLocalizedStr('QuitTheGame'); //显示退出本局游戏的文案
        }
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    LeaveDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "cancelBtn", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "leaveBtn", void 0);
    __decorate([
        property(cc.Label)
    ], LeaveDialogController.prototype, "tipContent", void 0);
    LeaveDialogController = __decorate([
        ccclass
    ], LeaveDialogController);
    return LeaveDialogController;
}(cc.Component));
exports.default = LeaveDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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