
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/util/NickNameLabel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'dd93eSTJ+ZIS7Rn/Y6PWVpR', 'NickNameLabel');
// scripts/util/NickNameLabel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, requireComponent = _a.requireComponent;
var NickNameLabel = /** @class */ (function (_super) {
    __extends(NickNameLabel, _super);
    function NickNameLabel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.MaxWidth = 0;
        _this._labelComp = null;
        return _this;
    }
    Object.defineProperty(NickNameLabel.prototype, "_label", {
        get: function () {
            if (this._labelComp == null) {
                this._labelComp = this.node.getComponent(cc.Label);
            }
            return this._labelComp;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(NickNameLabel.prototype, "string", {
        get: function () {
            return this._label.string;
        },
        set: function (value) {
            var overflow = this._label.overflow;
            value = this.processNickname(value, this.MaxWidth);
            this._label.string = value;
            this._label.overflow = overflow;
        },
        enumerable: false,
        configurable: true
    });
    NickNameLabel.prototype.getTextWidth = function (text) {
        this._label.string = text;
        // @ts-ignore
        this._label._forceUpdateRenderData();
        return this.node.width;
    };
    NickNameLabel.prototype.processNickname = function (nickname, max) {
        this._label.overflow = cc.Label.Overflow.NONE;
        var nickNameWidth = this.getTextWidth(nickname);
        if (nickNameWidth > max) {
            // 这里使用二分查找来找到最接近且不超出标签宽度的截断位置
            var left = 0;
            var right = nickname.length;
            var result = 0;
            while (left <= right) {
                var mid = Math.floor((left + right) / 2);
                var midString = nickname.substring(0, mid) + '...';
                var midWidth = this.getTextWidth(midString);
                if (midWidth <= max) {
                    result = mid;
                    left = mid + 1;
                }
                else {
                    right = mid - 1;
                }
            }
            return nickname.substring(0, result) + '...';
        }
        return nickname;
    };
    __decorate([
        property
    ], NickNameLabel.prototype, "MaxWidth", void 0);
    NickNameLabel = __decorate([
        ccclass,
        requireComponent(cc.Label)
    ], NickNameLabel);
    return NickNameLabel;
}(cc.Component));
exports.default = NickNameLabel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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