
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/util/Tools.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '617b5KPPRpFBp3JOQImy4wR', 'Tools');
// scripts/util/Tools.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tools = void 0;
var AudioManager_1 = require("./AudioManager");
var Config_1 = require("./Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var Tools = /** @class */ (function () {
    function Tools() {
    }
    Tools.setTouchEvent = function (peopleNode, startFunction, endFunction, cancelFunction) {
        this.setTouchEventParent(peopleNode, true, startFunction, endFunction, cancelFunction);
    };
    Tools.setGameTouchEvent = function (peopleNode, startFunction, endFunction, cancelFunction) {
        this.setTouchEventParent(peopleNode, false, startFunction, endFunction, cancelFunction);
    };
    //添加点击事件 
    //isSound 是否需要按键音效，大厅的都需要 游戏内有自己的按键音所以不需要
    //peopleNode 节点
    //startFunction 按下事件
    //endFunction 抬起事件
    //cancelFunction 取消事件
    Tools.setTouchEventParent = function (peopleNode, isSound, startFunction, endFunction, cancelFunction) {
        peopleNode.on(cc.Node.EventType.TOUCH_START, function (event) {
            if (isSound) {
                AudioManager_1.AudioManager.keyingToneAudio();
            }
            if (startFunction != null) {
                startFunction(peopleNode, event);
            }
        }, this);
        peopleNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            if (endFunction != null) {
                endFunction(peopleNode, event);
            }
        }, this);
        peopleNode.on(cc.Node.EventType.TOUCH_CANCEL, function (event) {
            if (cancelFunction != null) {
                cancelFunction(peopleNode, event);
            }
        }, this);
    };
    Tools.cancelTouchStartListener = function (peopleNode) {
        peopleNode.off(cc.Node.EventType.TOUCH_START, this);
    };
    Tools.cancelTouchEndListener = function (peopleNode) {
        peopleNode.off(cc.Node.EventType.TOUCH_END, this);
    };
    Tools.cancelTouchCancelListener = function (peopleNode) {
        peopleNode.off(cc.Node.EventType.TOUCH_CANCEL, this);
    };
    //为精灵添加图片
    Tools.setNodeSpriteFrame = function (node, path) {
        cc.resources.load(path, cc.SpriteFrame, function (error, assets) {
            var sprite = node.getComponent(cc.Sprite);
            sprite.spriteFrame = assets;
        });
    };
    //添加网络图片
    Tools.setNodeSpriteFrameUrl = function (node, url) {
        if (!node) {
            return;
        }
        var avatarSp = node.getComponent(cc.Sprite);
        if (!avatarSp) {
            console.warn("⚠️ 节点没有Sprite组件，正在添加...");
            avatarSp = node.addComponent(cc.Sprite);
        }
        if (url == null || url == '') {
            console.warn("⚠️ URL为空，跳过图片加载");
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png'; // 默认
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u56FE\u7247\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 尝试加载备用图片或设置默认颜色
                Tools.setFallbackTexture(avatarSp);
                return;
            }
            texture.setPremultiplyAlpha(true); // 👈 关键设置
            texture.packable = false; //加载圆头像的时候 必须关闭合图
            avatarSp.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            node.active = true;
            node.opacity = 255;
        });
    };
    // 设置备用纹理
    Tools.setFallbackTexture = function (sprite) {
        // 创建一个简单的纯色纹理
        var texture = new cc.Texture2D();
        var color = [150, 150, 150, 255]; // 灰色
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
    };
    // 异步加载网络图片（带回调）
    Tools.setNodeSpriteFrameUrlAsync = function (node, url, onComplete) {
        if (!node) {
            console.error("❌ 节点为null，无法设置图片");
            if (onComplete)
                onComplete(false);
            return;
        }
        var avatarSp = node.getComponent(cc.Sprite);
        if (!avatarSp) {
            console.warn("⚠️ 节点没有Sprite组件，正在添加...");
            avatarSp = node.addComponent(cc.Sprite);
        }
        if (url == null || url == '') {
            console.warn("⚠️ URL为空，跳过图片加载");
            if (onComplete)
                onComplete(false);
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png'; // 默认
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u56FE\u7247\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 尝试加载备用图片或设置默认颜色
                Tools.setFallbackTexture(avatarSp);
                if (onComplete)
                    onComplete(false);
                return;
            }
            texture.setPremultiplyAlpha(true); // 👈 关键设置
            texture.packable = false; //加载圆头像的时候 必须关闭合图
            avatarSp.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            node.active = true;
            node.opacity = 255;
            if (onComplete)
                onComplete(true);
        });
    };
    //红色按钮
    Tools.redButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnRedNormal, Config_1.Config.btnRedPressed, Config_1.Config.btnRedNormalColor, Config_1.Config.btnRedPressedColor, click, label);
    };
    //绿色按钮
    Tools.greenButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnGreenNormal, Config_1.Config.btnGreenPressed, Config_1.Config.btnGreenNormalColor, Config_1.Config.btnGreenPressedColor, click, label);
    };
    //黄色按钮
    Tools.yellowButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnYellowNormal, Config_1.Config.btnYellowPressed, Config_1.Config.btnYellowNormalColor, Config_1.Config.btnYellowPressedColor, click, label);
    };
    //灰色按钮
    Tools.grayButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnGrayNormal, Config_1.Config.btnGrayNormal, Config_1.Config.btnGrayNormalColor, Config_1.Config.btnGrayNormalColor, click, label);
    };
    //通用的按钮点击事件，带点击变颜色的
    Tools.buttonState = function (node, normalImg, pressedImg, normalColor, pressedColor, click, labelText) {
        var btnGreen = node.getChildByName('btn_color_normal'); //获取按钮背景节点
        var btnLabel = node.getChildByName('button_label'); //获取按钮文字节点
        var label = btnLabel.getComponent(cc.Label);
        var labelOutline = btnLabel.getComponent(cc.LabelOutline);
        if (labelText != null) {
            label.string = labelText;
        }
        Tools.setTouchEvent(btnGreen, function (node) {
            Tools.setNodeSpriteFrame(node, pressedImg);
            label.fontSize = 34;
            label.lineHeight = 34;
            var color = new cc.Color();
            cc.Color.fromHEX(color, pressedColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(), '#B3B3B3');
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, normalColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            if (click != null) {
                click();
            }
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, normalColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
        });
    };
    //点击变颜色的图片按钮
    Tools.imageButtonClick = function (node, normalImg, pressedImg, click) {
        Tools.setTouchEvent(node, function (node) {
            Tools.setNodeSpriteFrame(node, pressedImg);
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
            click();
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
        });
    };
    //格式化资金显示格式的
    Tools.NumToTBMK = function (num, digit, min) {
        var _a;
        if (digit === void 0) { digit = 1; }
        if (min === void 0) { min = 10000; }
        var intNum = num;
        if (intNum < min) {
            return intNum.toString();
        }
        var unitStrArr = ["T", "B", "M", "K"];
        var unitArr = [Math.pow(10, 12), Math.pow(10, 9), Math.pow(10, 6), Math.pow(10, 3)];
        for (var i = 0; i < unitArr.length; ++i) {
            var result = intNum / unitArr[i];
            if (result >= 1) {
                var str = result.toString();
                var strArr = str.split(".");
                var suffix = (_a = strArr[1]) !== null && _a !== void 0 ? _a : "";
                if (suffix.length >= digit) {
                    if (digit == 0) {
                        return strArr[0] + unitStrArr[i];
                    }
                    return strArr[0] + "." + suffix.substring(0, digit) + unitStrArr[i];
                }
                else {
                    var fillStr = new Array(digit - suffix.length).fill("0").join("");
                    return strArr[0] + "." + suffix + fillStr + unitStrArr[i];
                }
            }
        }
    };
    Tools.getCurrentTimeWithMilliseconds = function () {
        var currentDate = new Date();
        var year = currentDate.getFullYear();
        var month = String(currentDate.getMonth() + 1).padStart(2, '0');
        var day = String(currentDate.getDate()).padStart(2, '0');
        var hours = String(currentDate.getHours()).padStart(2, '0');
        var minutes = String(currentDate.getMinutes()).padStart(2, '0');
        var seconds = String(currentDate.getSeconds()).padStart(2, '0');
        var milliseconds = String(currentDate.getMilliseconds()).padStart(3, '0');
        return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds + "." + milliseconds;
    };
    //赋值文本到剪切板
    Tools.copyToClipboard = function (text) {
        var textarea = document.createElement('textarea');
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        try {
            var successful = document.execCommand('copy');
            if (successful) {
                console.log('文本已复制到剪切板');
            }
            else {
                console.error('复制到剪切板失败');
            }
        }
        catch (err) {
            console.error('复制到剪切板失败：', err);
        }
        document.body.removeChild(textarea);
    };
    //拆分数组用的，一个长度为 10 的数组 拆分成 2 个长度为 5 的数组 chunkArray(user_s, 5)
    Tools.chunkArray = function (arr, chunkSize) {
        var result = [];
        for (var i = 0; i < arr.length; i += chunkSize) {
            result.push(arr.slice(i, i + chunkSize));
        }
        return result;
    };
    //设置倒计时的秒数的位置
    Tools.setCountDownTimeLabel = function (buttonNode) {
        var btn = buttonNode.getChildByName('button_label');
        var timeBtn = buttonNode.getChildByName('buttonLabel_time');
        var xPos = btn.position.x + btn.width / 2 + timeBtn.width / 2;
        timeBtn.setPosition(xPos, 0);
    };
    return Tools;
}());
exports.Tools = Tools;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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