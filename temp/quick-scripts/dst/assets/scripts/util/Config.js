
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/util/Config.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '21e14lh3EZFpZ8wKzVoFPD8', 'Config');
// scripts/util/Config.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Config = void 0;
var Config = /** @class */ (function () {
    function Config() {
    }
    //resources 相关
    Config.buttonRes = 'button_res/'; //按钮相关的资源
    Config.hallRes = 'hall_res/'; //大厅的资源文件夹
    Config.gameRes = 'game_res/'; //游戏中的资源文件夹
    Config.audio = 'audio/'; //游戏中的音乐资源
    //按钮名称
    Config.btnRedNormal = Config.buttonRes + 'btn_red_normal';
    Config.btnRedPressed = Config.buttonRes + 'btn_red_pressed';
    Config.btnRedNormalColor = "#BA3207";
    Config.btnRedPressedColor = "#832305";
    Config.btnGreenNormal = Config.buttonRes + 'btn_green_normal';
    Config.btnGreenPressed = Config.buttonRes + 'btn_green_pressed';
    Config.btnGreenNormalColor = "#119C0F";
    Config.btnGreenPressedColor = "#0C6D0B";
    Config.btnYellowNormal = Config.buttonRes + 'btn_yellow_normal';
    Config.btnYellowPressed = Config.buttonRes + 'btn_yellow_pressed';
    Config.btnYellowNormalColor = "#CC6519";
    Config.btnYellowPressedColor = "#8F4611";
    Config.btnGrayNormal = Config.buttonRes + 'btn_gray_normal';
    Config.btnGrayNormalColor = "#7B7B7B";
    //game 相关
    Config.dialogScaleTime = 0.15; //弹窗的缩放时间
    Config.latticeNumber = 63; //格子数量
    Config.latticeNumberH = 7; //每一竖行格子数
    Config.latticeNumberW = 9; //每一横行格子数
    Config.flyTime = 0.3; //飞行动画时间 s
    Config.huojianAndZhadanSkillTime = 0.65; //火箭和炸弹技能动画时间 s
    return Config;
}());
exports.Config = Config;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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