
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/util/BlockingQueue.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3ff90WyQGFLYZ+OEda8zbs/', 'BlockingQueue');
// scripts/util/BlockingQueue.ts

var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var BlockingQueue = /** @class */ (function () {
    // 私有构造函数，防止外部直接创建实例
    function BlockingQueue(capacity) {
        this.queue = [];
        this.waitingConsumers = [];
        this.waitingProducers = [];
        this.capacity = capacity;
    }
    // 获取单例实例
    BlockingQueue.getInstance = function (capacity) {
        if (!BlockingQueue.instance) {
            BlockingQueue.instance = new BlockingQueue(capacity);
        }
        return BlockingQueue.instance;
    };
    // 向队列添加元素
    BlockingQueue.prototype.enqueue = function (item) {
        return __awaiter(this, void 0, Promise, function () {
            var consumer;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(this.queue.length >= this.capacity)) return [3 /*break*/, 2];
                        // 队列已满，等待直到有空间
                        return [4 /*yield*/, new Promise(function (resolve) { return _this.waitingProducers.push(resolve); })];
                    case 1:
                        // 队列已满，等待直到有空间
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        this.queue.push(item);
                        // 如果有消费者等待，唤醒一个消费者
                        if (this.waitingConsumers.length > 0) {
                            consumer = this.waitingConsumers.shift();
                            consumer === null || consumer === void 0 ? void 0 : consumer();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 从队列中取出元素
    BlockingQueue.prototype.dequeue = function () {
        return __awaiter(this, void 0, Promise, function () {
            var item, producer;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(this.queue.length === 0)) return [3 /*break*/, 2];
                        // 队列为空，等待直到有元素
                        return [4 /*yield*/, new Promise(function (resolve) { return _this.waitingConsumers.push(resolve); })];
                    case 1:
                        // 队列为空，等待直到有元素
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        item = this.queue.shift();
                        // 如果有生产者等待，唤醒一个生产者
                        if (this.waitingProducers.length > 0) {
                            producer = this.waitingProducers.shift();
                            producer === null || producer === void 0 ? void 0 : producer();
                        }
                        return [2 /*return*/, item];
                }
            });
        });
    };
    // 队列的当前大小
    BlockingQueue.prototype.size = function () {
        return this.queue.length;
    };
    // 队列是否已满
    BlockingQueue.prototype.isFull = function () {
        return this.queue.length >= this.capacity;
    };
    // 队列是否为空
    BlockingQueue.prototype.isEmpty = function () {
        return this.queue.length === 0;
    };
    BlockingQueue.instance = null;
    return BlockingQueue;
}());

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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