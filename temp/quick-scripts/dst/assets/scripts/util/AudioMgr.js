
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/util/AudioMgr.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a2ba8VmZJlPZ4hHcfCOQPX0', 'AudioMgr');
// scripts/util/AudioMgr.ts

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioMgr = void 0;
var Config_1 = require("./Config");
var Dictionary_1 = require("./Dictionary");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**最多有几个音效播放器*/
var MAX_SOUNDS = 8;
/**
 * 音效管理器
 */
var AudioMgr = /** @class */ (function () {
    function AudioMgr() {
        /**音效的常驻节点*/
        this._persistRootNode = null;
        //bgm音效全局唯一一个
        this._music = null;
        //sound音效可以有多个
        this._sounds = null;
        /**bgm静音 0没静音 1静音*/
        this._music_muted = 0;
        /**sound静音 0没静音 1静音*/
        this._sound_muted = 0;
        /**bgm音量*/
        this._music_volume = 1;
        /**sound音量*/
        this._sound_volume = 1;
        /**当前播放的音效索引用以控制使用不同的AudioSource*/
        this._now_soundid = 0;
        /**当前播放的bgm音效名字*/
        this._cur_music_name = "";
    }
    AudioMgr_1 = AudioMgr;
    Object.defineProperty(AudioMgr, "ins", {
        get: function () {
            if (this._instance) {
                return this._instance;
            }
            this._instance = new AudioMgr_1();
            return this._instance;
        },
        enumerable: false,
        configurable: true
    });
    AudioMgr.prototype.init = function () {
        if (this._persistRootNode)
            return; //避免切换场景初始化报错
        this._persistRootNode = cc.find("DataNode"); //这个节点是常驻节点，如果不是的话 就用下面那行代码把它变成常驻节点
        cc.game.addPersistRootNode(this._persistRootNode);
        this._sounds = [];
        this.music_clips = new Dictionary_1.Dictionary();
        /**
         * 读取本地存储的数据
         *
         */
        this._music = this._persistRootNode.addComponent(cc.AudioSource);
        //获取bgm的音量
        this._music.volume = this._music_volume;
        //获取bgm是否存储静音
        this._music.volume = this._music_muted == 1 ? 0 : this._music_volume;
        //获取sounds列表
        for (var i = 0; i < MAX_SOUNDS; i++) {
            this._sounds[i] = this._persistRootNode.addComponent(cc.AudioSource);
            this._sounds[i].volume = this._sound_volume;
            this._sounds[i].volume = this._sound_muted == 1 ? 0 : this._sound_volume;
        }
    };
    /**
     * @param audioName 音效名字
     * @param isLoop 是否循环播放
     * @protected 播放音效
     */
    AudioMgr.prototype.playMusic = function (audioName, isLoop) {
        var _this = this;
        if (isLoop === void 0) { isLoop = true; }
        if (this._cur_music_name == audioName) {
            return;
        }
        var call = function (clip) {
            // this._music.clip = null
            _this._music.clip = clip;
            _this._music.loop = isLoop;
            _this._music.play();
            if (!_this.music_clips.containsKey(audioName)) {
                _this.music_clips.add(audioName, clip);
            }
        };
        if (this.music_clips.containsKey(audioName)) {
            call(this.music_clips.getValue(audioName));
        }
        else {
            var bundleName = "resources";
            cc.assetManager.loadBundle(bundleName, function (err, bundle) {
                bundle.load(Config_1.Config.audio + audioName, cc.AudioClip, function (err, clip) {
                    if (err) {
                        console.error("loadAudioClip" + err);
                    }
                    else {
                        call(clip);
                    }
                });
            });
        }
    };
    /**
     * @param audioName 音效名字
     * @param isLoop 是否循环播放
     * @protected 播放音效
     */
    AudioMgr.prototype.playSound = function (audioName, isLoop) {
        var _this = this;
        if (isLoop === void 0) { isLoop = false; }
        var call = function (clip) {
            // this._sounds[this._now_soundid].clip = null
            _this._sounds[_this._now_soundid].clip = clip;
            _this._sounds[_this._now_soundid].loop = isLoop;
            _this._sounds[_this._now_soundid].play();
            if (!_this.music_clips.containsKey(audioName)) {
                _this.music_clips.add(audioName, clip);
            }
            _this._now_soundid = _this._now_soundid + 1 >= _this._sounds.length ? 0 : _this._now_soundid + 1;
        };
        if (this.music_clips.containsKey(audioName)) {
            call(this.music_clips.getValue(audioName));
        }
        else {
            var bundleName = "resources";
            cc.assetManager.loadBundle(bundleName, function (err, bundle) {
                bundle.load(Config_1.Config.audio + audioName, cc.AudioClip, function (err, clip) {
                    if (err) {
                        console.error("loadAudioClip" + err);
                    }
                    else {
                        call(clip);
                    }
                });
            });
        }
    };
    /**
     * 停止播放bgm
     */
    AudioMgr.prototype.stopMusic = function () {
        this._music.stop();
        // this._music.clip = null
    };
    /**
     * 停止播放所有的sound
     */
    AudioMgr.prototype.stopAllSound = function () {
        for (var i = 0; i < this._sounds.length; i++) {
            this._sounds[i].stop();
            // this._sounds[i].clip = null
        }
        this._now_soundid = 0;
    };
    /**
     *
     * @param mute 是否静音music
     */
    AudioMgr.prototype.setMusicMute = function (mute) {
        if (mute == (this._music_muted == 1)) {
            return;
        }
        this._music_muted = mute ? 1 : 0;
        this._music.volume = mute ? this._music_volume : 0;
        //存储music静音 this._music_muted
    };
    /**
     *
     * @param mute 是否静音sound
     */
    AudioMgr.prototype.setSoundMute = function (mute) {
        if (mute == (this._sound_muted == 1)) {
            return;
        }
        this._sound_muted = mute ? 1 : 0;
        for (var i = 0; i < this._sounds.length; i++) {
            this._sounds[i].volume = mute ? this._sound_volume : 0;
        }
        //存储sound静音 this._sound_muted
    };
    /**
     *
     * @param value 设置音乐声音大小
     */
    AudioMgr.prototype.setMusicVolume = function (value) {
        this._music.volume = value;
        this._music_volume = value;
        //存储music音量大小 this._music_volume
    };
    /**
     *
     * @param value 设置sound声音大小
     */
    AudioMgr.prototype.setSoundVolume = function (value) {
        this._sound_volume = value;
        for (var i = 0; i < this._sounds.length; i++) {
            this._sounds[i].volume = value;
        }
        //存储sound音量大小 this._sound_volume
    };
    /**
     *
     * @returns 返回bgm静音状态
     */
    AudioMgr.prototype.getMusicMute = function () {
        return this._music_muted;
    };
    /**
     *
     * @returns 返回sound音效静音状态
     */
    AudioMgr.prototype.getSoundMute = function () {
        return this._sound_muted;
    };
    /**
     *
     * @returns 返回bgm声音大小
     */
    AudioMgr.prototype.getMusicVolume = function () {
        return this._music_volume;
    };
    /**
     *
     * @returns 返回sound音效声音大小
     */
    AudioMgr.prototype.getSoundVolume = function () {
        return this._sound_volume;
    };
    var AudioMgr_1;
    AudioMgr = AudioMgr_1 = __decorate([
        ccclass("AudioMgr")
    ], AudioMgr);
    return AudioMgr;
}());
exports.AudioMgr = AudioMgr;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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