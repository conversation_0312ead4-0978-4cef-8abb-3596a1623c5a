
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/util/Dictionary.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '80e8491m9tLO5SusdDsOgKH', 'Dictionary');
// scripts/util/Dictionary.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Dictionary = void 0;
/**
 * 自定义字典类
 */
var Dictionary = /** @class */ (function () {
    function Dictionary() {
        this.keys = [];
        this.values = [];
    }
    /**
     * 获取所有值
     */
    Dictionary.prototype.getList = function () {
        var self = this;
        return self.values;
    };
    /**
     * 根据键得到值
     */
    Dictionary.prototype.getValue = function (key) {
        var self = this;
        var index = self.keys.indexOf(key);
        if (index != -1)
            return self.values[index];
        return null;
    };
    /**根据值得到键 */
    Dictionary.prototype.getKey = function (value) {
        var self = this;
        var index = self.values.indexOf(value);
        if (index != -1)
            return self.keys[index];
        return null;
    };
    /**改变值 */
    Dictionary.prototype.changeValue = function (key, changeValue) {
        var self = this;
        var index = self.keys.indexOf(key);
        if (index != -1)
            self.values[index] = changeValue;
    };
    /**改变键 */
    Dictionary.prototype.changeKey = function (key, changeKey) {
        var self = this;
        var index = self.keys.indexOf(key);
        if (index != -1)
            self.keys[index] = changeKey;
    };
    /** 根据键刷新值 */
    Dictionary.prototype.updateValue = function (key, value) {
        var self = this;
        var index = self.keys.indexOf(key);
        if (index != -1)
            self.values[index] = value;
        else
            self.add(key, value);
    };
    /**
     * 添加键值
     */
    Dictionary.prototype.add = function (key, value, name) {
        var self = this;
        if (self.keys.indexOf(key) != -1) {
            // console.log("same key in dic", name);
            return;
        }
        self.keys.push(key);
        self.values.push(value);
    };
    /**
     * 根据键添加值
     * type: 0:values是数组往里面添加值
     *       1:values是number,用来统计数量的
     */
    Dictionary.prototype.addValue = function (key, value, type) {
        if (type === void 0) { type = 0; }
        var self = this;
        var index = self.keys.indexOf(key);
        if (index < 0) {
            self.keys.push(key);
            self.addValue(key, value, type);
        }
        else {
            var values = self.getValue(key);
            if (type == 0) {
                if (values) {
                    values.push(value);
                }
                else {
                    values = [value];
                }
            }
            else {
                if (values) {
                    values += value;
                }
                else {
                    values = 1;
                }
            }
            self.changeValue(key, values);
        }
    };
    /**
     * 清空
     */
    Dictionary.prototype.clear = function () {
        var self = this;
        self.keys.length = 0;
        self.values.length = 0;
    };
    /**
     * 根据键移除对象
     */
    Dictionary.prototype.removeKey = function (key) {
        var self = this;
        var index = self.keys.indexOf(key);
        if (index < 0)
            return;
        self.keys.splice(index, 1);
        self.values.splice(index, 1);
    };
    /**
     * 根据值移除对象
     */
    Dictionary.prototype.removeValue = function (value) {
        var self = this;
        var index = self.values.indexOf(value);
        self.keys.splice(index, 1);
        self.values.splice(index, 1);
    };
    /**
     * 根据键检测是否存在对象
     */
    Dictionary.prototype.containsKey = function (key) {
        if (this.keys.indexOf(key, 0) == -1) {
            return false;
        }
        return true;
    };
    /**
     * 根据值检测是否存在对象
     */
    Dictionary.prototype.containsValue = function (value) {
        if (this.values.indexOf(value, 0) == -1) {
            return false;
        }
        return true;
    };
    /**突出最后一个对象 */
    Dictionary.prototype.pop = function () {
        var self = this;
        self.keys.pop();
        self.values.pop();
    };
    /**根据索引交换位置 */
    Dictionary.prototype.swap = function (num1, num2) {
        var self = this;
        if (self.keys.length <= num1 ||
            self.keys.length <= num2)
            return;
        //交换
        var tmpK = self.keys[num1];
        self.keys[num1] = self.keys[num2];
        self.keys[num2] = tmpK;
        var tmpV = self.values[num1];
        self.values[num1] = self.values[num2];
        self.values[num2] = tmpV;
    };
    /** 交换两个索引对应的值*/
    Dictionary.prototype.cutValue = function (num1, num2) {
        var self = this;
        if (self.keys.indexOf(num1) < -1 ||
            self.keys.indexOf(num1) < -2)
            return;
        var tmpV = self.getValue(num1);
        self.changeValue(num1, self.getValue(num2));
        self.changeValue(num2, tmpV);
    };
    Object.defineProperty(Dictionary.prototype, "size", {
        // public cutValue(num1:number, num2:number):void {
        //     let self = this;
        //     if( self.keys.length <= num1 ||
        //         self.keys.length <= num2  )
        //         return;
        //     let tmpV = self.values[num1];
        //     self.values[num1] = self.values[num2];
        //     self.values[num2] = tmpV;
        // }
        /**长度 */
        get: function () {
            return this.keys.length;
        },
        enumerable: false,
        configurable: true
    });
    return Dictionary;
}());
exports.Dictionary = Dictionary;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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