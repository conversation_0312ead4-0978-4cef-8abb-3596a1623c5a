
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/util/LocalStorageManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4829baz02BEOKmgqAB/a7a3', 'LocalStorageManager');
// scripts/util/LocalStorageManager.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalStorageManager = void 0;
var Singleton_1 = require("../../meshTools/Singleton");
//这个就是本地存储的管理类
var LocalStorageManager = /** @class */ (function (_super) {
    __extends(LocalStorageManager, _super);
    function LocalStorageManager() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.musicSwitch = 'MusicSwitch'; //背景音乐的控制
        _this.soundSwitch = 'SoundSwitch'; //动效音乐的控制
        return _this;
    }
    LocalStorageManager.prototype._setValue = function (key, value) {
        localStorage.setItem(key, value);
    };
    LocalStorageManager.prototype._getValue = function (key) {
        return localStorage.getItem(key);
    };
    //设置背景音乐的开关
    LocalStorageManager.prototype.setMusicSwitch = function (bool) {
        this._setValue(this.musicSwitch, bool ? '1' : '0');
    };
    //获取背景音乐的开关,默认是开的
    LocalStorageManager.prototype.getMusicSwitch = function () {
        var value = this._getValue(this.musicSwitch);
        if (value) {
            return value == '1' ? true : false;
        }
        else {
            return true;
        }
    };
    //设置动效音乐的开关
    LocalStorageManager.prototype.setSoundSwitch = function (bool) {
        this._setValue(this.soundSwitch, bool ? '1' : '0');
    };
    //获取动效音乐的开关,默认是开的
    LocalStorageManager.prototype.getSoundSwitch = function () {
        var value = this._getValue(this.soundSwitch);
        if (value) {
            return value == '1' ? true : false;
        }
        else {
            return true;
        }
    };
    return LocalStorageManager;
}(Singleton_1.Singleton));
exports.LocalStorageManager = LocalStorageManager;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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