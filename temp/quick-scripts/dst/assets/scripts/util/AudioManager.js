
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/util/AudioManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '41fa63qXFBNjI/3M246s/5D', 'AudioManager');
// scripts/util/AudioManager.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioManager = void 0;
var AudioMgr_1 = require("./AudioMgr");
var LocalStorageManager_1 = require("./LocalStorageManager");
var AudioManager = /** @class */ (function () {
    function AudioManager() {
    }
    //播放 Bgm
    AudioManager.playBgm = function () {
        // 播放音乐，音乐文件名为'bgm.mp3'
        var music = LocalStorageManager_1.LocalStorageManager.GetInstance().getMusicSwitch(); //判断有没有开启背景音乐
        if (!music) {
            return;
        }
        AudioMgr_1.AudioMgr.ins.playMusic('bgm');
    };
    //停止 bgm
    AudioManager.stopBgm = function () {
        AudioMgr_1.AudioMgr.ins.stopMusic();
    };
    //按键音效
    AudioManager.keyingToneAudio = function () {
        AudioManager.playSound('keying_tone');
    };
    //胜利音效
    AudioManager.winAudio = function () {
        AudioManager.playSound('you_win');
    };
    //失败音效
    AudioManager.loseAudio = function () {
        AudioManager.playSound('you_lose');
    };
    //播放音效
    AudioManager.playSound = function (audioName) {
        var sound = LocalStorageManager_1.LocalStorageManager.GetInstance().getSoundSwitch(); //判断有没有开启音效
        if (!sound) {
            return;
        }
        AudioMgr_1.AudioMgr.ins.playSound(audioName);
    };
    return AudioManager;
}());
exports.AudioManager = AudioManager;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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