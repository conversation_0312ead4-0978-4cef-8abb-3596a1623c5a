
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/GridController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '477f5VfSppO37TQXXq0KN4Z', 'GridController');
// scripts/game/GridController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GridController = /** @class */ (function (_super) {
    __extends(GridController, _super);
    function GridController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.backgroundSprite = null; // 格子背景
        _this.normalFrame = null; // 正常状态的背景图
        _this.hoverFrame = null; // 悬停状态的背景图
        _this.occupiedFrame = null; // 已占用状态的背景图
        // 格子坐标
        _this.gridX = 0;
        _this.gridY = 0;
        // 格子状态
        _this.isOccupied = false;
        _this.playerNode = null;
        // 回调函数
        _this.onGridClickCallback = null;
        return _this;
        // update (dt) {}
    }
    GridController.prototype.onLoad = function () {
        // 添加触摸事件
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.node.on(cc.Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);
        this.node.on(cc.Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);
    };
    GridController.prototype.start = function () {
        this.updateVisualState();
    };
    // 设置格子坐标
    GridController.prototype.setGridCoordinate = function (x, y) {
        this.gridX = x;
        this.gridY = y;
        this.node.name = "Grid_" + x + "_" + y;
    };
    // 设置点击回调
    GridController.prototype.setClickCallback = function (callback) {
        this.onGridClickCallback = callback;
    };
    // 触摸开始
    GridController.prototype.onTouchStart = function (event) {
        if (!this.isOccupied && this.hoverFrame && this.backgroundSprite) {
            this.backgroundSprite.spriteFrame = this.hoverFrame;
        }
    };
    // 触摸结束
    GridController.prototype.onTouchEnd = function (event) {
        this.updateVisualState();
        // 触发点击回调
        if (this.onGridClickCallback) {
            this.onGridClickCallback(this.gridX, this.gridY);
        }
    };
    // 触摸取消
    GridController.prototype.onTouchCancel = function (event) {
        this.updateVisualState();
    };
    // 鼠标进入
    GridController.prototype.onMouseEnter = function (event) {
        if (!this.isOccupied && this.hoverFrame && this.backgroundSprite) {
            this.backgroundSprite.spriteFrame = this.hoverFrame;
        }
    };
    // 鼠标离开
    GridController.prototype.onMouseLeave = function (event) {
        this.updateVisualState();
    };
    // 更新视觉状态
    GridController.prototype.updateVisualState = function () {
        if (!this.backgroundSprite)
            return;
        if (this.isOccupied && this.occupiedFrame) {
            this.backgroundSprite.spriteFrame = this.occupiedFrame;
        }
        else if (this.normalFrame) {
            this.backgroundSprite.spriteFrame = this.normalFrame;
        }
    };
    // 设置占用状态
    GridController.prototype.setOccupied = function (occupied, playerNode) {
        this.isOccupied = occupied;
        this.playerNode = playerNode || null;
        this.updateVisualState();
    };
    // 获取格子世界坐标
    GridController.prototype.getWorldPosition = function () {
        return this.node.convertToWorldSpaceAR(cc.Vec2.ZERO);
    };
    // 清除玩家
    GridController.prototype.clearPlayer = function () {
        if (this.playerNode) {
            this.playerNode.removeFromParent();
            this.playerNode = null;
        }
        this.setOccupied(false);
    };
    GridController.prototype.onDestroy = function () {
        // 清理事件监听
        this.node.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.node.off(cc.Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);
        this.node.off(cc.Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);
    };
    __decorate([
        property(cc.Sprite)
    ], GridController.prototype, "backgroundSprite", void 0);
    __decorate([
        property(cc.SpriteFrame)
    ], GridController.prototype, "normalFrame", void 0);
    __decorate([
        property(cc.SpriteFrame)
    ], GridController.prototype, "hoverFrame", void 0);
    __decorate([
        property(cc.SpriteFrame)
    ], GridController.prototype, "occupiedFrame", void 0);
    GridController = __decorate([
        ccclass
    ], GridController);
    return GridController;
}(cc.Component));
exports.default = GridController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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