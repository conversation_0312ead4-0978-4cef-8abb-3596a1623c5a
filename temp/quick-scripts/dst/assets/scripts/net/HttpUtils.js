
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/net/HttpUtils.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'aeafbVbu41Ig7SOSQHgyL6L', 'HttpUtils');
// scripts/net/HttpUtils.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpUtils = void 0;
var HttpUtils = /** @class */ (function () {
    function HttpUtils() {
    }
    HttpUtils.SyncGet = function (address, params) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            var xhr = new XMLHttpRequest();
            var url = address + _this.ParamsToString(params);
            xhr.open("GET", url, true); // 异步模式
            var interfaceName = _this.GetUrlInterfaceName(address);
            _this._isShowLog && console.log(interfaceName + " url: ", url);
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) { // 请求完成
                    if ((xhr.status >= 200 && xhr.status < 300) || xhr.status === 304) {
                        var data = _this.GetHttpData(xhr);
                        _this._isShowLog && console.log(interfaceName, data);
                        resolve(data);
                    }
                    else {
                        var errorData = _this.CreateGetErrorData(address, xhr.status);
                        _this._isShowLog && console.log(interfaceName + " 错误", errorData);
                        reject(errorData);
                    }
                }
            };
            xhr.send();
        });
    };
    HttpUtils.Get = function (address, params, cb) {
        var _this = this;
        var interfaceName = this.GetUrlInterfaceName(address);
        var xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function (ev) {
            if (xhr.readyState == 4) {
                var data = null;
                if ((xhr.status >= 200 && xhr.status < 300) || xhr.status == 304) {
                    data = _this.GetHttpData(xhr);
                }
                else {
                    data = _this.CreateGetErrorData(address, xhr.status);
                }
                _this._isShowLog && console.log(interfaceName, data);
                cb && cb(data);
            }
        };
        var url = address + this.ParamsToString(params);
        xhr.open("GET", url);
        xhr.send();
        this._isShowLog && console.log(interfaceName + " url: ", url);
    };
    HttpUtils.ParamsToString = function (params) {
        var arr = [];
        for (var key in params) {
            arr.push(key + "=" + params[key]);
        }
        if (arr.length > 0) {
            return "?" + arr.join("&");
        }
        return "";
    };
    HttpUtils.GetHttpData = function (xhr) {
        var outData = {};
        try {
            outData = JSON.parse(xhr.response);
        }
        catch (err) {
            outData = xhr.response;
        }
        return outData;
    };
    HttpUtils.GetUrlInterfaceName = function (url) {
        var adress = url.split("?")[0];
        var strArr = adress.split("/");
        return strArr[strArr.length - 1];
    };
    HttpUtils.CreateGetErrorData = function (address, status) {
        var interfaceName = this.GetUrlInterfaceName(address);
        var resData = {};
        resData.code = status;
        resData.docs = interfaceName + " status: " + status;
        return resData;
    };
    HttpUtils._isShowLog = true;
    return HttpUtils;
}());
exports.HttpUtils = HttpUtils;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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