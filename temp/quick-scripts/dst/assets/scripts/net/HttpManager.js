
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/net/HttpManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8405dpDwZlPW5cASlfBvNdT', 'HttpManager');
// scripts/net/HttpManager.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpManager = void 0;
var HttpUtils_1 = require("./HttpUtils");
var MessageBaseBean_1 = require("./MessageBaseBean");
var GameServerUrl_1 = require("./GameServerUrl");
var MeshTools_1 = require("../../meshTools/MeshTools");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var GameRouteUrlDef = {
    0: {
        101: "https://game-dev.jieyou.shop/game_route/"
    },
    1: {
        101: "https://game-cn-test.jieyou.shop/game_route/",
        8001: "https://gameapi-test.soofun.online/game_route/"
    },
    2: {
        101: "https://mesh-gameapi.jieyou.shop/game_route/",
        201: "https://aws-gameapi.jieyou.shop/game_route/",
        301: "https://bysk.gameapi.gg.jieyou.shop/game_route/",
        401: "https://gameapi.fra.jieyou.shop/game_route/",
        8001: "https://gameapi.soofun.online/game_route/"
    }
};
var InterfaceDef;
(function (InterfaceDef) {
    InterfaceDef["GET_ADDRESS"] = "get_addr";
    InterfaceDef["UPDATE_TIME"] = "update_time";
})(InterfaceDef || (InterfaceDef = {}));
var HttpManager = /** @class */ (function () {
    function HttpManager() {
        this.CheckUrlTimer = null;
    }
    Object.defineProperty(HttpManager, "Instance", {
        get: function () {
            if (this._inst == null) {
                this._inst = new HttpManager();
            }
            return this._inst;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HttpManager.prototype, "Url", {
        get: function () {
            var env = MeshTools_1.MeshTools.Publish.getEnv();
            return GameRouteUrlDef[env][MeshTools_1.MeshTools.Publish.gsp];
        },
        enumerable: false,
        configurable: true
    });
    HttpManager.prototype.ReqServerUrl = function (callBack) {
        return __awaiter(this, void 0, void 0, function () {
            var params, url, data, autoMessageBean_1, autoMessageBean;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        params = {};
                        params.user_id = MeshTools_1.MeshTools.Publish.userId;
                        params.room_id = MeshTools_1.MeshTools.Publish.roomId;
                        params.game_mode = MeshTools_1.MeshTools.Publish.gameMode;
                        params.game_id = MeshTools_1.MeshTools.Publish.getGameId().toString();
                        params.app_id = MeshTools_1.MeshTools.Publish.appId.toString();
                        params.app_channel = MeshTools_1.MeshTools.Publish.appChannel;
                        url = this.Url + InterfaceDef.GET_ADDRESS;
                        return [4 /*yield*/, HttpUtils_1.HttpUtils.SyncGet(url, params)];
                    case 1:
                        data = _a.sent();
                        if (data.code == 200) {
                            GameServerUrl_1.GameServerUrl.Http = data.data.http_addr;
                            GameServerUrl_1.GameServerUrl.Ws = data.data.ws_addr;
                            this.StartCheckServerUrlState();
                            callBack();
                            autoMessageBean_1 = {
                                'msgId': MessageBaseBean_1.AutoMessageId.HttpSucceed,
                                'data': {}
                            };
                            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean_1);
                            return [2 /*return*/];
                        }
                        autoMessageBean = {
                            'msgId': MessageBaseBean_1.AutoMessageId.GameRouteNotFoundMsg,
                            'data': { code: window.getLocalizedStr('GameRouteNotFound') }
                        };
                        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
                        return [2 /*return*/];
                }
            });
        });
    };
    HttpManager.prototype.ReqServerUrlState = function () {
        var _this = this;
        if (!window.navigator.onLine) {
            setTimeout(function () {
                GameMgr_1.GameMgr.Console.Error('短链接重连');
                _this.ReqServerUrlState();
            }, 1000);
            return;
        }
        var params = {};
        params.user_id = MeshTools_1.MeshTools.Publish.userId;
        params.room_id = MeshTools_1.MeshTools.Publish.roomId;
        params.game_mode = MeshTools_1.MeshTools.Publish.gameMode;
        params.game_id = MeshTools_1.MeshTools.Publish.getGameId().toString();
        params.app_id = MeshTools_1.MeshTools.Publish.appId.toString();
        params.app_channel = MeshTools_1.MeshTools.Publish.appChannel;
        params.curr_http_addr = GameServerUrl_1.GameServerUrl.Http;
        params.curr_ws_addr = GameServerUrl_1.GameServerUrl.Ws;
        var url = this.Url + InterfaceDef.UPDATE_TIME;
        HttpUtils_1.HttpUtils.Get(url, params, function (data) {
            if (data.code == 200) {
                _this.StartCheckServerUrlState();
            }
            else {
                _this.StopCheckServerUrlState();
                _this.ReqServerUrl(function () { });
            }
        });
    };
    HttpManager.prototype.StartCheckServerUrlState = function () {
        var _this = this;
        this.CheckUrlTimer = setTimeout(function () {
            _this.ReqServerUrlState();
        }, 180000); // 这里是三分钟
    };
    HttpManager.prototype.StopCheckServerUrlState = function () {
        if (this.CheckUrlTimer != null) {
            clearInterval(this.CheckUrlTimer);
            this.CheckUrlTimer = null;
        }
    };
    HttpManager._inst = null;
    return HttpManager;
}());
exports.HttpManager = HttpManager;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL25ldC9IdHRwTWFuYWdlci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFHQSx5Q0FBd0M7QUFDeEMscURBQW1FO0FBQ25FLGlEQUFnRDtBQUNoRCx1REFBc0Q7QUFDdEQsNkNBQTRDO0FBQzVDLHFEQUFrRDtBQUVsRCxJQUFNLGVBQWUsR0FBRztJQUNwQixDQUFDLEVBQUU7UUFDQyxHQUFHLEVBQUUsMENBQTBDO0tBQ2xEO0lBRUQsQ0FBQyxFQUFFO1FBQ0MsR0FBRyxFQUFFLDhDQUE4QztRQUNuRCxJQUFJLEVBQUUsZ0RBQWdEO0tBQ3pEO0lBRUQsQ0FBQyxFQUFFO1FBQ0MsR0FBRyxFQUFFLDhDQUE4QztRQUNuRCxHQUFHLEVBQUUsNkNBQTZDO1FBQ2xELEdBQUcsRUFBRSxpREFBaUQ7UUFDdEQsR0FBRyxFQUFFLDZDQUE2QztRQUNsRCxJQUFJLEVBQUUsMkNBQTJDO0tBQ3BEO0NBQ0osQ0FBQTtBQUlELElBQUssWUFHSjtBQUhELFdBQUssWUFBWTtJQUNiLHdDQUF3QixDQUFBO0lBQ3hCLDJDQUEyQixDQUFBO0FBQy9CLENBQUMsRUFISSxZQUFZLEtBQVosWUFBWSxRQUdoQjtBQUVEO0lBQUE7UUFZVyxrQkFBYSxHQUFRLElBQUksQ0FBQztJQWlGckMsQ0FBQztJQXpGRyxzQkFBa0IsdUJBQVE7YUFBMUI7WUFDSSxJQUFJLElBQUksQ0FBQyxLQUFLLElBQUksSUFBSSxFQUFFO2dCQUNwQixJQUFJLENBQUMsS0FBSyxHQUFHLElBQUksV0FBVyxFQUFFLENBQUM7YUFDbEM7WUFFRCxPQUFPLElBQUksQ0FBQyxLQUFLLENBQUM7UUFDdEIsQ0FBQzs7O09BQUE7SUFJRCxzQkFBVyw0QkFBRzthQUFkO1lBQ0ksSUFBSSxHQUFHLEdBQVcscUJBQVMsQ0FBQyxPQUFPLENBQUMsTUFBTSxFQUFFLENBQUM7WUFDN0MsT0FBTyxlQUFlLENBQUMsR0FBRyxDQUFDLENBQUMscUJBQVMsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDdkQsQ0FBQzs7O09BQUE7SUFFWSxrQ0FBWSxHQUF6QixVQUEwQixRQUFpQjs7Ozs7O3dCQUNuQyxNQUFNLEdBQW9DLEVBQXFDLENBQUM7d0JBQ3BGLE1BQU0sQ0FBQyxPQUFPLEdBQUcscUJBQVMsQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDO3dCQUMxQyxNQUFNLENBQUMsT0FBTyxHQUFHLHFCQUFTLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQzt3QkFDMUMsTUFBTSxDQUFDLFNBQVMsR0FBRyxxQkFBUyxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUM7d0JBQzlDLE1BQU0sQ0FBQyxPQUFPLEdBQUcscUJBQVMsQ0FBQyxPQUFPLENBQUMsU0FBUyxFQUFFLENBQUMsUUFBUSxFQUFFLENBQUM7d0JBQzFELE1BQU0sQ0FBQyxNQUFNLEdBQUcscUJBQVMsQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLFFBQVEsRUFBRSxDQUFDO3dCQUNuRCxNQUFNLENBQUMsV0FBVyxHQUFHLHFCQUFTLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQzt3QkFFOUMsR0FBRyxHQUFXLElBQUksQ0FBQyxHQUFHLEdBQUcsWUFBWSxDQUFDLFdBQVcsQ0FBQzt3QkFDVixxQkFBTSxxQkFBUyxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsTUFBTSxDQUFDLEVBQUE7O3dCQUE1RSxJQUFJLEdBQW9DLFNBQW9DO3dCQUNoRixJQUFJLElBQUksQ0FBQyxJQUFJLElBQUksR0FBRyxFQUFFOzRCQUNsQiw2QkFBYSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQzs0QkFDekMsNkJBQWEsQ0FBQyxFQUFFLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUM7NEJBQ3JDLElBQUksQ0FBQyx3QkFBd0IsRUFBRSxDQUFDOzRCQUNoQyxRQUFRLEVBQUUsQ0FBQTs0QkFDTixvQkFBbUM7Z0NBQ25DLE9BQU8sRUFBRSwrQkFBYSxDQUFDLFdBQVc7Z0NBQ2xDLE1BQU0sRUFBRSxFQUFFOzZCQUNiLENBQUE7NEJBQ0QsaUJBQU8sQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLHVCQUFTLENBQUMsV0FBVyxFQUFFLGlCQUFlLENBQUMsQ0FBQzs0QkFDM0Qsc0JBQU87eUJBQ1Y7d0JBRUcsZUFBZSxHQUFvQjs0QkFDbkMsT0FBTyxFQUFFLCtCQUFhLENBQUMsb0JBQW9COzRCQUMzQyxNQUFNLEVBQUUsRUFBQyxJQUFJLEVBQUUsTUFBTSxDQUFDLGVBQWUsQ0FBQyxtQkFBbUIsQ0FBQyxFQUFDO3lCQUM5RCxDQUFBO3dCQUNELGlCQUFPLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyx1QkFBUyxDQUFDLFdBQVcsRUFBRSxlQUFlLENBQUMsQ0FBQzs7Ozs7S0FDOUQ7SUFFTSx1Q0FBaUIsR0FBeEI7UUFBQSxpQkE2QkM7UUE1QkcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsTUFBTSxFQUFFO1lBQzFCLFVBQVUsQ0FBQztnQkFDUCxpQkFBTyxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUE7Z0JBQzlCLEtBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQzdCLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQztZQUNULE9BQU87U0FDVjtRQUVELElBQUksTUFBTSxHQUFnQyxFQUFpQyxDQUFDO1FBQzVFLE1BQU0sQ0FBQyxPQUFPLEdBQUcscUJBQVMsQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDO1FBQzFDLE1BQU0sQ0FBQyxPQUFPLEdBQUcscUJBQVMsQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDO1FBQzFDLE1BQU0sQ0FBQyxTQUFTLEdBQUcscUJBQVMsQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDO1FBQzlDLE1BQU0sQ0FBQyxPQUFPLEdBQUcscUJBQVMsQ0FBQyxPQUFPLENBQUMsU0FBUyxFQUFFLENBQUMsUUFBUSxFQUFFLENBQUM7UUFDMUQsTUFBTSxDQUFDLE1BQU0sR0FBRyxxQkFBUyxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsUUFBUSxFQUFFLENBQUM7UUFDbkQsTUFBTSxDQUFDLFdBQVcsR0FBRyxxQkFBUyxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUM7UUFDbEQsTUFBTSxDQUFDLGNBQWMsR0FBRyw2QkFBYSxDQUFDLElBQUksQ0FBQztRQUMzQyxNQUFNLENBQUMsWUFBWSxHQUFHLDZCQUFhLENBQUMsRUFBRSxDQUFDO1FBRXZDLElBQUksR0FBRyxHQUFXLElBQUksQ0FBQyxHQUFHLEdBQUcsWUFBWSxDQUFDLFdBQVcsQ0FBQztRQUN0RCxxQkFBUyxDQUFDLEdBQUcsQ0FBQyxHQUFHLEVBQUUsTUFBTSxFQUFFLFVBQUMsSUFBMkI7WUFDbkQsSUFBSSxJQUFJLENBQUMsSUFBSSxJQUFJLEdBQUcsRUFBRTtnQkFDbEIsS0FBSSxDQUFDLHdCQUF3QixFQUFFLENBQUM7YUFDbkM7aUJBQ0k7Z0JBQ0QsS0FBSSxDQUFDLHVCQUF1QixFQUFFLENBQUM7Z0JBQy9CLEtBQUksQ0FBQyxZQUFZLENBQUMsY0FBSyxDQUFDLENBQUMsQ0FBQzthQUM3QjtRQUNMLENBQUMsQ0FBQyxDQUFDO0lBQ1AsQ0FBQztJQUVNLDhDQUF3QixHQUEvQjtRQUFBLGlCQUlDO1FBSEcsSUFBSSxDQUFDLGFBQWEsR0FBRyxVQUFVLENBQUM7WUFDNUIsS0FBSSxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFDN0IsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxDQUFDLENBQUMsU0FBUztJQUN6QixDQUFDO0lBRU0sNkNBQXVCLEdBQTlCO1FBQ0ksSUFBSSxJQUFJLENBQUMsYUFBYSxJQUFJLElBQUksRUFBRTtZQUM1QixhQUFhLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBQ2xDLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDO1NBQzdCO0lBQ0wsQ0FBQztJQTFGYyxpQkFBSyxHQUFnQixJQUFJLENBQUM7SUEyRjdDLGtCQUFDO0NBN0ZELEFBNkZDLElBQUE7QUE3Rlksa0NBQVciLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJcclxuXHJcbmltcG9ydCB7IElIdHRwTXNnQm9keSB9IGZyb20gXCIuL0lIdHRwTXNnQm9keVwiO1xyXG5pbXBvcnQgeyBIdHRwVXRpbHMgfSBmcm9tIFwiLi9IdHRwVXRpbHNcIjtcclxuaW1wb3J0IHsgQXV0b01lc3NhZ2VCZWFuLCBBdXRvTWVzc2FnZUlkIH0gZnJvbSBcIi4vTWVzc2FnZUJhc2VCZWFuXCI7XHJcbmltcG9ydCB7IEdhbWVTZXJ2ZXJVcmwgfSBmcm9tIFwiLi9HYW1lU2VydmVyVXJsXCI7XHJcbmltcG9ydCB7IE1lc2hUb29scyB9IGZyb20gXCIuLi8uLi9tZXNoVG9vbHMvTWVzaFRvb2xzXCI7XHJcbmltcG9ydCB7IEdhbWVNZ3IgfSBmcm9tIFwiLi4vY29tbW9uL0dhbWVNZ3JcIjtcclxuaW1wb3J0IHsgRXZlbnRUeXBlIH0gZnJvbSBcIi4uL2NvbW1vbi9FdmVudENlbnRlclwiO1xyXG5cclxuY29uc3QgR2FtZVJvdXRlVXJsRGVmID0ge1xyXG4gICAgMDoge1xyXG4gICAgICAgIDEwMTogXCJodHRwczovL2dhbWUtZGV2LmppZXlvdS5zaG9wL2dhbWVfcm91dGUvXCJcclxuICAgIH0sXHJcblxyXG4gICAgMToge1xyXG4gICAgICAgIDEwMTogXCJodHRwczovL2dhbWUtY24tdGVzdC5qaWV5b3Uuc2hvcC9nYW1lX3JvdXRlL1wiLFxyXG4gICAgICAgIDgwMDE6IFwiaHR0cHM6Ly9nYW1lYXBpLXRlc3Quc29vZnVuLm9ubGluZS9nYW1lX3JvdXRlL1wiXHJcbiAgICB9LFxyXG5cclxuICAgIDI6IHtcclxuICAgICAgICAxMDE6IFwiaHR0cHM6Ly9tZXNoLWdhbWVhcGkuamlleW91LnNob3AvZ2FtZV9yb3V0ZS9cIixcclxuICAgICAgICAyMDE6IFwiaHR0cHM6Ly9hd3MtZ2FtZWFwaS5qaWV5b3Uuc2hvcC9nYW1lX3JvdXRlL1wiLFxyXG4gICAgICAgIDMwMTogXCJodHRwczovL2J5c2suZ2FtZWFwaS5nZy5qaWV5b3Uuc2hvcC9nYW1lX3JvdXRlL1wiLFxyXG4gICAgICAgIDQwMTogXCJodHRwczovL2dhbWVhcGkuZnJhLmppZXlvdS5zaG9wL2dhbWVfcm91dGUvXCIsXHJcbiAgICAgICAgODAwMTogXCJodHRwczovL2dhbWVhcGkuc29vZnVuLm9ubGluZS9nYW1lX3JvdXRlL1wiXHJcbiAgICB9XHJcbn1cclxuXHJcblxyXG5cclxuZW51bSBJbnRlcmZhY2VEZWYge1xyXG4gICAgR0VUX0FERFJFU1MgPSBcImdldF9hZGRyXCIsXHJcbiAgICBVUERBVEVfVElNRSA9IFwidXBkYXRlX3RpbWVcIlxyXG59XHJcblxyXG5leHBvcnQgY2xhc3MgSHR0cE1hbmFnZXIge1xyXG5cclxuICAgIHByaXZhdGUgc3RhdGljIF9pbnN0OiBIdHRwTWFuYWdlciA9IG51bGw7XHJcblxyXG4gICAgcHVibGljIHN0YXRpYyBnZXQgSW5zdGFuY2UoKTogSHR0cE1hbmFnZXIge1xyXG4gICAgICAgIGlmICh0aGlzLl9pbnN0ID09IG51bGwpIHtcclxuICAgICAgICAgICAgdGhpcy5faW5zdCA9IG5ldyBIdHRwTWFuYWdlcigpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2luc3Q7XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIENoZWNrVXJsVGltZXI6IGFueSA9IG51bGw7XHJcblxyXG4gICAgcHVibGljIGdldCBVcmwoKTogc3RyaW5nIHtcclxuICAgICAgICBsZXQgZW52OiBudW1iZXIgPSBNZXNoVG9vbHMuUHVibGlzaC5nZXRFbnYoKTtcclxuICAgICAgICByZXR1cm4gR2FtZVJvdXRlVXJsRGVmW2Vudl1bTWVzaFRvb2xzLlB1Ymxpc2guZ3NwXTtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgYXN5bmMgUmVxU2VydmVyVXJsKGNhbGxCYWNrOkZ1bmN0aW9uKSB7XHJcbiAgICAgICAgbGV0IHBhcmFtczogSUh0dHBNc2dCb2R5LklSZXFHZXRHYW1lQWRkcmVzcyA9IHt9IGFzIElIdHRwTXNnQm9keS5JUmVxR2V0R2FtZUFkZHJlc3M7XHJcbiAgICAgICAgcGFyYW1zLnVzZXJfaWQgPSBNZXNoVG9vbHMuUHVibGlzaC51c2VySWQ7XHJcbiAgICAgICAgcGFyYW1zLnJvb21faWQgPSBNZXNoVG9vbHMuUHVibGlzaC5yb29tSWQ7XHJcbiAgICAgICAgcGFyYW1zLmdhbWVfbW9kZSA9IE1lc2hUb29scy5QdWJsaXNoLmdhbWVNb2RlO1xyXG4gICAgICAgIHBhcmFtcy5nYW1lX2lkID0gTWVzaFRvb2xzLlB1Ymxpc2guZ2V0R2FtZUlkKCkudG9TdHJpbmcoKTtcclxuICAgICAgICBwYXJhbXMuYXBwX2lkID0gTWVzaFRvb2xzLlB1Ymxpc2guYXBwSWQudG9TdHJpbmcoKTtcclxuICAgICAgICBwYXJhbXMuYXBwX2NoYW5uZWwgPSBNZXNoVG9vbHMuUHVibGlzaC5hcHBDaGFubmVsO1xyXG5cclxuICAgICAgICBsZXQgdXJsOiBzdHJpbmcgPSB0aGlzLlVybCArIEludGVyZmFjZURlZi5HRVRfQUREUkVTUztcclxuICAgICAgICBsZXQgZGF0YTogSUh0dHBNc2dCb2R5LklSZXNHZXRHYW1lQWRkcmVzcyA9IGF3YWl0IEh0dHBVdGlscy5TeW5jR2V0KHVybCwgcGFyYW1zKTtcclxuICAgICAgICBpZiAoZGF0YS5jb2RlID09IDIwMCkge1xyXG4gICAgICAgICAgICBHYW1lU2VydmVyVXJsLkh0dHAgPSBkYXRhLmRhdGEuaHR0cF9hZGRyO1xyXG4gICAgICAgICAgICBHYW1lU2VydmVyVXJsLldzID0gZGF0YS5kYXRhLndzX2FkZHI7XHJcbiAgICAgICAgICAgIHRoaXMuU3RhcnRDaGVja1NlcnZlclVybFN0YXRlKCk7XHJcbiAgICAgICAgICAgIGNhbGxCYWNrKClcclxuICAgICAgICAgICAgbGV0IGF1dG9NZXNzYWdlQmVhbjogQXV0b01lc3NhZ2VCZWFuID0ge1xyXG4gICAgICAgICAgICAgICAgJ21zZ0lkJzogQXV0b01lc3NhZ2VJZC5IdHRwU3VjY2VlZCwvL+efremTvumTvuaOpeaIkOWKn1xyXG4gICAgICAgICAgICAgICAgJ2RhdGEnOiB7fVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIEdhbWVNZ3IuRXZlbnQuU2VuZChFdmVudFR5cGUuQXV0b01lc3NhZ2UsIGF1dG9NZXNzYWdlQmVhbik7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGxldCBhdXRvTWVzc2FnZUJlYW46IEF1dG9NZXNzYWdlQmVhbiA9IHtcclxuICAgICAgICAgICAgJ21zZ0lkJzogQXV0b01lc3NhZ2VJZC5HYW1lUm91dGVOb3RGb3VuZE1zZywvL+a4uOaIj+e6v+i3r+W8guW4uOeahOmAmuefpVxyXG4gICAgICAgICAgICAnZGF0YSc6IHtjb2RlOiB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdHYW1lUm91dGVOb3RGb3VuZCcpfVxyXG4gICAgICAgIH1cclxuICAgICAgICBHYW1lTWdyLkV2ZW50LlNlbmQoRXZlbnRUeXBlLkF1dG9NZXNzYWdlLCBhdXRvTWVzc2FnZUJlYW4pO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBSZXFTZXJ2ZXJVcmxTdGF0ZSgpOiB2b2lkIHtcclxuICAgICAgICBpZiAoIXdpbmRvdy5uYXZpZ2F0b3Iub25MaW5lKSB7XHJcbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgR2FtZU1nci5Db25zb2xlLkVycm9yKCfnn63pk77mjqXph43ov54nKVxyXG4gICAgICAgICAgICAgICAgdGhpcy5SZXFTZXJ2ZXJVcmxTdGF0ZSgpO1xyXG4gICAgICAgICAgICB9LCAxMDAwKTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgbGV0IHBhcmFtczogSUh0dHBNc2dCb2R5LklSZXFVcGRhdGVUaW1lID0ge30gYXMgSUh0dHBNc2dCb2R5LklSZXFVcGRhdGVUaW1lO1xyXG4gICAgICAgIHBhcmFtcy51c2VyX2lkID0gTWVzaFRvb2xzLlB1Ymxpc2gudXNlcklkO1xyXG4gICAgICAgIHBhcmFtcy5yb29tX2lkID0gTWVzaFRvb2xzLlB1Ymxpc2gucm9vbUlkO1xyXG4gICAgICAgIHBhcmFtcy5nYW1lX21vZGUgPSBNZXNoVG9vbHMuUHVibGlzaC5nYW1lTW9kZTtcclxuICAgICAgICBwYXJhbXMuZ2FtZV9pZCA9IE1lc2hUb29scy5QdWJsaXNoLmdldEdhbWVJZCgpLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgcGFyYW1zLmFwcF9pZCA9IE1lc2hUb29scy5QdWJsaXNoLmFwcElkLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgcGFyYW1zLmFwcF9jaGFubmVsID0gTWVzaFRvb2xzLlB1Ymxpc2guYXBwQ2hhbm5lbDtcclxuICAgICAgICBwYXJhbXMuY3Vycl9odHRwX2FkZHIgPSBHYW1lU2VydmVyVXJsLkh0dHA7XHJcbiAgICAgICAgcGFyYW1zLmN1cnJfd3NfYWRkciA9IEdhbWVTZXJ2ZXJVcmwuV3M7XHJcblxyXG4gICAgICAgIGxldCB1cmw6IHN0cmluZyA9IHRoaXMuVXJsICsgSW50ZXJmYWNlRGVmLlVQREFURV9USU1FO1xyXG4gICAgICAgIEh0dHBVdGlscy5HZXQodXJsLCBwYXJhbXMsIChkYXRhOiBJSHR0cE1zZ0JvZHkuSVJlc0Jhc2UpID0+IHtcclxuICAgICAgICAgICAgaWYgKGRhdGEuY29kZSA9PSAyMDApIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuU3RhcnRDaGVja1NlcnZlclVybFN0YXRlKCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLlN0b3BDaGVja1NlcnZlclVybFN0YXRlKCk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLlJlcVNlcnZlclVybCgoKT0+e30pO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIFN0YXJ0Q2hlY2tTZXJ2ZXJVcmxTdGF0ZSgpOiB2b2lkIHtcclxuICAgICAgICB0aGlzLkNoZWNrVXJsVGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgdGhpcy5SZXFTZXJ2ZXJVcmxTdGF0ZSgpO1xyXG4gICAgICAgIH0sIDE4MDAwMCk7IC8vIOi/memHjOaYr+S4ieWIhumSn1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBTdG9wQ2hlY2tTZXJ2ZXJVcmxTdGF0ZSgpOiB2b2lkIHtcclxuICAgICAgICBpZiAodGhpcy5DaGVja1VybFRpbWVyICE9IG51bGwpIHtcclxuICAgICAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLkNoZWNrVXJsVGltZXIpO1xyXG4gICAgICAgICAgICB0aGlzLkNoZWNrVXJsVGltZXIgPSBudWxsO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufSJdfQ==