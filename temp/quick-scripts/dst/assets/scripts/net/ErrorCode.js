
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/net/ErrorCode.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '18f0ckDFwBGKYEa+6+oQZ/M', 'ErrorCode');
// scripts/net/ErrorCode.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorCode = void 0;
//错误码
var ErrorCode;
(function (ErrorCode) {
    ErrorCode[ErrorCode["OK"] = 0] = "OK";
    ErrorCode[ErrorCode["ErrGameId"] = 1] = "ErrGameId";
    ErrorCode[ErrorCode["ErrGameMode"] = 3] = "ErrGameMode";
    ErrorCode[ErrorCode["ErrRequestUser"] = 5] = "ErrRequestUser";
    ErrorCode[ErrorCode["ErrInPair"] = 6] = "ErrInPair";
    ErrorCode[ErrorCode["ErrNotEnoughCoin"] = 7] = "ErrNotEnoughCoin";
    ErrorCode[ErrorCode["ErrChangeBalance"] = 8] = "ErrChangeBalance";
    ErrorCode[ErrorCode["ErrNotFoundRoom"] = 9] = "ErrNotFoundRoom";
    ErrorCode[ErrorCode["ErrNotFoundUser"] = 10] = "ErrNotFoundUser";
    ErrorCode[ErrorCode["ErrRoomConfig"] = 12] = "ErrRoomConfig";
    ErrorCode[ErrorCode["ErrParams"] = 13] = "ErrParams";
    ErrorCode[ErrorCode["ErrDefend"] = 16] = "ErrDefend";
    ErrorCode[ErrorCode["ErrSitHaveUser"] = 18] = "ErrSitHaveUser";
    ErrorCode[ErrorCode["ErrHaveSit"] = 19] = "ErrHaveSit";
    ErrorCode[ErrorCode["ErrUserPlaying"] = 20] = "ErrUserPlaying";
    ErrorCode[ErrorCode["ErrInInvite"] = 31] = "ErrInInvite";
    ErrorCode[ErrorCode["ErrPlaying"] = 32] = "ErrPlaying";
    ErrorCode[ErrorCode["ErrInvalidInviteCode"] = 33] = "ErrInvalidInviteCode";
    ErrorCode[ErrorCode["ErrEnoughUser"] = 34] = "ErrEnoughUser";
    ErrorCode[ErrorCode["ErrNotInvite"] = 35] = "ErrNotInvite";
    ErrorCode[ErrorCode["ErrChgInvite"] = 36] = "ErrChgInvite";
    ErrorCode[ErrorCode["ErrNotInviteCreator"] = 37] = "ErrNotInviteCreator";
    ErrorCode[ErrorCode["ErrForbidKickSelf"] = 38] = "ErrForbidKickSelf";
    ErrorCode[ErrorCode["ErrInviteNotAllReady"] = 40] = "ErrInviteNotAllReady";
    ErrorCode[ErrorCode["ErrInviteStart"] = 41] = "ErrInviteStart";
    ErrorCode[ErrorCode["ErrNotInPair"] = 44] = "ErrNotInPair";
})(ErrorCode = exports.ErrorCode || (exports.ErrorCode = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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