
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/net/MessageId.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f1db1fNQYxAjIe8lTfdT1iF', 'MessageId');
// scripts/net/MessageId.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageId = void 0;
//消息 id
var MessageId;
(function (MessageId) {
    MessageId["MsgTypeCreateWs"] = "CreateWs";
    MessageId["MsgTypeNoticeUserCoin"] = "NoticeUserCoin";
    MessageId["MsgTypeHeartbeat"] = "Heartbeat";
    MessageId["MsgTypeLogin"] = "Login";
    MessageId["MsgTypeUserInfo"] = "UserInfo";
    MessageId["MsgTypePairRequest"] = "PairRequest";
    MessageId["MsgTypeCancelPair"] = "CancelPair";
    MessageId["MsgTypePairResult"] = "PairResult";
    MessageId["MsgTypeEnterRoom"] = "EnterRoom";
    MessageId["MsgTypeSitDown"] = "SitDown";
    MessageId["MsgTypeRobotSitDown"] = "RobotSitDown";
    MessageId["MsgTypeStand"] = "Stand";
    MessageId["MsgTypeReady"] = "Ready";
    MessageId["MsgTypeLeaveRoom"] = "LeaveRoom";
    MessageId["MsgTypeUserOffline"] = "UserOffline";
    MessageId["MsgTypeKickOutUser"] = "KickOutUser";
    MessageId["MsgTypeCreateInvite"] = "CreateInvite";
    MessageId["MsgTypeAcceptInvite"] = "AcceptInvite";
    MessageId["MsgTypeInviteReady"] = "InviteReady";
    MessageId["MsgTypeChgInviteCfg"] = "ChgInviteCfg";
    MessageId["MsgTypeLeaveInvite"] = "LeaveInvite";
    MessageId["MsgTypeNoticeInviteStatus"] = "NoticeInviteStatus";
    MessageId["MsgTypeInviteKickOut"] = "InviteKickOut";
    MessageId["MsgTypeInviteStart"] = "InviteStart";
    MessageId["MsgTypeViewerList"] = "ViewerList";
    MessageId["MsgTypeGameStart"] = "GameStart";
    MessageId["MsgTypeFirstMove"] = "FirstMove";
    MessageId["MsgTypeFirstMoveEnd"] = "FirstMoveEnd";
    MessageId["MsgTypeUserPosList"] = "UserPosList";
    MessageId["MsgTypeRollDice"] = "RollDice";
    MessageId["MsgTypeMoveChess"] = "MoveChess";
    MessageId["MsgTypeUseProp"] = "UseProp";
    MessageId["MsgTypeChoiceProp"] = "ChoiceProp";
    MessageId["MsgTypeChoicePropResult"] = "ChoicePropResult";
    MessageId["MsgTypeChoiceAdvance"] = "ChoiceAdvance";
    MessageId["MsgTypeMoveChessEnd"] = "MoveChessEnd";
    MessageId["MsgTypeSettlement"] = "Settlement";
    MessageId["MsgTypeProductConfigs"] = "ProductConfigs";
    MessageId["MsgTypeBuyProduct"] = "BuyProduct";
    MessageId["MsgTypeSetSkin"] = "SetSkin";
    MessageId["MsgTypeMoveBlock"] = "MoveBlock";
    MessageId["MsgTypeScoreChg"] = "ScoreChg";
    // 扫雷游戏相关消息
    MessageId["MsgTypeNoticeRoundStart"] = "NoticeRoundStart";
    MessageId["MsgTypeNoticeActionDisplay"] = "NoticeActionDisplay";
    MessageId["MsgTypeNoticeRoundEnd"] = "NoticeRoundEnd";
    MessageId["MsgTypeNoticeFirstChoiceBonus"] = "NoticeFirstChoiceBonus";
    MessageId["MsgTypeNoticeGameEnd"] = "NoticeGameEnd";
    MessageId["MsgTypeClickBlock"] = "ClickBlock";
    // 关卡进度相关消息
    MessageId["MsgTypeExtendLevelProgress"] = "ExtendLevelProgress";
    MessageId["MsgTypeExtendLevelInfo"] = "ExtendLevelInfo";
})(MessageId = exports.MessageId || (exports.MessageId = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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