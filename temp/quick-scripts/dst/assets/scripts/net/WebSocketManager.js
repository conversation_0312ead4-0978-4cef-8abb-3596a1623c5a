
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/net/WebSocketManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c0d31NKTstCWK9r83Z2ogy2', 'WebSocketManager');
// scripts/net/WebSocketManager.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketManager = void 0;
var Singleton_1 = require("../../meshTools/Singleton");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var WebSocketTool_1 = require("./WebSocketTool");
var WebSocketManager = /** @class */ (function (_super) {
    __extends(WebSocketManager, _super);
    function WebSocketManager() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.webState = WebSocketTool_1.WebSocketToolState.Closing;
        return _this;
    }
    //连接 socket
    WebSocketManager.prototype.connect = function () {
        WebSocketTool_1.WebSocketTool.GetInstance().setSocketFunction(this.msgData, this.socketState);
        WebSocketTool_1.WebSocketTool.GetInstance().connect(); //连接长链接
    };
    //这个是用来接收服务器返回回来的数据
    WebSocketManager.prototype.msgData = function (msg) {
        var parsedData = JSON.parse(msg);
        if (parsedData.code == 0) { //正常数据
            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, parsedData); //将收到的消息发送出去
        }
        else {
            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveErrorMessage, parsedData); //将收到的消息发送出去
        }
    };
    //这个是接收 websocket 状态的
    WebSocketManager.prototype.socketState = function (webState) {
        GameMgr_1.GameMgr.Console.Log('webSocket状态' + webState);
        WebSocketManager.GetInstance().webState = webState;
    };
    //发送消息  data 请求参数 是个对象{}
    WebSocketManager.prototype.sendMsg = function (msgId, data) {
        var sendMessageBean = {
            msgId: msgId,
            data: data,
        };
        var jsonString = JSON.stringify(sendMessageBean);
        WebSocketTool_1.WebSocketTool.GetInstance().send(jsonString);
    };
    return WebSocketManager;
}(Singleton_1.Singleton));
exports.WebSocketManager = WebSocketManager;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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