
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/net/IHttpMsgBody.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1f204Ig5UFMX41Gjc62LUBj', 'IHttpMsgBody');
// scripts/net/IHttpMsgBody.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL25ldC9JSHR0cE1zZ0JvZHkudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBuYW1lc3BhY2UgSUh0dHBNc2dCb2R5XHJcbntcclxuICAgIGV4cG9ydCBpbnRlcmZhY2UgSVJlc0Jhc2VcclxuICAgIHtcclxuICAgICAgICBjb2RlOiBudW1iZXIsXHJcbiAgICAgICAgZG9jczogc3RyaW5nLFxyXG4gICAgfVxyXG5cclxuICAgIGV4cG9ydCBpbnRlcmZhY2UgSVJlc0dldEdhbWVBZGRyZXNzIGV4dGVuZHMgSVJlc0Jhc2VcclxuICAgIHtcclxuICAgICAgICBkYXRhOiBJR2V0QWRkcmVzc0RhdGFcclxuICAgIH1cclxuXHJcbiAgICBpbnRlcmZhY2UgSUdldEFkZHJlc3NEYXRhIFxyXG4gICAge1xyXG4gICAgICAgIGh0dHBfYWRkcjogc3RyaW5nLFxyXG4gICAgICAgIHdzX2FkZHI6IHN0cmluZyxcclxuICAgIH1cclxuXHJcbiAgICBleHBvcnQgaW50ZXJmYWNlIElSZXFHZXRHYW1lQWRkcmVzc1xyXG4gICAge1xyXG4gICAgICAgIHVzZXJfaWQ6IHN0cmluZyxcclxuICAgICAgICBhcHBfaWQ6IHN0cmluZyxcclxuICAgICAgICBhcHBfY2hhbm5lbDogc3RyaW5nLFxyXG4gICAgICAgIGdhbWVfaWQ6IHN0cmluZyxcclxuICAgICAgICBnYW1lX21vZGU6IHN0cmluZyxcclxuICAgICAgICByb29tX2lkOiBzdHJpbmdcclxuICAgIH1cclxuXHJcbiAgICBleHBvcnQgaW50ZXJmYWNlIElSZXFVcGRhdGVUaW1lIGV4dGVuZHMgSVJlcUdldEdhbWVBZGRyZXNzXHJcbiAgICB7XHJcbiAgICAgICAgY3Vycl9odHRwX2FkZHI6IHN0cmluZyxcclxuICAgICAgICBjdXJyX3dzX2FkZHI6IHN0cmluZ1xyXG4gICAgfVxyXG59Il19