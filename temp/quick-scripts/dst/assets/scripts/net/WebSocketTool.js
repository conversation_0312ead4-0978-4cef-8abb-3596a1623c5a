
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/net/WebSocketTool.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '02a22zuhH5B5IgF4IwvCR8E', 'WebSocketTool');
// scripts/net/WebSocketTool.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketTool = exports.WebSocketToolState = void 0;
var MessageBaseBean_1 = require("./MessageBaseBean");
var MessageId_1 = require("./MessageId");
var Tools_1 = require("../util/Tools");
var Singleton_1 = require("../../meshTools/Singleton");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var WebSocketToolState;
(function (WebSocketToolState) {
    WebSocketToolState["Connecting"] = "connecting";
    WebSocketToolState["Connected"] = "connected";
    WebSocketToolState["Closing"] = "closing";
    WebSocketToolState["Reconnecting"] = "reconnecting";
    WebSocketToolState["Timeout"] = "Timeout";
})(WebSocketToolState = exports.WebSocketToolState || (exports.WebSocketToolState = {}));
var WebSocketTool = /** @class */ (function (_super) {
    __extends(WebSocketTool, _super);
    function WebSocketTool() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.webState = WebSocketToolState.Closing; //默认是关闭的
        _this.reconnectInterval = 5000; // 重连间隔，单位毫秒
        _this.heartbeatInterval = 5000; // 心跳间隔，单位毫秒
        _this.heartbeatTimer = null; //心跳定时器的 id
        _this.reconnectMaxCount = 5; // 最大重连次数
        _this.reconnectCount = 0; // 当前重连次数
        _this.heartTimeOutTimer = null; //心跳超时定时器 id
        _this.heartTimeOutInterval = 11000; //心跳超时间隔，单位毫秒
        _this.msgFunction = null; //这个是回调消息的的
        _this.stateFunction = null; //这个是回调 socket 状态的
        return _this;
    }
    WebSocketTool.prototype.setSocketFunction = function (msgFunction, stateFunction) {
        this.msgFunction = msgFunction;
        this.stateFunction = stateFunction;
    };
    //连接
    WebSocketTool.prototype.connect = function () {
        var _this = this;
        this.activeShutdown = false;
        if (this.webState == WebSocketToolState.Connecting || this.webState == WebSocketToolState.Connected)
            return;
        var url = GameMgr_1.GameMgr.GameData.ServerUrl;
        this.setWebState(WebSocketToolState.Connecting);
        this.ws = new WebSocket(url);
        GameMgr_1.GameMgr.Console.Log('webSocket 链接地址' + url);
        //连接成功
        this.ws.onopen = function () {
            _this.setWebState(WebSocketToolState.Connected);
            // 连接成功后发送心跳包
            _this.sendHeartbeat();
            _this.sendHeartTimeOut();
            _this.reconnectCount = 0; //连接上之后 重置重连次数
        };
        //收到消息
        this.ws.onmessage = function (event) {
            // 处理接收到的消息
            GameMgr_1.GameMgr.Console.Log("webSocket \u6536\u5230\u6D88\u606F(" + Tools_1.Tools.getCurrentTimeWithMilliseconds() + "):" + event.data);
            var parsedData = JSON.parse(event.data);
            if (parsedData.msgId == MessageId_1.MessageId.MsgTypeHeartbeat) {
                _this.sendHeartTimeOut();
            }
            if (_this.msgFunction) {
                _this.msgFunction(event.data);
            }
        };
        //关闭连接
        this.ws.onclose = function () {
            if (_this.webState == WebSocketToolState.Reconnecting) {
                //在 ios 系统中 断网不会断开 websocket，所以在这状态下收到的 链接关闭都是上一个的
                console.error('WebSocket error:', '旧链接关闭');
                return;
            }
            console.error('WebSocket error:', '连接关闭');
            _this.setWebState(WebSocketToolState.Closing);
            _this.linkException();
        };
        //连接异常
        this.ws.onerror = function (event) {
            console.error('WebSocket error:', event);
        };
    };
    //主动断开连接
    WebSocketTool.prototype.disconnect = function () {
        console.error('WebSocket error:', '主动断开');
        this.activeShutdown = true;
        this.ws.close();
    };
    //重连，内部自动调用的
    WebSocketTool.prototype.reconnect = function () {
        var _this = this;
        if (this.webState == WebSocketToolState.Reconnecting || this.webState == WebSocketToolState.Connecting)
            return;
        if (this.reconnectCount >= this.reconnectMaxCount) {
            console.error('WebSocket 重连失败');
            var autoMessageBean = {
                'msgId': MessageBaseBean_1.AutoMessageId.ReconnectionFailureMsg,
                'data': {}
            };
            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            return;
        }
        this.reconnectCount++;
        this.setWebState(WebSocketToolState.Reconnecting);
        // 重连间隔后重新连接
        setTimeout(function () {
            _this.connect();
        }, this.reconnectInterval);
    };
    //给外部调用的马上重连
    WebSocketTool.prototype.atOnceReconnect = function () {
        this.connect();
    };
    //启动心跳
    WebSocketTool.prototype.sendHeartbeat = function () {
        var _this = this;
        if (this.webState != WebSocketToolState.Connected)
            return;
        var sendMessageBean = {
            msgId: MessageId_1.MessageId.MsgTypeHeartbeat,
            data: {},
        };
        var jsonString = JSON.stringify(sendMessageBean);
        // 发送心跳包
        this.send(jsonString);
        // 每隔心跳间隔发送一次心跳包
        this.heartbeatTimer = setTimeout(function () {
            _this.sendHeartbeat();
        }, this.heartbeatInterval);
    };
    WebSocketTool.prototype.sendHeartTimeOut = function () {
        var _this = this;
        this.stopHeartTime();
        this.heartTimeOutTimer = setTimeout(function () {
            if (_this.webState == WebSocketToolState.Connected) {
                console.error('WebSocket 连接超时');
                _this.setWebState(WebSocketToolState.Timeout);
                _this.linkException();
            }
        }, this.heartTimeOutInterval); // 11 秒心跳超时
    };
    //链接异常
    WebSocketTool.prototype.linkException = function () {
        this.stopHeartbeat();
        this.stopHeartTime();
        if (!this.activeShutdown) {
            var autoMessageBean = {
                'msgId': MessageBaseBean_1.AutoMessageId.LinkExceptionMsg,
                'data': {}
            };
            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            // 连接关闭后尝试重连
            this.reconnect();
        }
    };
    //停止心跳
    WebSocketTool.prototype.stopHeartbeat = function () {
        if (this.heartbeatTimer) {
            // 清除心跳定时器
            clearTimeout(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    };
    //停止超时监听
    WebSocketTool.prototype.stopHeartTime = function () {
        if (this.heartTimeOutTimer) {
            clearTimeout(this.heartTimeOutTimer);
            this.heartTimeOutTimer = null;
        }
    };
    //发送消息
    WebSocketTool.prototype.send = function (message) {
        GameMgr_1.GameMgr.Console.Log("webSocket \u53D1\u9001\u6D88\u606F(" + Tools_1.Tools.getCurrentTimeWithMilliseconds() + "):" + message);
        if (this.webState == WebSocketToolState.Connected) {
            this.ws.send(message);
        }
        else {
            GameMgr_1.GameMgr.Console.Error('WebSocket is not connected');
        }
    };
    //赋值 websocket 状态
    WebSocketTool.prototype.setWebState = function (webState) {
        this.webState = webState;
        this.stateFunction(webState);
    };
    WebSocketTool.prototype.onDestroy = function () {
        // 清除心跳定时器
        this.stopHeartbeat();
        this.stopHeartTime();
    };
    return WebSocketTool;
}(Singleton_1.Singleton));
exports.WebSocketTool = WebSocketTool;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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