
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/net/MessageBaseBean.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2cfadyBGwdHeYPuuZnDcrEv', 'MessageBaseBean');
// scripts/net/MessageBaseBean.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoMessageId = void 0;
var AutoMessageId;
(function (AutoMessageId) {
    AutoMessageId["HttpSucceed"] = "httpSucceed";
    AutoMessageId["ReconnectionFailureMsg"] = "reconnectionFailure";
    AutoMessageId["LinkExceptionMsg"] = "LinkExceptionMsg";
    AutoMessageId["GameRouteNotFoundMsg"] = "gameRouteNotFoundMsg";
    AutoMessageId["JumpHallPage"] = "jumpHallPage";
    AutoMessageId["SwitchGameSceneMsg"] = "switchGameScene";
    AutoMessageId["WalletUpdateMsg"] = "walletUpdateMsg";
    AutoMessageId["ServerCodeUpdateMsg"] = "serverCodeUpdateMsg";
    AutoMessageId["LoginSuccessfulMsg"] = "LoginSuccessfulMsg";
    AutoMessageId["FailedToDeductGoldCoins"] = "Failed to deduct gold coins";
})(AutoMessageId = exports.AutoMessageId || (exports.AutoMessageId = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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