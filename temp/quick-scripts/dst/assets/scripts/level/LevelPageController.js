
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
        // 调试：检查当前节点的激活状态
        cc.log("LevelPageController start() - \u5F53\u524D\u8282\u70B9\u6FC0\u6D3B\u72B6\u6001: " + this.node.active);
        cc.log("LevelPageController start() - levelPageNode \u5F15\u7528: " + (this.levelPageNode ? '✅' : '❌'));
        if (this.levelPageNode) {
            cc.log("LevelPageController start() - levelPageNode \u6FC0\u6D3B\u72B6\u6001: " + this.levelPageNode.active);
        }
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        cc.log("关卡页面返回按钮被点击");
        // 弹出确认退出对话框，type=1表示退出本局游戏
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
                cc.log("退出游戏确认对话框已关闭");
            });
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        cc.log("\uD83D\uDE80 LevelPageController.onStartGameButtonClick - \u5F53\u524D\u5173\u5361: " + this.currentLevel);
        // 发送ExtendLevelInfo消息到后端获取地图数据
        var request = {
            levelId: this.currentLevel
        };
        cc.log("\uD83D\uDCE4 \u53D1\u9001ExtendLevelInfo\u8BF7\u6C42:", request);
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        cc.log("\u6536\u5230\u5173\u5361\u4FE1\u606F - \u5F53\u524D\u5173\u5361 " + this.currentLevel, levelInfo);
        cc.log("\u540E\u7AEF\u8FD4\u56DE\u7684levelId: " + levelInfo.levelId + ", levelNumber: " + levelInfo.levelNumber);
        this.currentLevelInfo = levelInfo;
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        cc.log("\u4F7F\u7528\u5173\u5361\u7F16\u53F7 " + this.currentLevel + " \u8FDB\u5165\u5173\u5361");
        this.enterLevel(this.currentLevel);
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
            cc.log("\u66F4\u65B0\u5730\u96F7\u6570UI: " + mineCount);
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
            cc.log("\u66F4\u65B0\u5173\u5361\u6570UI: \u7B2C" + levelNumber + "\u5173");
        }
    };
    /**
     * 根据关卡数进入相应的关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        cc.log("\u8FDB\u5165\u5173\u5361 " + levelNumber);
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 调试：检查地图节点是否已配置
        cc.log("地图节点配置状态：");
        cc.log("qipan8x8Node: " + (this.qipan8x8Node ? '✅' : '❌'));
        cc.log("qipan8x9Node: " + (this.qipan8x9Node ? '✅' : '❌'));
        cc.log("qipan9x9Node: " + (this.qipan9x9Node ? '✅' : '❌'));
        cc.log("qipan9x10Node: " + (this.qipan9x10Node ? '✅' : '❌'));
        cc.log("qipan10x10Node: " + (this.qipan10x10Node ? '✅' : '❌'));
        cc.log("levelS001Node: " + (this.levelS001Node ? '✅' : '❌'));
        // 先隐藏所有地图容器
        this.hideAllMapContainers();
        // 根据关卡数显示对应的地图节点
        if (levelNumber >= 1 && levelNumber <= 4) {
            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8
            this.showGameMap1();
            this.showMapNode(this.qipan8x8Node, "qipan8*8");
        }
        else if (levelNumber === 5) {
            // 第5关，打开level_page/game_map_2/game_bg/Level_S001
            this.showGameMap2();
            this.showMapNode(this.levelS001Node, "Level_S001");
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9
            this.showGameMap1();
            this.showMapNode(this.qipan8x9Node, "qipan8*9");
        }
        else if (levelNumber === 10) {
            // 第10关，打开level_page/game_map_2/game_bg/Level_S002
            this.showGameMap2();
            this.showMapNode(this.levelS002Node, "Level_S002");
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9
            this.showGameMap1();
            this.showMapNode(this.qipan9x9Node, "qipan9*9");
        }
        else if (levelNumber === 15) {
            // 第15关，打开level_page/game_map_2/game_bg/Level_S003
            this.showGameMap2();
            this.showMapNode(this.levelS003Node, "Level_S003");
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10
            this.showGameMap1();
            this.showMapNode(this.qipan9x10Node, "qipan9*10");
        }
        else if (levelNumber === 20) {
            // 第20关，打开level_page/game_map_2/game_bg/Level_S004
            this.showGameMap2();
            this.showMapNode(this.levelS004Node, "Level_S004");
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 25) {
            // 第25关，打开level_page/game_map_2/game_bg/Level_S005
            this.showGameMap2();
            this.showMapNode(this.levelS005Node, "Level_S005");
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 30) {
            // 第30关，打开level_page/game_map_2/game_bg/Level_S006
            this.showGameMap2();
            this.showMapNode(this.levelS006Node, "Level_S006");
        }
        else {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
        }
    };
    /**
     * 显示指定的地图节点
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNode = function (mapNode, mapName) {
        if (mapNode) {
            cc.log("\uD83D\uDD0D \u51C6\u5907\u663E\u793A\u5730\u56FE\u8282\u70B9: " + mapName);
            cc.log("\u8282\u70B9\u8DEF\u5F84: " + mapNode.name);
            cc.log("\u8282\u70B9\u5F53\u524D\u72B6\u6001 - active: " + mapNode.active + ", parent: " + (mapNode.parent ? mapNode.parent.name : 'null'));
            mapNode.active = true;
            // 检查父节点链是否都是激活状态
            var currentNode = mapNode.parent;
            var parentChain = [];
            while (currentNode) {
                // 避免访问场景节点的 active 属性
                if (currentNode.name === 'game_scene' || currentNode.name === 'Scene') {
                    parentChain.push(currentNode.name + "(Scene)");
                }
                else {
                    parentChain.push(currentNode.name + "(" + currentNode.active + ")");
                }
                currentNode = currentNode.parent;
            }
            cc.log("\u7236\u8282\u70B9\u94FE: " + parentChain.join(' -> '));
            cc.log("\u2705 \u663E\u793A\u5730\u56FE\u8282\u70B9: " + mapName + " - \u8BBE\u7F6E\u540E\u72B6\u6001: " + mapNode.active);
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
            cc.warn("\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u4E3A LevelPageController \u914D\u7F6E " + mapName + " \u8282\u70B9\u5C5E\u6027");
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        cc.log("\uD83C\uDFAF LevelPageController.setCurrentLevel \u88AB\u8C03\u7528: " + this.currentLevel + " -> " + levelNumber);
        this.currentLevel = levelNumber;
        cc.log("\u2705 \u8BBE\u7F6E\u5F53\u524D\u5173\u5361\u5B8C\u6210: " + levelNumber);
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
            cc.log("✅ 确保 level_page 节点激活");
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
            cc.log("隐藏 game_map_1 容器");
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
            cc.log("隐藏 game_map_2 容器");
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
            cc.log("✅ 显示 game_map_1 容器");
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
            cc.log("✅ 显示 game_map_2 容器");
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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