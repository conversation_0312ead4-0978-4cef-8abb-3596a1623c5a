
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/bean/EnumBean.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a3026CGwJlCfJrx0ZY7AvdX', 'EnumBean');
// scripts/bean/EnumBean.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MoveType = exports.RewardType = exports.Obstacle = exports.BlockType = exports.BlockColor = exports.GameStatus = exports.UserStatus = exports.RoomType = void 0;
// RoomType 房间类型
var RoomType;
(function (RoomType) {
    RoomType[RoomType["RoomTypeCommon"] = 1] = "RoomTypeCommon";
    RoomType[RoomType["RoomTypePrivate"] = 2] = "RoomTypePrivate";
    RoomType[RoomType["RoomTypeVoice"] = 3] = "RoomTypeVoice";
})(RoomType = exports.RoomType || (exports.RoomType = {}));
// UserStatus 用户状态
var UserStatus;
(function (UserStatus) {
    UserStatus[UserStatus["UserStatusStand"] = 1] = "UserStatusStand";
    UserStatus[UserStatus["UserStatusSit"] = 2] = "UserStatusSit";
})(UserStatus = exports.UserStatus || (exports.UserStatus = {}));
// GameStatus 游戏状态
var GameStatus;
(function (GameStatus) {
    GameStatus[GameStatus["GameStatusMove"] = 1] = "GameStatusMove";
})(GameStatus = exports.GameStatus || (exports.GameStatus = {}));
// BlockColor 块颜色
var BlockColor;
(function (BlockColor) {
    BlockColor[BlockColor["BlockColorRed"] = 1] = "BlockColorRed";
    BlockColor[BlockColor["BlockColorBlue"] = 2] = "BlockColorBlue";
    BlockColor[BlockColor["BlockColorGreen"] = 3] = "BlockColorGreen";
    BlockColor[BlockColor["BlockColorYellow"] = 4] = "BlockColorYellow";
    BlockColor[BlockColor["BlockColorBrown"] = 5] = "BlockColorBrown";
})(BlockColor = exports.BlockColor || (exports.BlockColor = {}));
// BlockType 块类型(普通块、被动技能块)
var BlockType;
(function (BlockType) {
    BlockType[BlockType["BlockTypeCommon"] = 1] = "BlockTypeCommon";
    BlockType[BlockType["BlockTypeArrowX"] = 2] = "BlockTypeArrowX";
    BlockType[BlockType["BlockTypeArrowY"] = 3] = "BlockTypeArrowY";
    BlockType[BlockType["BlockTypeBomb"] = 4] = "BlockTypeBomb";
    BlockType[BlockType["BlockTypeRainbow"] = 5] = "BlockTypeRainbow";
    BlockType[BlockType["BlockTypeSuperArrow"] = 6] = "BlockTypeSuperArrow";
    BlockType[BlockType["BlockTypeBombArrow"] = 7] = "BlockTypeBombArrow";
    BlockType[BlockType["BlockTypeRainbowArrow"] = 8] = "BlockTypeRainbowArrow";
    BlockType[BlockType["BlockTypeSuperBomb"] = 9] = "BlockTypeSuperBomb";
    BlockType[BlockType["BlockTypeRainbowBomb"] = 10] = "BlockTypeRainbowBomb";
    BlockType[BlockType["BlockTypeSuperRain"] = 11] = "BlockTypeSuperRain";
})(BlockType = exports.BlockType || (exports.BlockType = {}));
// Obstacle 障碍物类型
var Obstacle;
(function (Obstacle) {
    Obstacle[Obstacle["ObstacleDefault"] = 0] = "ObstacleDefault";
    Obstacle[Obstacle["ObstacleChain"] = 1000] = "ObstacleChain";
    Obstacle[Obstacle["ObstacleIce"] = 1001] = "ObstacleIce";
    Obstacle[Obstacle["ObstacleChainIce"] = 1002] = "ObstacleChainIce";
})(Obstacle = exports.Obstacle || (exports.Obstacle = {}));
//实时任务的类型
var RewardType;
(function (RewardType) {
    // (1-彩虹、2-冰块、3-锁链、4-箭头X、5-箭头Y)
    RewardType[RewardType["RAINBOW"] = 1] = "RAINBOW";
    RewardType[RewardType["ICE"] = 2] = "ICE";
    RewardType[RewardType["LOCK"] = 3] = "LOCK";
    RewardType[RewardType["ARROW_X"] = 4] = "ARROW_X";
    RewardType[RewardType["ARROW_Y"] = 5] = "ARROW_Y";
})(RewardType = exports.RewardType || (exports.RewardType = {}));
var MoveType;
(function (MoveType) {
    MoveType[MoveType["MoveDefault"] = 0] = "MoveDefault";
    MoveType[MoveType["MoveStart"] = 1] = "MoveStart";
    MoveType[MoveType["MoveEnd"] = 2] = "MoveEnd";
})(MoveType = exports.MoveType || (exports.MoveType = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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