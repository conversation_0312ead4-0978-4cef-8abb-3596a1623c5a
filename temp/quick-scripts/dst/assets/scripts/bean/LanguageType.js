
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/bean/LanguageType.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '83c31BPQVZMSJTrcsJTL4Yt', 'LanguageType');
// scripts/bean/LanguageType.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var LanguageType;
(function (LanguageType) {
    /** 简体中文 */
    LanguageType["SimplifiedChinese"] = "0";
    LanguageType["SimplifiedChinese_type"] = "zh_CN";
    /** 繁体中文 */
    LanguageType["TraditionalChinese"] = "1";
    LanguageType["TraditionalChinese_type"] = "zh_HK";
    /** 英文 */
    LanguageType["English"] = "2";
    LanguageType["English_type"] = "en";
    /** 印尼语 */
    LanguageType["Indonesian"] = "3";
    LanguageType["Indonesian_type"] = "id";
    /** 马来语 */
    LanguageType["Malay"] = "4";
    LanguageType["Malay_type"] = "ms_MY";
    /** 泰语 */
    LanguageType["Thai"] = "5";
    LanguageType["Thai_type"] = "th";
    /** 越南语 */
    LanguageType["Vietnamese"] = "6";
    LanguageType["Vietnamese_type"] = "vi_VN";
    /** 阿拉伯语 */
    LanguageType["Arabic"] = "7";
    LanguageType["Arabic_type"] = "ar";
    /** 菲律宾语 */
    LanguageType["Filipino"] = "8";
    LanguageType["Filipino_type"] = "fil";
    /** 葡萄牙语 */
    LanguageType["Portuguese"] = "9";
    LanguageType["Portuguese_type"] = "pt_br";
    /** 土耳其语 */
    LanguageType["Turkish"] = "10";
    LanguageType["Turkish_type"] = "tur";
    /** 乌尔都语 */
    LanguageType["Urdu"] = "11";
    LanguageType["Urdu_type"] = "ur";
    /** 日语 */
    LanguageType["Japanese"] = "12";
    LanguageType["Japanese_type"] = "ja";
    /** 俄语 */
    LanguageType["Russian"] = "13";
    LanguageType["Russian_type"] = "ru";
    /** 西班牙语 */
    LanguageType["Spanish"] = "14";
    LanguageType["Spanish_type"] = "es";
})(LanguageType || (LanguageType = {}));
exports.default = LanguageType;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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