
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/bean/GlobalBean.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'adb11KURoVCvZ/ECr2VcXvT', 'GlobalBean');
// scripts/bean/GlobalBean.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalBean = void 0;
var Singleton_1 = require("../../meshTools/Singleton");
var HallAutoController_1 = require("../hall/HallAutoController");
var GlobalBean = /** @class */ (function (_super) {
    __extends(GlobalBean, _super);
    function GlobalBean() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.loginData = null; //用户数据
        _this.autoAndRoom = HallAutoController_1.AutoOrRoom.AUTO; //当前选中的是哪种游戏模式
        _this.players = 2; //玩家人数 默认两个人
        _this.ticketsNum = 0; //门票价格
        return _this;
    }
    GlobalBean.prototype.cleanData = function () {
        this.noticeStartGame = null;
        this.inviteInfo = null;
    };
    //调整 users 数据的展示顺序把自己的数据调整到第一位， 并不是调整座位数据
    GlobalBean.prototype.adjustUserData = function () {
        var user = this.noticeStartGame.users;
        var index = user.findIndex(function (item) { return item.userId === GlobalBean.GetInstance().loginData.userInfo.userId; });
        if (index !== -1) {
            var element = this.noticeStartGame.users.splice(index, 1)[0];
            user.unshift(element);
        }
        // 确保所有用户都有score字段
        user.forEach(function (u) {
            if (u.score === undefined || u.score === null) {
                u.score = 0;
            }
        });
        return user;
    };
    GlobalBean.prototype.getQueue = function () {
        return BlockingQueue.getInstance(1000);
    };
    return GlobalBean;
}(Singleton_1.Singleton));
exports.GlobalBean = GlobalBean;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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