
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/pfb/CongratsItemController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4483cBqWUJAG7hafZbyIkLC', 'CongratsItemController');
// scripts/pfb/CongratsItemController.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Publish_1 = require("../../meshTools/tools/Publish");
var Config_1 = require("../util/Config");
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var ccclass = cc._decorator.ccclass;
var CongratsItemController = /** @class */ (function (_super) {
    __extends(CongratsItemController, _super);
    function CongratsItemController() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    CongratsItemController.prototype.start = function () {
        this.boardIconCrownNode = this.node.getChildByName('board_icon_crown'); //皇冠的节点
        this.boardNum = this.node.getChildByName('board_num'); //皇冠的节点
        this.avatarNode = this.node.getChildByName('avatar'); //头像的节点
        this.nameNode = this.node.getChildByName('name_layout').getChildByName('name'); //名称的节点
        this.stepNode = this.node.getChildByName("step_layout").getChildByName('step'); // 步数的节点
        this.numberNode = this.node.getChildByName('congrats_list_frame').getChildByName('number_view').getChildByName('number'); //金豆的节点
        this.beanIcon = this.node.getChildByName('congrats_list_frame').getChildByName('board_icon_beans'); //金豆图标
    };
    //设置数据
    //settleType:结算类型
    //intUserID:中断游戏的玩家Id
    CongratsItemController.prototype.createData = function (settlement, gameUsers) {
        if (settlement.rank <= 3) {
            this.boardIconCrownNode.active = true;
            this.boardNum.active = false;
            //设置皇冠图片
            switch (settlement.rank) {
                case 1:
                    Tools_1.Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config_1.Config.gameRes + 'board_icon_crown_01');
                    break;
                case 2:
                    Tools_1.Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config_1.Config.gameRes + 'board_icon_crown_02');
                    break;
                case 3:
                    Tools_1.Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config_1.Config.gameRes + 'board_icon_crown_03');
                    break;
            }
        }
        else {
            this.boardIconCrownNode.active = false;
            this.boardNum.active = true;
            this.boardNum.getComponent(cc.Label).string = settlement.rank + ''; //显示名次
        }
        // 根据数据类型显示不同的分数字段
        var scoreValue;
        if ('score' in settlement) {
            // UserSettlement 类型
            scoreValue = settlement.score;
        }
        else {
            // PlayerFinalResult 类型
            scoreValue = settlement.totalScore;
        }
        this.stepNode.getComponent(cc.Label).string = scoreValue + ''; //显示分数
        var index = gameUsers.findIndex(function (item) { return item.userId === settlement.userId; }); //搜索
        if (index != -1) {
            var user = gameUsers[index];
            Tools_1.Tools.setNodeSpriteFrameUrl(this.avatarNode, user.avatar);
            this.nameNode.getComponent(NickNameLabel_1.default).string = user.nickName;
        }
        this.numberNode.getComponent(cc.Label).string = Tools_1.Tools.NumToTBMK(settlement.coinChg);
        if (Publish_1.Publish.GetInstance().currencyIcon != null && Publish_1.Publish.GetInstance().currencyIcon !== '') {
            Tools_1.Tools.setNodeSpriteFrameUrl(this.beanIcon, Publish_1.Publish.GetInstance().currencyIcon);
        }
    };
    CongratsItemController = __decorate([
        ccclass
    ], CongratsItemController);
    return CongratsItemController;
}(cc.Component));
exports.default = CongratsItemController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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