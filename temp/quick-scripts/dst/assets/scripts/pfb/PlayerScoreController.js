
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/pfb/PlayerScoreController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fece9VdqbVCdoFnIuu8FUgl', 'PlayerScoreController');
// scripts/pfb/PlayerScoreController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerScoreController = /** @class */ (function (_super) {
    __extends(PlayerScoreController, _super);
    function PlayerScoreController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.nameLabel = null; //用户昵称
        // 分数显示相关节点
        _this.scoreBgMy = null; //我的分数背景节点 score_bg_my
        _this.scoreBgOthers = null; //其他人的分数背景节点 score_bg_others
        // 加减分效果节点
        _this.addScoreNode = null; //加分背景节点 addscore
        _this.subScoreNode = null; //减分背景节点 deductscore
        // 当前用户数据
        _this.currentUser = null;
        return _this;
    }
    PlayerScoreController.prototype.start = function () {
        // 初始化时隐藏所有加减分效果
        this.hideScoreEffects();
    };
    /**
     * 设置玩家数据
     * @param user 房间用户数据
     */
    PlayerScoreController.prototype.setData = function (user) {
        this.currentUser = user;
        if (user == null) {
            // 清空数据
            this.avatar.active = false;
            this.nameLabel.string = "";
            this.hideAllScoreBackgrounds();
            this.hideScoreEffects();
        }
        else {
            // 设置头像和昵称
            Tools_1.Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);
            var nicknameLabel = this.nameLabel.getComponent(NickNameLabel_1.default);
            nicknameLabel.string = user.nickName;
            this.avatar.active = true;
            // 设置分数显示
            this.updateScore(user.score || 0);
        }
    };
    /**
     * 更新分数显示
     * @param score 新的分数值
     */
    PlayerScoreController.prototype.updateScore = function (score) {
        if (!this.currentUser)
            return;
        var isMyself = this.isCurrentUser(this.currentUser.userId);
        if (isMyself) {
            // 显示我的分数
            this.showMyScore(score);
        }
        else {
            // 显示其他人的分数
            this.showOthersScore(score);
        }
    };
    /**
     * 判断是否为当前登录用户
     * @param userId 用户ID
     */
    PlayerScoreController.prototype.isCurrentUser = function (userId) {
        var _a, _b;
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        return userId === currentUserId;
    };
    /**
     * 显示我的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showMyScore = function (score) {
        // 显示我的分数背景，隐藏其他人的
        if (this.scoreBgMy) {
            this.scoreBgMy.active = true;
            // 获取my_score文本节点并设置分数
            var myScoreLabel = this.scoreBgMy.getChildByName("my_score");
            if (myScoreLabel) {
                var labelComponent = myScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示其他人的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showOthersScore = function (score) {
        // 显示其他人的分数背景，隐藏我的
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = true;
            // 获取other_score文本节点并设置分数
            var otherScoreLabel = this.scoreBgOthers.getChildByName("other_score");
            if (otherScoreLabel) {
                var labelComponent = otherScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
    };
    /**
     * 隐藏所有分数背景
     */
    PlayerScoreController.prototype.hideAllScoreBackgrounds = function () {
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示加分效果
     * @param addValue 加分数值
     */
    PlayerScoreController.prototype.showAddScore = function (addValue) {
        var _this = this;
        if (this.addScoreNode) {
            this.addScoreNode.active = true;
            // 获取change_score文本节点并设置加分文本
            var changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.addScoreNode) {
                    _this.addScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    PlayerScoreController.prototype.showSubScore = function (subValue) {
        var _this = this;
        if (this.subScoreNode) {
            this.subScoreNode.active = true;
            // 获取change_score文本节点并设置减分文本
            var changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.subScoreNode) {
                    _this.subScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 隐藏加减分效果节点
     */
    PlayerScoreController.prototype.hideScoreEffects = function () {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    };
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Label)
    ], PlayerScoreController.prototype, "nameLabel", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgMy", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgOthers", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "addScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "subScoreNode", void 0);
    PlayerScoreController = __decorate([
        ccclass
    ], PlayerScoreController);
    return PlayerScoreController;
}(cc.Component));
exports.default = PlayerScoreController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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