
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/pfb/SeatItemController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '282a9v0jv9KApt/IYgxbleE', 'SeatItemController');
// scripts/pfb/SeatItemController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var SeatItemController = /** @class */ (function (_super) {
    __extends(SeatItemController, _super);
    function SeatItemController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatarFrame = null;
        _this.avatar = null;
        _this.seatEmpty = null;
        _this.nameLabel = null;
        _this.ready = null;
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    SeatItemController.prototype.start = function () {
    };
    SeatItemController.prototype.setData = function (users) {
        this.users = users;
        if (users == null) {
            this.nameLabel.string = window.getLocalizedStr('Empty');
            this.nameLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#F4C684');
            this.seatEmpty.active = true;
            this.avatarFrame.active = false;
            this.avatar.active = false;
            this.ready.active = false;
        }
        else {
            this.nameLabel.string = users.nickname;
            this.seatEmpty.active = false;
            this.avatarFrame.active = true;
            this.avatar.active = true;
            this.ready.active = users.ready;
            this.nameLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#D07649');
            Tools_1.Tools.setNodeSpriteFrameUrl(this.avatar, users.avatar);
        }
    };
    SeatItemController.prototype.getUsers = function () {
        return this.users;
    };
    __decorate([
        property(cc.Node)
    ], SeatItemController.prototype, "avatarFrame", void 0);
    __decorate([
        property(cc.Node)
    ], SeatItemController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Node)
    ], SeatItemController.prototype, "seatEmpty", void 0);
    __decorate([
        property(NickNameLabel_1.default)
    ], SeatItemController.prototype, "nameLabel", void 0);
    __decorate([
        property(cc.Node)
    ], SeatItemController.prototype, "ready", void 0);
    SeatItemController = __decorate([
        ccclass
    ], SeatItemController);
    return SeatItemController;
}(cc.Component));
exports.default = SeatItemController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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