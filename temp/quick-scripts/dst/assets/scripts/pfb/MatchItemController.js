
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/pfb/MatchItemController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '17d65tE2S5HU4rq7OdoOrej', 'MatchItemController');
// scripts/pfb/MatchItemController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var MatchItemController = /** @class */ (function (_super) {
    __extends(MatchItemController, _super);
    function MatchItemController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.gundongtouxiang = null; //滚动头像
        _this.nameLabel = null; //用户昵称
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    MatchItemController.prototype.start = function () {
    };
    MatchItemController.prototype.setData = function (user) {
        var _this = this;
        this.scheduleOnce(function () {
            if (user == null) {
                _this.gundongtouxiang.active = true;
                _this.avatar.active = false;
            }
            else {
                Tools_1.Tools.setNodeSpriteFrameUrl(_this.avatar, user.avatar); //添加头像
                var nicknameLabel = _this.nameLabel.getComponent(NickNameLabel_1.default);
                nicknameLabel.string = user.nickName; //添加昵称
                _this.gundongtouxiang.active = false;
                _this.avatar.active = true;
            }
        }, 0.1);
    };
    __decorate([
        property(cc.Node)
    ], MatchItemController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Node)
    ], MatchItemController.prototype, "gundongtouxiang", void 0);
    __decorate([
        property(cc.Label)
    ], MatchItemController.prototype, "nameLabel", void 0);
    MatchItemController = __decorate([
        ccclass
    ], MatchItemController);
    return MatchItemController;
}(cc.Component));
exports.default = MatchItemController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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