
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/common/MineConsole.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b7ed6JVjjlO9KYRYoBoofK2', 'MineConsole');
// scripts/common/MineConsole.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Singleton_1 = require("../../meshTools/Singleton");
var MineConsole = /** @class */ (function (_super) {
    __extends(MineConsole, _super);
    function MineConsole() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.IsShowConsole = true;
        return _this;
    }
    MineConsole.prototype.showInfo = function (callback) {
        if (callback === void 0) { callback = null; }
        if (this.IsShowConsole) {
            callback && callback();
        }
    };
    MineConsole.prototype.Log = function () {
        var data = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            data[_i] = arguments[_i];
        }
        this.showInfo(function () {
            console.log.apply(console, data);
        });
    };
    MineConsole.prototype.Info = function () {
        var data = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            data[_i] = arguments[_i];
        }
        this.showInfo(function () {
            console.info.apply(console, data);
        });
    };
    MineConsole.prototype.Warn = function () {
        // this.showInfo(() =>
        // {
        //     console.warn(...data);
        // });
        var data = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            data[_i] = arguments[_i];
        }
        console.warn.apply(console, data);
    };
    MineConsole.prototype.Error = function () {
        // this.showInfo(() =>
        // {
        //     console.error(...data);
        // });
        var data = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            data[_i] = arguments[_i];
        }
        console.error.apply(console, data);
    };
    MineConsole.prototype.Group = function (groupKey) {
        var data = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            data[_i - 1] = arguments[_i];
        }
        this.showInfo(function () {
            console.group(groupKey);
            console.log.apply(console, data);
            console.groupEnd();
        });
    };
    MineConsole.prototype.LogString = function (str, color) {
        if (color === void 0) { color = cc.Color.WHITE; }
        this.showInfo(function () {
            var style = "background-color:#" + color.toHEX("#rrggbb");
            cc.sys.isBrowser ? console.log("%c" + str, style) : console.log(str);
        });
    };
    MineConsole.prototype.Table = function (data) {
        this.showInfo(function () {
            cc.sys.isBrowser ? console.table(data) : console.log(data);
        });
    };
    MineConsole.prototype.Assert = function (correctConditions) {
        var data = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            data[_i - 1] = arguments[_i];
        }
        this.showInfo(function () {
            console.assert.apply(console, __spreadArrays([correctConditions], data));
        });
    };
    MineConsole.prototype.Time = function (funcKey, func) {
        if (func === void 0) { func = null; }
        var params = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            params[_i - 2] = arguments[_i];
        }
        this.showInfo(function () {
            console.time(funcKey);
            func && func.apply(void 0, params);
            console.timeEnd(funcKey);
        });
    };
    MineConsole.prototype.TimeInterval = function (key) {
        var _this = this;
        var time = new Date().getTime();
        return function () {
            var nowTime = new Date().getTime();
            _this.Log(key + " time: " + ((nowTime - time) / 1000) + "s");
        };
    };
    return MineConsole;
}(Singleton_1.Singleton));
exports.default = MineConsole;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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