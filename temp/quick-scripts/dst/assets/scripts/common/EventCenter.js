
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/common/EventCenter.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f9b83/qkM9GlaEvLsvMwW8P', 'EventCenter');
// scripts/common/EventCenter.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventType = exports.EventCenter = void 0;
var Singleton_1 = require("../../meshTools/Singleton");
var GameMgr_1 = require("./GameMgr");
var EventCenter = /** @class */ (function (_super) {
    __extends(EventCenter, _super);
    function EventCenter() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._handle = {};
        return _this;
    }
    EventCenter.prototype.AddEventListener = function (eventName, cb, target) {
        GameMgr_1.GameMgr.Console.Log('添加监听：' + eventName);
        eventName = eventName.toString();
        if (!this._handle[eventName]) {
            this._handle[eventName] = [];
        }
        var eventData = {};
        eventData.cb = cb;
        eventData.target = target;
        this._handle[eventName].push(eventData);
    };
    EventCenter.prototype.RemoveEventListener = function (eventName, cb, target) {
        var _a;
        GameMgr_1.GameMgr.Console.Log('移除监听：' + eventName);
        eventName = eventName.toString();
        var list = (_a = this._handle[eventName]) !== null && _a !== void 0 ? _a : [];
        if (list.length <= 0) {
            return;
        }
        for (var i = 0; i < list.length; ++i) {
            var event = list[i];
            if (event.cb === cb && (!target || target === event.target)) {
                list.splice(i, 1);
            }
        }
    };
    EventCenter.prototype.Send = function (eventName) {
        var _a;
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        eventName = eventName.toString();
        var list = (_a = this._handle[eventName]) !== null && _a !== void 0 ? _a : [];
        if (list.length <= 0) {
            return;
        }
        for (var i = 0; i < list.length; ++i) {
            var event = list[i];
            event.cb.apply(event.target, params);
        }
    };
    return EventCenter;
}(Singleton_1.Singleton));
exports.EventCenter = EventCenter;
//通知的消息类型
var EventType;
(function (EventType) {
    EventType["ReceiveMessage"] = "receiveMessage";
    EventType["ReceiveErrorMessage"] = "receiveErrorMessage";
    EventType["AutoMessage"] = "autoMessage";
})(EventType = exports.EventType || (exports.EventType = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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