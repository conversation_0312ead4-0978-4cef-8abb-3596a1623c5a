
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/common/GameData.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '50d67lS8N5Ehr/tRCitXjWZ', 'GameData');
// scripts/common/GameData.ts

"use strict";
/*
 * @Author: Tony
 * @Date: 2023-03-15 11:47:21
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameData = void 0;
var MeshTools_1 = require("../../meshTools/MeshTools");
var Singleton_1 = require("../../meshTools/Singleton");
var GameServerUrl_1 = require("../net/GameServerUrl");
var GameData = /** @class */ (function (_super) {
    __extends(GameData, _super);
    function GameData() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.GameIsInFront = true; //true 游戏中没有压后台  false 压后台了  
        return _this;
    }
    Object.defineProperty(GameData.prototype, "ServerUrl", {
        // ?env=0
        // &game_id=1051
        // &game_mode=3
        // &user_id=667055436
        // &country=EN
        // &code=RW0WXfzGQmTw8gqDkIg5LfUm0UBGN6UTFAynqhENZjVgehdufnSlQhmseHHW
        // &app_id=66666666
        // &app_channel=mesh
        // &room_id=room01
        // &role=1
        get: function () {
            var url = GameServerUrl_1.GameServerUrl.Ws +
                '?env=' + MeshTools_1.MeshTools.Publish.getEnv() +
                '&version=' + MeshTools_1.MeshTools.Publish.getVersion() +
                '&game_id=' + MeshTools_1.MeshTools.Publish.getGameId() +
                '&game_mode=' + MeshTools_1.MeshTools.Publish.gameMode +
                '&user_id=' + MeshTools_1.MeshTools.Publish.userId +
                '&country=' + MeshTools_1.MeshTools.Publish.language +
                '&code=' + MeshTools_1.MeshTools.Publish.code +
                '&app_id=' + MeshTools_1.MeshTools.Publish.appId +
                '&app_channel=' + MeshTools_1.MeshTools.Publish.appChannel +
                '&room_id=' + MeshTools_1.MeshTools.Publish.roomId;
            return url;
        },
        enumerable: false,
        configurable: true
    });
    return GameData;
}(Singleton_1.Singleton));
exports.GameData = GameData;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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