
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/common/GameMgr.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '17fd4BJ6+1INYp143EG7XAk', 'GameMgr');
// scripts/common/GameMgr.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameMgr = void 0;
var MeshSdkApi_1 = require("../../meshTools/tools/MeshSdkApi");
var EventCenter_1 = require("./EventCenter");
var GameData_1 = require("./GameData");
var GameTools_1 = require("./GameTools");
var MineConsole_1 = require("./MineConsole");
var GameMgr = /** @class */ (function () {
    function GameMgr() {
    }
    Object.defineProperty(GameMgr, "H5SDK", {
        get: function () {
            if (GameMgr._h5SDK == null) {
                GameMgr._h5SDK = new MeshSdkApi_1.default();
            }
            return GameMgr._h5SDK;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameMgr, "GameData", {
        get: function () {
            return GameData_1.GameData.GetInstance();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameMgr, "Event", {
        get: function () {
            return EventCenter_1.EventCenter.GetInstance();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameMgr, "Utils", {
        get: function () {
            return GameTools_1.GameTools.GetInstance();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameMgr, "Console", {
        get: function () {
            return MineConsole_1.default.GetInstance();
        },
        enumerable: false,
        configurable: true
    });
    GameMgr._h5SDK = null;
    return GameMgr;
}());
exports.GameMgr = GameMgr;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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