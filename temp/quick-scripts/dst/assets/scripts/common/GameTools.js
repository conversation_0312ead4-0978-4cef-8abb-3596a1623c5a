
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/common/GameTools.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fe2d52E2MxMKarwZe5xDbAh', 'GameTools');
// scripts/common/GameTools.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameTools = void 0;
var Singleton_1 = require("../../meshTools/Singleton");
var GameTools = /** @class */ (function (_super) {
    __extends(GameTools, _super);
    function GameTools() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    GameTools.prototype.GetUrlParams = function (url) {
        var urlArr = url.split("?");
        var data = {};
        if (urlArr.length === 1)
            return data;
        for (var i = 1; i <= urlArr.length - 1; i++) {
            var paramsStr = decodeURIComponent(urlArr[i]);
            if (paramsStr && paramsStr !== 'undefined') {
                var paramsArr = paramsStr.split("&");
                paramsArr.forEach(function (str) {
                    var _a = str.split("="), key = _a[0], rest = _a.slice(1);
                    var value = rest.join("=");
                    if (key)
                        data[key] = value;
                });
            }
        }
        return data;
    };
    return GameTools;
}(Singleton_1.Singleton));
exports.GameTools = GameTools;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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