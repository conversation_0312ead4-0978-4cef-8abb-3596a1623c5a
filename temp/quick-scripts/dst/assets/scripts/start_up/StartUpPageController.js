
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/start_up/StartUpPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f764exjceBDhJY0TWFTY4tZ', 'StartUpPageController');
// scripts/start_up/StartUpPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var StartUpCenterController_1 = require("./StartUpCenterController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var StartUpPageController = /** @class */ (function (_super) {
    __extends(StartUpPageController, _super);
    function StartUpPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.startUpCenterController = null;
        return _this;
        // update (dt) {}
    }
    StartUpPageController.prototype.start = function () {
        //这是是通知 web 端 游戏加载完成
        this.scheduleOnce(function () {
            if (window.closeLoadingBg) {
                window.closeLoadingBg();
            }
            GameMgr_1.GameMgr.H5SDK.HideLoading();
        }, 0.1);
    };
    //设置登录成功
    StartUpPageController.prototype.setLogin = function () {
        this.startUpCenterController.setLogin();
    };
    __decorate([
        property(StartUpCenterController_1.default)
    ], StartUpPageController.prototype, "startUpCenterController", void 0);
    StartUpPageController = __decorate([
        ccclass
    ], StartUpPageController);
    return StartUpPageController;
}(cc.Component));
exports.default = StartUpPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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