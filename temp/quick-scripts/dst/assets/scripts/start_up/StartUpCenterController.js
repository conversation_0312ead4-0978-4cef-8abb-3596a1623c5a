
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/start_up/StartUpCenterController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9ba1dVF68pFvbF9tS0dicZi', 'StartUpCenterController');
// scripts/start_up/StartUpCenterController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var MessageBaseBean_1 = require("../net/MessageBaseBean");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var StartUpCenterController = /** @class */ (function (_super) {
    __extends(StartUpCenterController, _super);
    function StartUpCenterController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.progress = null;
        _this.progressLabel = null;
        _this.countdownInterval = null; //倒计时的 id
        _this.login = false; //登录是否成功
        _this.loding = false; //加载是否成功
        _this.preload = false; //加载是否成功
        return _this;
        // update (dt) {}
    }
    StartUpCenterController.prototype.onLoad = function () {
        var _this = this;
        cc.resources.preloadDir(Config_1.Config.buttonRes, cc.SpriteAtlas, function (error, items) {
            if (error) {
                GameMgr_1.GameMgr.Console.Log('预加载按钮资源失败');
            }
            else {
                GameMgr_1.GameMgr.Console.Log('预加载按钮资源成功');
                _this.preload = true;
                _this.jumpHall();
            }
        }); //提前预加载图片
    };
    StartUpCenterController.prototype.onEnable = function () {
        this.updateCountdownLabel(0);
        this.startCountdown();
    };
    StartUpCenterController.prototype.start = function () {
    };
    StartUpCenterController.prototype.startCountdown = function () {
        var _this = this;
        var remainingSeconds = 0;
        this.countdownInterval = setInterval(function () {
            remainingSeconds++;
            if (remainingSeconds > 100) {
                clearInterval(_this.countdownInterval);
                _this.countdownInterval = null;
                _this.loding = true;
                // 进度到 100的处理
                _this.jumpHall();
                return;
            }
            _this.updateCountdownLabel(remainingSeconds);
        }, 10);
    };
    StartUpCenterController.prototype.updateCountdownLabel = function (percent) {
        this.progressLabel.string = percent + "%";
        this.progress.fillRange = percent / 100;
    };
    StartUpCenterController.prototype.onDisable = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    StartUpCenterController.prototype.setLogin = function () {
        this.login = true;
        this.jumpHall();
    };
    StartUpCenterController.prototype.jumpHall = function () {
        //加载成功 并且登录成功 才允许跳转大厅页面
        if (this.loding && this.login && this.preload) {
            var autoMessageBean = {
                'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                'data': { 'type': 1 } //1是启动页面跳转的
            };
            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
        }
    };
    __decorate([
        property(cc.Sprite)
    ], StartUpCenterController.prototype, "progress", void 0);
    __decorate([
        property(cc.Label)
    ], StartUpCenterController.prototype, "progressLabel", void 0);
    StartUpCenterController = __decorate([
        ccclass
    ], StartUpCenterController);
    return StartUpCenterController;
}(cc.Component));
exports.default = StartUpCenterController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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