
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/meshTools/BaseSDK.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b65e79RMudAKablFCxs6O0q', 'BaseSDK');
// meshTools/BaseSDK.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseSDK = void 0;
var BaseSDK = /** @class */ (function () {
    function BaseSDK() {
    }
    BaseSDK.prototype.AddAPPEvent = function () { };
    BaseSDK.prototype.HideLoading = function () { };
    BaseSDK.prototype.CloseWebView = function () { };
    BaseSDK.prototype.ShowAppShop = function (type) { };
    BaseSDK.prototype.GetAppVersion = function (callback, errCallback) {
        callback && callback("0.0.0");
    };
    BaseSDK.prototype.GetConfig = function (callback) {
        callback && callback(null);
    };
    return BaseSDK;
}());
exports.BaseSDK = BaseSDK;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9tZXNoVG9vbHMvQmFzZVNESy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFQTtJQUFBO0lBc0JBLENBQUM7SUFwQlUsNkJBQVcsR0FBbEIsY0FBNEIsQ0FBQztJQUV0Qiw2QkFBVyxHQUFsQixjQUE0QixDQUFDO0lBRXRCLDhCQUFZLEdBQW5CLGNBQTZCLENBQUM7SUFFdkIsNkJBQVcsR0FBbEIsVUFBbUIsSUFBYSxJQUFTLENBQUM7SUFFbkMsK0JBQWEsR0FBcEIsVUFBcUIsUUFBeUIsRUFBRSxXQUFxQjtRQUVqRSxRQUFRLElBQUksUUFBUSxDQUFDLE9BQU8sQ0FBQyxDQUFDO0lBQ2xDLENBQUM7SUFFTSwyQkFBUyxHQUFoQixVQUFpQixRQUFzQjtRQUVuQyxRQUFRLElBQUksUUFBUSxDQUFDLElBQUksQ0FBQyxDQUFDO0lBQy9CLENBQUM7SUFJTCxjQUFDO0FBQUQsQ0F0QkEsQUFzQkMsSUFBQTtBQXRCWSwwQkFBTyIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIlxyXG5cclxuZXhwb3J0IGNsYXNzIEJhc2VTREtcclxue1xyXG4gICAgcHVibGljIEFkZEFQUEV2ZW50KCk6IHZvaWQge31cclxuXHJcbiAgICBwdWJsaWMgSGlkZUxvYWRpbmcoKTogdm9pZCB7fVxyXG5cclxuICAgIHB1YmxpYyBDbG9zZVdlYlZpZXcoKTogdm9pZCB7fVxyXG5cclxuICAgIHB1YmxpYyBTaG93QXBwU2hvcCh0eXBlPzogbnVtYmVyKTogdm9pZCB7fVxyXG5cclxuICAgIHB1YmxpYyBHZXRBcHBWZXJzaW9uKGNhbGxiYWNrOiBBY3Rpb24xPHN0cmluZz4sIGVyckNhbGxiYWNrPzogQWN0aW9uMCk6IHZvaWQgXHJcbiAgICB7XHJcbiAgICAgICAgY2FsbGJhY2sgJiYgY2FsbGJhY2soXCIwLjAuMFwiKTtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgR2V0Q29uZmlnKGNhbGxiYWNrOiBBY3Rpb24xPGFueT4pOiB2b2lkIFxyXG4gICAge1xyXG4gICAgICAgIGNhbGxiYWNrICYmIGNhbGxiYWNrKG51bGwpO1xyXG4gICAgfVxyXG5cclxuICAgXHJcblxyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIEFjdGlvbjBcclxue1xyXG4gICAgKCk6IHZvaWQ7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQWN0aW9uMTxUPlxyXG57XHJcbiAgICAocDogVCk6IHZvaWQ7XHJcbn0iXX0=