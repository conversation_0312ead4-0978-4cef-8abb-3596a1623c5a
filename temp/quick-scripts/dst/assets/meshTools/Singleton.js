
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/meshTools/Singleton.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7ea6eVJMwFIT6o9GqVNzOjt', 'Singleton');
// meshTools/Singleton.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Singleton = void 0;
var Singleton = /** @class */ (function () {
    function Singleton() {
    }
    Singleton.GetInstance = function () {
        if (this._inst == null) {
            this._inst = new this();
        }
        return this._inst;
    };
    return Singleton;
}());
exports.Singleton = Singleton;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9tZXNoVG9vbHMvU2luZ2xldG9uLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNBO0lBQUE7SUFXQSxDQUFDO0lBVGlCLHFCQUFXLEdBQXpCO1FBRUksSUFBVSxJQUFLLENBQUMsS0FBSyxJQUFJLElBQUksRUFDN0I7WUFDVSxJQUFLLENBQUMsS0FBSyxHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7U0FDbEM7UUFFRCxPQUFhLElBQUssQ0FBQyxLQUFLLENBQUM7SUFDN0IsQ0FBQztJQUNMLGdCQUFDO0FBQUQsQ0FYQSxBQVdDLElBQUE7QUFYWSw4QkFBUyIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IGNsYXNzIFNpbmdsZXRvblxue1xuICAgIHB1YmxpYyBzdGF0aWMgR2V0SW5zdGFuY2U8VD4odGhpczogbmV3KCkgPT4gVCk6IFRcbiAgICB7XG4gICAgICAgIGlmICgoPGFueT50aGlzKS5faW5zdCA9PSBudWxsKVxuICAgICAgICB7XG4gICAgICAgICAgICAoPGFueT50aGlzKS5faW5zdCA9IG5ldyB0aGlzKCk7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gKDxhbnk+dGhpcykuX2luc3Q7XG4gICAgfVxufVxuIl19