
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/meshTools/MeshTools.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1ac88QF4p1CyKQm2YcGvV0R', 'MeshTools');
// meshTools/MeshTools.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MeshTools = void 0;
var Publish_1 = require("./tools/Publish");
var MeshTools = /** @class */ (function () {
    function MeshTools() {
    }
    Object.defineProperty(MeshTools, "Publish", {
        /**
         * 游戏发布配置
         * @type {Publish}
         */
        get: function () {
            return Publish_1.Publish.GetInstance();
        },
        enumerable: false,
        configurable: true
    });
    return MeshTools;
}());
exports.MeshTools = MeshTools;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9tZXNoVG9vbHMvTWVzaFRvb2xzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDJDQUEwQztBQUUxQztJQUFBO0lBVUEsQ0FBQztJQUpHLHNCQUFrQixvQkFBTztRQUp6Qjs7O1dBR0c7YUFDSDtZQUVJLE9BQU8saUJBQU8sQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUNqQyxDQUFDOzs7T0FBQTtJQUNMLGdCQUFDO0FBQUQsQ0FWQSxBQVVDLElBQUE7QUFWWSw4QkFBUyIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFB1Ymxpc2ggfSBmcm9tIFwiLi90b29scy9QdWJsaXNoXCI7XHJcblxyXG5leHBvcnQgY2xhc3MgTWVzaFRvb2xzXHJcbntcclxuICAgIC8qKlxyXG4gICAgICog5ri45oiP5Y+R5biD6YWN572uXHJcbiAgICAgKiBAdHlwZSB7UHVibGlzaH1cclxuICAgICAqL1xyXG4gICAgcHVibGljIHN0YXRpYyBnZXQgUHVibGlzaCAoKTogUHVibGlzaFxyXG4gICAge1xyXG4gICAgICAgIHJldHVybiBQdWJsaXNoLkdldEluc3RhbmNlKCk7XHJcbiAgICB9XHJcbn0iXX0=