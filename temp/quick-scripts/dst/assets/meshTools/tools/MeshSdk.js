
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/meshTools/tools/MeshSdk.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c64ce5TWABPRLlk1MMhNw/U', 'MeshSdk');
// meshTools/tools/MeshSdk.js

"use strict";

(function (root, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) : typeof define === 'function' && define.amd ? define(['exports'], factory) : (global = typeof globalThis !== 'undefined' ? globalThis : window.global = window || self, factory(global.MeshSDK = {}));
})(void 0, function (exports) {
  'use strict';

  var Protocal = function () {
    function Protocal(methodName, id, params, onComplete, onReceive, timeout) {
      var _this2 = this;

      var _this = this;

      var key = methodName + "_" + id;
      this._receiveName = key + "_receive";
      this._completeName = key + "_complete";
      params = params || {};
      params['receive'] = this._receiveName;
      params['jsCallback'] = this._completeName;

      if (methodName !== 'sendGameAction' && params.type !== 5) {
        console.log('[MeshH5]', 'params', params);
      }

      window[this._receiveName] = function (result) {
        console.log('[MeshH5]', 'native:', "methodName " + key + " received");
        window[_this._receiveName] = null;

        _this.done();

        if (onReceive) {
          onReceive(null, result);
        }
      };

      var flag = false;

      window[this._completeName] = function (result) {
        console.log('[MeshH5]', 'native:', "methodName " + key + " completed", result);
        flag = true;

        _this.dispose();

        if (onComplete) {
          onComplete(null, result);
        }
      };

      if (Utils.isIframe()) {
        if (methodName == 'sendGameAction') {
          if (params.type == 1) {
            flag = true;

            _this.dispose();

            return;
          }
        }

        window.addEventListener('message', function (e) {
          if (e.data && window[_this2._completeName]) {
            var data = e.data;
            var jsMethods = data.jsMethods;
            if (!jsMethods) return;
            if (jsMethods !== _this2._completeName) return;
            var jsData = data.jsData;
            if (!jsData) return;
            var result = MeshSDK.prototype.parseResult(undefined, jsData);
            if (!result) return;
            console.log('[MeshH5]', 'iFrame:', "methodName " + jsMethods + " completed", result);
            flag = true;

            _this.dispose();

            if (onComplete) {
              onComplete(null, result);
            }
          }
        }, false);
      }

      if (timeout > 0) {
        this._completeTimeoutId = setTimeout(function () {
          clearTimeout(_this._completeTimeoutId);

          if (onComplete && !flag) {
            _this.dispose();

            onComplete("methodName " + key + " not responding", null);
          }
        }, timeout);
      }
    }

    Protocal.prototype.onDone = function (callback) {
      if (this._receiveName && this._completeName) this._onDone = callback;
    };

    Protocal.prototype.done = function () {
      if (this._onDone) {
        this._onDone();

        this._onDone = undefined;
      }
    };

    Protocal.prototype.dispose = function () {
      this.done();
      window[this._receiveName] = null;
      delete window[this._receiveName];
      this._receiveName = null;
      window[this._completeName] = null;
      delete window[this._completeName];
      this._completeName = null;
      clearTimeout(this._completeTimeoutId);
    };

    Protocal.TIME_OUT_IN_MS = 1500;
    return Protocal;
  }();

  var Native = function () {
    function Native() {}

    Native.invoke = function (methodName, protocal, params, onComplete, onReceive, timeout) {
      var _this = this;

      if (timeout === void 0) {
        timeout = 5000;
      }

      var proto = this.createProtocal(methodName, params, onComplete, onReceive, timeout);

      if (proto) {
        var isIframe = Utils.isIframe();

        if (Utils.isAndroid()) {
          if (isIframe) {
            console.log('android iframe'); //向iframe发送消息

            window.parent.postMessage(JSON.stringify(params), "*");
          } else {
            if (protocal) {
              if (Utils.isUnity()) {
                protocal.call(window.Unity, JSON.stringify(params));
              } else {
                protocal.call(window.NativeBridge, JSON.stringify(params));
              }
            }
          }
        } else if (Utils.isIOS()) {
          if (isIframe) {
            console.log('ios iframe'); //向iframe发送消息

            window.parent.postMessage(JSON.stringify(params), "*");
          } else {
            if (protocal) {
              protocal.postMessage(JSON.stringify(params));
            }
          }
        } else {
          if (isIframe) {
            console.log('pc iframe'); //向iframe发送消息

            window.parent.postMessage(JSON.stringify(params), "*");
          } else {
            console.error('[MeshH5]', 'native:', 'invalid Android&Ios');
          }
        }
      } else {
        console.error('[MeshH5]', 'native:', 'invalid protocal initliazation');
      }
    };

    Native.createProtocal = function (methodName, params, onComplete, onReceive, timeout) {
      var total = this.PROTOCAL_CACHE[methodName] || 0;
      total += 1;
      this.PROTOCAL_CACHE[methodName] = total;
      return new Protocal(methodName, total, params, onComplete, onReceive, timeout);
    };

    Native._allIFrames = [];
    Native._iframeId = -1;
    Native.PROTOCAL_CACHE = {};
    return Native;
  }();

  var isAndroid = function isAndroid() {
    var u = navigator.userAgent;
    return u.indexOf('Android') > -1 || u.indexOf('Adr') > -1;
  };

  var isIOS = function isIOS() {
    var u = navigator.userAgent;
    return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
  };

  var isIframe = function isIframe() {
    return window.top !== window;
  };

  var isUnity = function isUnity() {
    return window['isUnity'];
  };

  var nativeCall = Native.invoke.bind(Native);
  var Utils = Object.freeze({
    nativeCall: nativeCall,
    isAndroid: isAndroid,
    isIOS: isIOS,
    isIframe: isIframe,
    isUnity: isUnity
  });

  var MeshSDK = function () {
    function MeshSDK() {
      /*注册H5接受消息*/
      this._wrappedConfigParams = {
        //用户token发生变化
        'serverCodeUpdate': function serverCodeUpdate(handler, result) {
          var cb = handler,
              data = result;

          if (cb) {
            cb(data);
          }
        },
        //用户货币发生变化
        'walletUpdate': function walletUpdate(handler, result) {
          var cb = handler,
              data = result;

          if (cb) {
            cb(data);
          }
        },

        /**
         * 游戏行为发生变化
         * 物理返回键:{
         *     type:1
         * }
         * 返回按钮显示状态:{
         *     type:2,
         *     showStatus:0 隐藏 1 显示
         * }
         * 观众人数变化:{
         *     type:3,
         *     audienceCount:1//最新观众人数
         * }
         * APP操作游戏座位
         *      1.收到sendGameAction 上/下游戏座位 type:15
         *      2.通过APP麦位上座游戏座位(按需调用)
         *      3.收到sendGameAction 同步座位信息 type:16 时，发现座位信息同麦位信息不一致时，
         *        以APP麦位信息为准，进而保证两者的一致性。(按需调用)
         * :{
         *      type:4,
         *      params:{
         *          optType:0,//0上座,1下座
         *          userId:xxxA,
         *          seat:0 // 座位位置
         *      }
         * }
         * 变更用户身份
         * {
         *     type:5,
         *     params:{
         *         role:0,//0.正常用户 1.游客  2.主持人(拥有开始游戏权限)
         *         userId:xxxA
         *     }
         * }
         * APP返回踢人操作结果
         * {
         *     type:6,
         *     params:{
         *         optResult:0,//0 踢人成功 1 踢人失败
         *         optUserId:0,//执行踢人操作用户id
         *         userId:xxxA,//被踢用户id
         *         reason:"success" //optResult 为1时，需要注明原因
         *     }
         * }
         * @param handler
         * @param result
         * 活动入口ICON状态 (psd):
         * {
         *     type:2001,
         *     showStatus:0 隐藏 1 显示
         *     activityIcon:'' 活动icon 外网访问地址 (120*120 px)
         * }
         *
         * 打开帮助
         * {
         *     type:2002
         * }
         *
         * 控制游戏背景音乐 0-1
         * {
         *     type:2003,
         *     bgVolume:0.5
         * }
         *
         * 控制游戏音效 0-1
         * {
         *     type:2004,
         *     soundVolume:0.5
         * }
         *
         * 麦位图标底部到屏幕顶部距离/屏幕高度
         * 数值范围 0-1
         * {
         *     type:2005,
         *     value:0.1
         * }
         *
         * 帮助按钮显示状态:{
         *     type:2006,
         *     showStatus:0 隐藏 1 显示
         * }
         *
         * musee 最小化按钮显示状态:{
         *     type:2007,
         *     showStatus:0 隐藏 1 显示
         * }
         * 客户端通知游戏最小化状态
         * {
         *     type:2008,
         *     minimizeStatus:0 取消最小化 1 启用最小化
         * }
         * tietie 控制游戏内的按钮点击逻辑
         * {
         *   type:2009,
         *   params:{
         *       "subAction":2009,
         *       "subMsg":{}
         *   }
         * }
         * 切换货币
         * {
         *     type:2010,
         *     params:{
         *          appId:xxx,
         *          coinType:1
         *      }
         * }
         * 隐藏设置按钮和商城按钮
         * {
         *     type:2011,
         *     params:{
         *         "settingButton": false,  //设置按钮是否隐藏（true显示，false隐藏，默认显示）
         *         "mallButton": false, //商城按钮是否隐藏（true显示，false隐藏，默认显示）
         *         "subMsg": {} //保留字段可不传
         *     }
         * }
         * 查询当前音乐和音效的状态
         * {
         *     type:2012,
         *     params:{}
         * }
         * 直接触发游戏内的事件（拉起商城、拉起规则页）
         * {
         *     type:2013,
         *     params:{
         *         //1001 拉起规则页面  //1002拉起商城页面 //1003设置音效和音乐相关的
         *         "subAction": 1001,
         *         "subMsg": {
         *                     //在subAction为 1003 的时候传bgmStatus、seStatus字段
         *                     "bgmStatus": 1, //背景音乐 bgmStatus 0 关闭 1 打开
         *                     "seStatus": 1 //音效 seStatus 0 关闭 1 打开
         *                   }
         *     }
         * }
         */
        'gameActionUpdate': function gameActionUpdate(handler, result) {
          var cb = handler,
              data = result;

          if (cb) {
            cb(data);
          }
        },

        /**
         * 声音发生变化
         * 背景音乐 bgmStatus 0 关闭 1 打开
         * 音效 seStatus 0 关闭 1 打开
         * {bgmStatus:1,seStatus:1}
         * @param handler
         * @param result
         */
        'soundUpdate': function soundUpdate(handler, result) {
          var cb = handler,
              data = result;

          if (cb) {
            cb(data);
          }
        },

        /**
         * musee 恢复游戏面板
         * 游戏 恢复音量
         */
        'openFoldGame': function openFoldGame(handler, result) {
          var cb = handler,
              data = result;

          if (cb) {
            cb(data);
          }
        }
      };
    }
    /**
     * 创建协议
     * @param fnName 方法名
     */


    MeshSDK.prototype.createProtocol = function (fnName) {
      var isIframe = Utils.isIframe();

      if (Utils.isAndroid()) {
        if (isIframe) {
          console.log('android iframe');
        } else if (Utils.isUnity()) {
          if (window.Unity && window.Unity['call']) {
            return window.Unity['call'];
          } else {
            return null;
          }
        } else {
          if (window.NativeBridge && window.NativeBridge[fnName]) {
            return window.NativeBridge[fnName];
          } else {
            return null;
          }
        }
      } else if (Utils.isIOS()) {
        if (isIframe) {
          console.log('ios iframe');
        } else if (Utils.isUnity()) {
          if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers['unityControl']) {
            return window.webkit.messageHandlers['unityControl'];
          } else {
            return null;
          }
        } else {
          if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers[fnName]) {
            return window.webkit.messageHandlers[fnName];
          } else {
            return null;
          }
        }
      } else {
        if (isIframe) {
          console.log('pc iframe');
        } else {
          console.error('[Mesh-H5-sdk]', "createProtocol 既不是Andoroid也不是Ios");
        }
      }
    };
    /**
     * 通用的处理结果的方法
     * @param err 错误信息
     * @param result 成功的结果，一般是字符串，需要JSON.parse处理
     * @returns {*}
     */


    MeshSDK.prototype.parseResult = function (err, result) {
      if (err) throw err;else if (result == undefined || result == null) throw 'empty result';else {
        var parsed = void 0;

        if (typeof result == 'string') {
          parsed = JSON.parse(result);
        } else if (typeof result == 'object') {
          parsed = result;
        }

        if (!parsed) throw 'failed to parse';
        return parsed;
      }
    };
    /**
     * 辅助方法，用来方便创建统一的协议回调处理逻辑
     * @param resolve 通过 promise 传入的 resolve 回调
     * @param reject 通过 promise 传入的 reject 回调
     */


    MeshSDK.prototype.usePromiseResult = function (resolve, reject) {
      var _this = this;

      return function (err, result) {
        try {
          var parsed = _this.parseResult(err, result);

          if (parsed) {
            resolve(parsed);
          }
        } catch (e) {
          reject(e);
        }
      };
    };
    /**
     * 发送协议的接口。
     * 一般情况下无需主动调用
     * @param params 构造出来的协议参数，包括协议名和参数等
     * @param onComplete 完成回调
     * @param onReceive 接收到回调
     */


    MeshSDK.prototype.send = function (params, onComplete, onReceive) {
      if (!params) return;
      Utils.nativeCall(params.methodName, params.protocol, params.params, onComplete, onReceive, params.timeout);
    };
    /**
     * 通用的请求协议的方法。可以参照协议文档进行调用。
     * @param methodName 方法函数名
     * @param params 协议参数，JSON Object
     * @param onComplete 协议完成回调方法
     * @param onReceived 协议收到回调方法（ACK）
     * @param timeout 超时，单位毫秒。默认 0 为不超时。超时设置的用处是清空回调等资源。
     */


    MeshSDK.prototype.request = function (methodName, params, onComplete, onReceived, timeout) {
      if (timeout === void 0) {
        timeout = 0;
      }

      this.send({
        methodName: methodName,
        protocol: this.createProtocol(methodName),
        params: params,
        timeout: timeout
      }, this.usePromiseResult(onComplete, function (reason) {
        return console.error('[Mesh-H5-sdk]', "methodName " + methodName + " error: " + reason);
      }), onReceived);
    };

    MeshSDK.prototype.regReceiveMessage = function (params) {
      var _this = this;

      var wrapped = {};

      for (var key in params) {
        if (!!_this._wrappedConfigParams[key]) {
          wrapped[key] = key;
        }
      }

      if (Utils.isIframe()) {
        window.addEventListener('message', function (e) {
          if (e.data) {
            var data = e.data;
            var jsMethods = data.jsMethods;
            if (!params[jsMethods]) return;
            var jsData = data.jsData;
            if (!jsData) return;
            var result = MeshSDK.prototype.parseResult(undefined, jsData);
            if (!result) return;

            if (typeof params[jsMethods] === 'function' || !!_this._wrappedConfigParams[jsMethods]) {
              _this._wrappedConfigParams[jsMethods](params[jsMethods], result);
            }
          }
        });
      } else {
        // 把包裹后的方法注册到 window 上
        Object.keys(wrapped).forEach(function (key) {
          var name = wrapped[key];

          if (name) {
            if (name == 'openFoldGame') {
              window[name] = function (str) {
                if (typeof params[key] === 'function' || !!_this._wrappedConfigParams[key]) {
                  _this._wrappedConfigParams[key](params[key], {});
                }
              };
            } else {
              window[name] = function (str) {
                var result = _this.parseResult(undefined, str);

                if (!result) return;
                if (!params[key]) return;

                if (typeof params[key] === 'function' || !!_this._wrappedConfigParams[key]) {
                  _this._wrappedConfigParams[key](params[key], result);
                }
              };
            }
          }
        });
      }

      return wrapped;
    };
    /**
     * 获取版本号(H5SDK 协议)
     * @returns {Promise<unknown>}
     */


    MeshSDK.prototype.getVersion = function () {
      return new Promise(function (resolve) {
        var data = "1.0.0";
        resolve(data);
      });
    };
    /**
     * 获取用户信息配置
     * @returns {Promise<unknown>}
     */


    MeshSDK.prototype.getConfig = function () {
      var _this = this;

      return new Promise(function (resolve, reject) {
        _this.request("getConfig", {}, function (data) {
          resolve(data);
        }, reject);
      });
    };
    /**
     * 销毁游戏
     * @returns {Promise<unknown>}
     */


    MeshSDK.prototype.destroy = function () {
      var _this = this;

      return new Promise(function (resolve, reject) {
        _this.request("destroy", {}, function (data) {
          resolve(data);
        }, reject);
      });
    };
    /**
     * 提示余额不足，拉起充值商城
     * type 0：余额不足 1：余额充足，主动调用
     * gameId：游戏Id
     * paymentType
     *   trovo: 1: mana; 2: game coin
     *   musee: 1:黄钻 2:蓝钻
     * inThisRound: 本轮是否参与 0:未参与  1:参与
     * @returns {Promise<unknown>}
     */


    MeshSDK.prototype.gameRecharge = function (params) {
      var _this = this;

      return new Promise(function (resolve, reject) {
        _this.request("gameRecharge", params ? params : {}, function (data) {
          resolve(data);
        }, reject);
      });
    };
    /**
     * 游戏加载完毕
     * @returns {Promise<unknown>}
     */


    MeshSDK.prototype.gameLoaded = function () {
      var _this = this;

      return new Promise(function (resolve, reject) {
        _this.request("gameLoaded", {}, function (data) {
          resolve(data);
        }, reject);
      });
    };
    /**
     * musee
     * 最小化按钮
     * 调用时游戏执行静音操作(不再执行，只是通知到最小化操作)
     * @returns {Promise<unknown>}
     */


    MeshSDK.prototype.foldGame = function () {
      var _this = this;

      return new Promise(function (resolve, reject) {
        _this.request("foldGame", {}, function (data) {
          resolve(data);
        }, reject);
      });
    };
    /**
     * 上报游戏行为
     * 游戏加载进度  type:1, params: {progress: 10}
     * 进入/打开游戏主页 type:2
     * 游戏房间号 type:3, params: {gameRoomId: 'xxxx'}
     * 网络延迟程度
     *   type:4,
     *   params:{
     *       status:1,延迟程度(1:良好 0~100ms,2:普通 100~200ms,3:较差 200~300ms,4:很差 300~600ms,5:极差 >600ms,6:断开网络 9999ms)
     *       latency:100,延迟时间，单位ms
     *   }
     * 帧率
     *   type:5,
     *   params:{
     *       status:1,帧率评级(1:超高 >60fps,2:高 50~60fps,3:中 30~50fps,4:较低 20~30fps,5:低 <20fps)
     *       fps:55,帧率值
     *   }
     * 拉起观众列表 type:6
     * 拉起玩家资料卡 type:7,params:{userId:xxx}
     * 获取用户是否已关注
     *    type:8,params:{userIds:[userId1xx,userId2xx]}
     *    返回数据：{
     *              userIds:{userId1xx:true,userId2xx:false}//true为已关注
     *            }
     * 关注用户
     *    type:9,params:{userId:xxx}
     *    返回数据：{
     *        status:0,//0 为关注成功,1 为关注失败
     *        userId:xxx,
     *    }
     * 拉起分享面板 type:10
     * 回到APP准备页(组队页) type:11
     * 点击右上角关闭按钮 type:12
     * 游戏开始 type:13
     * 游戏结束 type:14
     * 上/下游戏座位
     *    type:15
     *    params:{
     *        optType:0,//0上座,1下座
     *        userId:xxxA,
     *        seat:0 // 座位位置
     *    }
     * 同步座位信息
     *    type:16,
     *    params:{
     *      seats:[
     *          {
     *              userId:xxxA,
     *              seat:0, //座位位置
     *              prepareStatus:0 //0未准备,1已准备
     *          },
     *          {
     *              userId:xxxB,
     *              seat:1,
     *              prepareStatus:1,
     *          }
     *      ]
     *    }
     * 游戏发起踢人请求
     *    type:17,
     *    params:{
     *      userId:xxxA,
     *      seat:0 //座位位置
     *    }
     * 游戏上座失败
     *    type:18,
     *    params:{
     *        userId:xxxA,
     *    }
     * 是否可切换货币，游戏打开时状态为不可以切换
     *    type:19,
     *    params:{
     *        isCanChange: 0 不可切换 1 可切换
     *        currencyCoinType:1,//当前货币类型
     *    }
     * 语聊房游戏准备完成，通知 app 可以发起自动上座 type:20
     * 返回游戏内的音乐和音效状态（一般是收到gameActionUpdate中的 type ==2012 的时候发起回调）
     *    type: 21,
     *    params: {
     *         "bgmStatus": 1, //背景音乐 bgmStatus 0 关闭 1 打开
     *         "seStatus": 1   //音效 seStatus 0 关闭 1 打开
     *    }
     * 触发活动事件 psd专享()
     *    type:2001
     * 帮助页准备完毕
     *    type:2002
     *    params:{
     *        url:""
     *    }
     * 通知miya H5商城事件
     *  type:2003
     *  params:{
     *        env: 1 //1测试,2正式
     *        gameId:xxx
     *    }
     * 同步APP 麦克风和扬声器开关 游戏内通知 (比如拆弹猫、谁是卧底等）
     * {
     *     "type":3001,
     *     "params": {
     *         "users": [
     *                 {
     *                     "userId:": "xxxxx",   //用户 id
     *                     "microphone": false, //是否开启麦克风  false关闭  true开启
     *                     "useSpeaker": false //是否开启扬声器
     *                 },
     *                 {
     *                     "userId:": "xxxxx",
     *                     "microphone": true,
     *                     "useSpeaker": true
     *                 }
     *             ]
     *     }
     * }
     *
     * {
     *   "type": 3002,
     *   "params": {
     *     "state": 0 //0 代表点击的结算页面的关闭按钮   1 代表点击结算页面的再来一局按钮
     *   }
     * }
     * @returns {Promise<unknown>}
     */


    MeshSDK.prototype.sendGameAction = function (params) {
      var _this = this;

      return new Promise(function (resolve, reject) {
        _this.request("sendGameAction", params, function (data) {
          resolve(data);
        }, reject);
      });
    };
    /**
     * 是否支持isSupportRechargeV2
     * trovo 使用
     * @returns
     * isSupport boolean
     * true 去掉之前的Toast提示，调用gameRecharge paymentType =1
     * false 保留Toast
     */


    MeshSDK.prototype.isSupportRechargeV2 = function () {
      var _this = this;

      return new Promise(function (resolve) {
        var isSupport = false;

        if (Utils.isAndroid()) {
          if (window.NativeBridge['versionSupportApis']) {
            _this.request("versionSupportApis", {}, function (data) {
              if (data.apis && data.apis.includes('gameRechargeV2_trovo')) {
                isSupport = true;
              }

              resolve({
                'isSupport': isSupport
              });
            });
          } else {
            resolve({
              'isSupport': isSupport
            });
          }
        } else if (Utils.isIOS()) {
          if (window.webkit.messageHandlers['versionSupportApis']) {
            _this.request("versionSupportApis", {}, function (data) {
              if (data.apis && data.apis.includes('gameRechargeV2_trovo')) {
                isSupport = true;
              }

              resolve({
                'isSupport': isSupport
              });
            });
          } else {
            resolve({
              'isSupport': isSupport
            });
          }
        } else {
          isSupport = true;
          resolve({
            'isSupport': isSupport
          });
        }
      });
    };

    return MeshSDK;
  }();

  var meshSDK = new MeshSDK();
  exports.meshSDK = meshSDK;
  Object.defineProperty(exports, '__esModule', {
    value: true
  });
});

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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