
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/meshTools/tools/Publish.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1c80fkcVgdAbKBUUkWof48s', 'Publish');
// meshTools/tools/Publish.ts

"use strict";
/**
 * mesh游戏发布配置管理类
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Publish = void 0;
var Singleton_1 = require("../Singleton");
var Publish = /** @class */ (function (_super) {
    __extends(Publish, _super);
    function Publish() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 0 开发 1 测试 2 正式
        _this.miniGameEnv = 1;
        //版本号
        _this.miniGameVersion = "1.0.0";
        //游戏id
        _this.gameId = 2059;
        _this.gsp = 8001; //游戏正式服务器节点 101:新加坡（阿里云）  201:迪拜（AWS）301:硅谷 (阿里云)  401:法兰克福(阿里云） 8001:soofun（阿里云）
        //区分上面的参数是不是 url 传进来的，正常是用 js 传进来，个别渠道是通过 url 传参的
        _this.isDataByURL = false;
        return _this;
    }
    Publish.prototype.getVersion = function () {
        return this.miniGameVersion;
    };
    Publish.prototype.getEnv = function () {
        return this.miniGameEnv;
    };
    Publish.prototype.getGameId = function () {
        return this.gameId;
    };
    return Publish;
}(Singleton_1.Singleton));
exports.Publish = Publish;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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