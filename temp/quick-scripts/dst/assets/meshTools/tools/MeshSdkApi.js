
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/meshTools/tools/MeshSdkApi.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f530dRaLT5IBJrG6Sh+L1QP', 'MeshSdkApi');
// meshTools/tools/MeshSdkApi.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var MeshTools_1 = require("../MeshTools");
var BaseSDK_1 = require("../BaseSDK");
var MessageBaseBean_1 = require("../../scripts/net/MessageBaseBean");
var GameMgr_1 = require("../../scripts/common/GameMgr");
var EventCenter_1 = require("../../scripts/common/EventCenter");
var MeshSdk = require("MeshSdk");
// var ZegoGameClient = ZG.ZegoGameClient;
var MeshSdkApi = /** @class */ (function (_super) {
    __extends(MeshSdkApi, _super);
    function MeshSdkApi() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._meshSdk = MeshSdk.meshSDK; // new ZegoGameClient();
        _this._isChangeVoluming = false;
        _this.IsNotifyGameLoaded = false;
        _this.IsNotifyGameLoading = false;
        return _this;
    }
    // 监听 APP 事件  APP -> H5
    MeshSdkApi.prototype.AddAPPEvent = function () {
        // regReceiveMessage  注册 APP 回调到 H5 的函数
        this._meshSdk.regReceiveMessage({
            // 余额变更
            walletUpdate: function () {
                console.log("walletUpdate");
                var autoMessageBean = {
                    'msgId': MessageBaseBean_1.AutoMessageId.WalletUpdateMsg,
                    'data': {}
                };
                GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            },
            // code 变更回调
            serverCodeUpdate: function (data) {
                data.code = encodeURIComponent(data.code);
                var autoMessageBean = {
                    'msgId': MessageBaseBean_1.AutoMessageId.ServerCodeUpdateMsg,
                    'data': { code: data.code }
                };
                GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            }
        });
    };
    // H5 调用APP 获取APP 版本号
    MeshSdkApi.prototype.GetAppVersion = function (callback, errCallback) {
        callback && callback("1.0.0");
    };
    // H5 调用 APP  获取用户信息数据
    MeshSdkApi.prototype.GetConfig = function (callback) {
        if (MeshTools_1.MeshTools.Publish.isDataByURL) {
            callback({
                "gameConfig": {
                    "currencyIcon": "",
                    "sceneMode": 0
                },
                "code": MeshTools_1.MeshTools.Publish.code,
                "appId": MeshTools_1.MeshTools.Publish.appId,
                "language": MeshTools_1.MeshTools.Publish.language,
                "gameMode": MeshTools_1.MeshTools.Publish.gameMode,
                "userId": MeshTools_1.MeshTools.Publish.userId,
                "roomId": MeshTools_1.MeshTools.Publish.roomId,
                "appChannel": MeshTools_1.MeshTools.Publish.appChannel,
                "gsp": MeshTools_1.MeshTools.Publish.gsp
            });
            return;
        }
        if (CC_DEBUG) {
            callback({
                "gameConfig": {
                    "currencyIcon": "https://game-center-test.jieyou.shop/static/images/index/game_bean.png",
                    "sceneMode": 0
                },
                "code": "qFwaAVKyEYTmPey1vWA4Huq7bvto4xexT0UJRnh03vlwTghRwFyVsbO4JRLV",
                "appId": 66666666,
                "language": "0",
                "gameMode": 3,
                "userId": 1111,
                "roomId": "room01",
                "appChannel": "debug",
                "gsp": 8001
            });
            return;
        }
        this._meshSdk.getConfig().then(callback).catch(function (err) {
            cc.error(err);
        });
    };
    // 通知APP 游戏资源加载完了
    MeshSdkApi.prototype.HideLoading = function () {
        if (this.IsNotifyGameLoaded == true) {
            return;
        }
        this.IsNotifyGameLoaded = true;
        this._meshSdk.gameLoaded();
    };
    // 销毁 WebView
    MeshSdkApi.prototype.CloseWebView = function () {
        this._meshSdk.destroy();
    };
    // 余额不足  H5 调用 APP
    MeshSdkApi.prototype.ShowAppShop = function (type) {
        cc.log("余额不足回调");
        this._meshSdk.gameRecharge();
    };
    return MeshSdkApi;
}(BaseSDK_1.BaseSDK));
exports.default = MeshSdkApi;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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