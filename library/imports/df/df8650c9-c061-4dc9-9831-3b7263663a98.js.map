{"version": 3, "sources": ["assets/resources/i18n/zh_HK.ts"], "names": [], "mappings": ";;;;;;;AAAa,QAAA,QAAQ,GAAG;IACxB,SAAS;IACL,QAAQ,EAAE,QAAQ;IAClB,SAAS,EAAE,OAAO;IAClB,mBAAmB,EAAE,UAAU;IAC/B,iBAAiB,EAAE,QAAQ;IAC3B,YAAY,EAAE,MAAM;IACpB,UAAU,EAAE,MAAM;IAClB,eAAe,EAAE,OAAO;IACxB,iBAAiB,EAAE,UAAU;IAC7B,gBAAgB,EAAE,OAAO;IACzB,uBAAuB,EAAE,QAAQ;IACjC,eAAe,EAAE,SAAS;IAC1B,WAAW,EAAE,aAAa;IAE1B,gBAAgB,EAAE,QAAQ;IAC1B,sBAAsB,EAAE,QAAQ;IAChC,QAAQ,EAAE,eAAe;IAEzB,MAAM,EAAE,MAAM;IACd,QAAQ,EAAE,MAAM;IAChB,SAAS,EAAE,IAAI;IACf,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,MAAM;IAClB,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,IAAI,EAAE,IAAI;IACV,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,IAAI,EAAC,IAAI;IACT,MAAM,EAAC,IAAI;IACX,IAAI,EAAC,IAAI;IACT,IAAI,EAAC,IAAI;IACT,WAAW,EAAC,IAAI;IAChB,IAAI,EAAC,IAAI;IACT,WAAW,EAAC,MAAM;IAClB,cAAc,EAAC,OAAO;IACtB,UAAU,EAAC,OAAO;IAClB,iBAAiB,EAAC,OAAO;IACzB,IAAI,EAAC,IAAI;IACT,OAAO,EAAC,IAAI;IACZ,MAAM,EAAC,IAAI;IACX,OAAO,EAAC,IAAI;IACZ,KAAK,EAAC,IAAI;IAEV,SAAS,EAAC,KAAK;IACf,OAAO,EAAC,MAAM;IAGd,IAAI;IACJ,IAAI,EAAE,cAAI;IACV,KAAK,EAAE,0EAAmB;IAC1B,KAAK,EAAE,6NAAyC;IAChD,KAAK,EAAE,wYAAwE;IAC/E,KAAK,EAAE,yOAA2C;IAClD,KAAK,EAAE,4YAAuE;IAE9E,MAAM;IACN,iBAAiB,EAAE,wDAAW;IAC9B,kBAAkB,EAAE,iEAAe;IACnC,kBAAkB,EAAE,2FAAqB;IACzC,kBAAkB,EAAE,mIAA0B;IAC9C,kBAAkB,EAAE,wGAAwB;IAC5C,kBAAkB,EAAE,wGAAwB;IAC5C,kBAAkB,EAAE,iHAAuB;IAC3C,kBAAkB,EAAE,2IAA6B;IACjD,kBAAkB,EAAE,uHAAwB;IAC5C,kBAAkB,EAAE,iHAAuB;IAE3C,MAAM;IACN,cAAc,EAAE,0BAAM;IACtB,eAAe,EAAE,oVAAiE;IAElF,MAAM;IACN,WAAW,EAAE,0BAAM;IACnB,YAAY,EAAE,8QAAkD;IAChE,YAAY,EAAE,6EAAiB;IAC/B,YAAY,EAAE,wIAA0B;IACxC,YAAY,EAAE,qGAAqB;IACnC,YAAY,EAAE,+FAAoB;IAClC,YAAY,EAAE,+FAAoB;IAClC,YAAY,EAAE,yIAA2B;IAEzC,MAAM;IACN,MAAM,EAAE,cAAI;IACZ,OAAO,EAAE,2SAAsD;IAC/D,OAAO,EAAE,yaAA2E;IACpF,OAAO,EAAE,uSAAuD;IAEhE,MAAM;IACN,UAAU,EAAE,cAAI;IAChB,WAAW,EAAE,2SAAsD;IACnE,WAAW,EAAE,qPAA6C;IAC1D,WAAW,EAAE,kSAAuD;IAEpE,OAAO;IACP,eAAe,EAAE,0BAAM;IACvB,gBAAgB,EAAE,8EAAkB;IACpC,gBAAgB,EAAE,uGAAuB;IACzC,gBAAgB,EAAE,qJAAkC;IACpD,gBAAgB,EAAE,qJAAkC;IACpD,gBAAgB,EAAE,qJAAkC;IACpD,gBAAgB,EAAE,oLAAwC;IAC1D,gBAAgB,EAAE,kNAA6C;IAC/D,gBAAgB,EAAE,kNAA6C;IAC/D,gBAAgB,EAAE,2LAA0C;IAC5D,iBAAiB,EAAE,2LAA0C;IAC7D,iBAAiB,EAAE,2LAA0C;CAChE,CAAC;AAEF,IAAM,KAAK,GAAG,EAAS,CAAC;AACxB,IAAI,CAAC,KAAK,CAAC,QAAQ;IAAE,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AACzC,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,gBAAQ,CAAC", "file": "", "sourceRoot": "/", "sourcesContent": ["export const language = {\n//這部分是通用的\n    kickout1: '您被請出房間',\n    LeaveRoom: '房間已解散',\n    InsufficientBalance: '餘額不足，去儲值',\n    GameRouteNotFound: '遊戲路線異常',\n    NetworkError: '網絡異常',\n    RoomIsFull: '房間已滿',\n    EnterRoomNumber: '輸入房間號',\n    GetUserInfoFailed: '獲取用戶資訊失敗',\n    RoomDoesNotExist: '房間不存在',\n    FailedToDeductGoldCoins: '扣除金幣失敗',\n    ExitApplication: '確定退出遊戲？',\n    QuitTheGame: '退出後將無法返回遊戲。',\n\n    NotEnoughPlayers: '玩家數量不足',\n    TheGameIsFullOfPlayers: '玩家數量已滿',\n    kickout2: '是否將 {0} 請出房間?',\n\n    upSeat: '加入遊戲',\n    downSeat: '退出遊戲',\n    startGame: '開始',\n    readyGame: '準備',\n    cancelGame: '取消準備',\n    cancel: '取消',\n    confirm: '確定',\n    kickout3: '踢出',\n    back: '返回',\n    leave: '退出',\n    music: '音樂',\n    sound: '音效',\n    join:'進入', //加入\n    create:'創建', //创建\n    auto:'匹配',\n    Room:'房間',\n    room_number:'房號', //房间号\n    copy:'複製', //复制\n    game_amount:'遊戲費用', //游戏费用\n    player_numbers:'玩家數量:', //玩家数量\n    room_exist:'房間不存在',//房间不存在\n    enter_room_number:'輸入房間號',//输入房间号\n    free:'免費',\n    players:'玩家', //玩家\n    Player:'玩家',\n    Tickets:'門票',\n    Empty:'空位',\n\n    nextlevel:'下一關', //下一關  \n    relevel:'再玩一次', //再來一局 \n   \n\n    //幫助\n    Tips: `幫助`,\n    Tips1: `1. 每局遊戲比賽時間：120秒。`,\n    Tips2: `2. 排名規則：遊戲時間結束後，根據玩家的得分高低進行排名，得分相同排名相同。`,\n    Tips3: `3. 消除規則：通過移動遊戲中的食物與臨近的食物交換位置使相同的食物處於相鄰狀態，若滿足相鄰的同樣食物>=3個位置交換成功，則完成消除得分。`,\n    Tips4: `4. 道具消除：完成特殊的消除會生成消除道具，道具和附近塊交換位置後會使用並消耗。`,\n    Tips5: `5. 連續消除：玩家一次消除行為引發新食物產生，食物下落後觸發的消除為連續消除，連續消除期間不可操作。一次下落期間發生的連續消除記為1輪。`,\n\n    //產生方式\n    Generation_Method: `產生方式和使用效果`,\n    Generation_Method1: `1. 消除整列或整行食物。`,\n    Generation_Method2: `2. 消除其中心向外延伸的12個食物。`,\n    Generation_Method3: `3. 消除螢幕中所有與該道具交換位置相同的食物。`,\n    Generation_Method4: `4. 消除1行+1列的食物，包括鎖鏈和冰塊。`,\n    Generation_Method5: `5. 消除3行+3列的食物，包括鎖鏈和冰塊。`,\n    Generation_Method6: `6. 將螢幕中隨機一種食物變為火箭並釋放。`,\n    Generation_Method7: `7. 消除其中心向外延伸的24個食物，包括鎖鏈和冰塊。`,\n    Generation_Method8: `8. 將螢幕中隨機一種食物塊變為炸彈並釋放。`,\n    Generation_Method9: `9. 消除螢幕中所有食物，包括鎖鏈和冰塊。`,\n\n    //常駐任務\n    Permanent_Task: `常駐任務`,\n    Permanent_Task1: `玩家每消除10隻螃蟹，便會獲得兩次隨機消除一塊2x2區域的獎勵，若本次消除螃蟹滿足兩次及以上，則第二次開始只給一組2x2消除。`,\n\n    //隨機任務\n    Random_Task: `隨機任務`,\n    Random_Task1: `根據人數增加所需消除的數量，比例為加一人則需多消除一個，例如2人局需消除7次，3人局需消除8次。`,\n    Random_Task2: `- 每次最多同時存在1個任務。`,\n    Random_Task3: `- 任務順序每局開局隨機生成一種，依次交替進行。`,\n    Random_Task4: `- 消除7個指定顏色可獲得一個隨機彩。`,\n    Random_Task5: `- 消除7個指定顏色可獲得一個冰塊。`,\n    Random_Task6: `- 消除7個指定顏色可獲得一個鎖鏈。`,\n    Random_Task7: `- 消除7個指定顏色可獲得一個隨機形態的火箭道具。`,\n\n    //鎖鏈簡介\n    Chains: `鎖鏈`,\n    Chains1: `1. 消除任務：遊戲比賽開始後，每位玩家會收到系統派發的任務。完成任務會向其他玩家釋放妨礙道具——鎖鏈。`,\n    Chains2: `2. 鎖鏈的妨礙效果：隨機鎖住一個位置內的食物。受限制期間，鎖鏈鎖住的位置不能移動，鎖鏈內的食物可能會因為其他地塊的消除而改變，並非鎖住固定食物。`,\n    Chains3: `3. 鎖鏈的解除：在鎖鏈鄰近位置（上下左右1格的塊）進行普通消除後或者在道具有效區域內，消除1次即可解除。`,\n\n    //冰塊簡介\n    Ice_Blocks: `冰塊`,\n    Ice_Blocks1: `1. 消除任務：遊戲比賽開始後，每位玩家會收到系統派發的任務。完成任務會向其他玩家釋放妨礙道具——冰塊。`,\n    Ice_Blocks2: `2. 冰塊的妨礙效果：隨機凍住一個食物，受凍期間，位置可以被動移動，玩家不能主動移動。`,\n    Ice_Blocks3: `3. 冰塊的解除：在冰塊鄰近位置（上下左右1格的塊）進行普通消除後或者在道具有效區域內,消除1次即可解除。`,\n\n    // 得分細則\n    Scoring_Details: `得分細則`,\n    Scoring_Details1: `普通消除：消除單塊得分=20分;`,\n    Scoring_Details2: `連續消除：消除單塊得分=連消次數x20分;`,\n    Scoring_Details3: `火箭消除：火箭啟動得分=300分;由火箭消除的單塊得分=30分;`,\n    Scoring_Details4: `炸彈消除：炸彈啟動得分=500分;由炸彈消除的單塊得分=50分;`,\n    Scoring_Details5: `彩虹消除：彩虹啟動得分=600分;由彩虹消除的單塊得分=60分;`,\n    Scoring_Details6: `火箭組合消除：雙火箭啟動得分=1200分;由超級火箭消除的單塊得分=60分;`,\n    Scoring_Details7: `炸彈火箭組合消除：炸彈火箭組合啟動得分=1600分;由炸彈火箭消除的單塊得分=80分;`,\n    Scoring_Details8: `彩虹火箭組合消除：彩虹火箭組合啟動得分=1800分;由彩虹火箭消除的單塊得分=90分;`,\n    Scoring_Details9: `炸彈組合消除：超級炸彈啟動得分=2000分;由超級炸彈消除的單塊得分=100分;`,\n    Scoring_Details10: `彩虹炸彈消除：彩虹炸彈啟動得分=2200分;由彩虹炸彈消除的單塊得分=110分;`,\n    Scoring_Details11: `彩虹組合消除：超級彩虹啟動得分=2400分;由超級彩虹消除的單塊得分=120分;`,\n};\n\nconst cocos = cc as any;\nif (!cocos.Jou_i18n) cocos.Jou_i18n = {};\ncocos.Jou_i18n.zh_HK = language;"]}