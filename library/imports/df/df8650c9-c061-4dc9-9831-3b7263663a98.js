"use strict";
cc._RF.push(module, 'df865DJwGFNyZgxO3JjZjqY', 'zh_HK');
// resources/i18n/zh_HK.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //這部分是通用的
    kickout1: '您被請出房間',
    LeaveRoom: '房間已解散',
    InsufficientBalance: '餘額不足，去儲值',
    GameRouteNotFound: '遊戲路線異常',
    NetworkError: '網絡異常',
    RoomIsFull: '房間已滿',
    EnterRoomNumber: '輸入房間號',
    GetUserInfoFailed: '獲取用戶資訊失敗',
    RoomDoesNotExist: '房間不存在',
    FailedToDeductGoldCoins: '扣除金幣失敗',
    ExitApplication: '確定退出遊戲？',
    QuitTheGame: '退出後將無法返回遊戲。',
    NotEnoughPlayers: '玩家數量不足',
    TheGameIsFullOfPlayers: '玩家數量已滿',
    kickout2: '是否將 {0} 請出房間?',
    upSeat: '加入遊戲',
    downSeat: '退出遊戲',
    startGame: '開始',
    readyGame: '準備',
    cancelGame: '取消準備',
    cancel: '取消',
    confirm: '確定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音樂',
    sound: '音效',
    join: '進入',
    create: '創建',
    auto: '匹配',
    Room: '房間',
    room_number: '房號',
    copy: '複製',
    game_amount: '遊戲費用',
    player_numbers: '玩家數量:',
    room_exist: '房間不存在',
    enter_room_number: '輸入房間號',
    free: '免費',
    players: '玩家',
    Player: '玩家',
    Tickets: '門票',
    Empty: '空位',
    nextlevel: '下一關',
    relevel: '再玩一次',
    //幫助
    Tips: "\u5E6B\u52A9",
    Tips1: "1. \u6BCF\u5C40\u904A\u6232\u6BD4\u8CFD\u6642\u9593\uFF1A120\u79D2\u3002",
    Tips2: "2. \u6392\u540D\u898F\u5247\uFF1A\u904A\u6232\u6642\u9593\u7D50\u675F\u5F8C\uFF0C\u6839\u64DA\u73A9\u5BB6\u7684\u5F97\u5206\u9AD8\u4F4E\u9032\u884C\u6392\u540D\uFF0C\u5F97\u5206\u76F8\u540C\u6392\u540D\u76F8\u540C\u3002",
    Tips3: "3. \u6D88\u9664\u898F\u5247\uFF1A\u901A\u904E\u79FB\u52D5\u904A\u6232\u4E2D\u7684\u98DF\u7269\u8207\u81E8\u8FD1\u7684\u98DF\u7269\u4EA4\u63DB\u4F4D\u7F6E\u4F7F\u76F8\u540C\u7684\u98DF\u7269\u8655\u65BC\u76F8\u9130\u72C0\u614B\uFF0C\u82E5\u6EFF\u8DB3\u76F8\u9130\u7684\u540C\u6A23\u98DF\u7269>=3\u500B\u4F4D\u7F6E\u4EA4\u63DB\u6210\u529F\uFF0C\u5247\u5B8C\u6210\u6D88\u9664\u5F97\u5206\u3002",
    Tips4: "4. \u9053\u5177\u6D88\u9664\uFF1A\u5B8C\u6210\u7279\u6B8A\u7684\u6D88\u9664\u6703\u751F\u6210\u6D88\u9664\u9053\u5177\uFF0C\u9053\u5177\u548C\u9644\u8FD1\u584A\u4EA4\u63DB\u4F4D\u7F6E\u5F8C\u6703\u4F7F\u7528\u4E26\u6D88\u8017\u3002",
    Tips5: "5. \u9023\u7E8C\u6D88\u9664\uFF1A\u73A9\u5BB6\u4E00\u6B21\u6D88\u9664\u884C\u70BA\u5F15\u767C\u65B0\u98DF\u7269\u7522\u751F\uFF0C\u98DF\u7269\u4E0B\u843D\u5F8C\u89F8\u767C\u7684\u6D88\u9664\u70BA\u9023\u7E8C\u6D88\u9664\uFF0C\u9023\u7E8C\u6D88\u9664\u671F\u9593\u4E0D\u53EF\u64CD\u4F5C\u3002\u4E00\u6B21\u4E0B\u843D\u671F\u9593\u767C\u751F\u7684\u9023\u7E8C\u6D88\u9664\u8A18\u70BA1\u8F2A\u3002",
    //產生方式
    Generation_Method: "\u7522\u751F\u65B9\u5F0F\u548C\u4F7F\u7528\u6548\u679C",
    Generation_Method1: "1. \u6D88\u9664\u6574\u5217\u6216\u6574\u884C\u98DF\u7269\u3002",
    Generation_Method2: "2. \u6D88\u9664\u5176\u4E2D\u5FC3\u5411\u5916\u5EF6\u4F38\u768412\u500B\u98DF\u7269\u3002",
    Generation_Method3: "3. \u6D88\u9664\u87A2\u5E55\u4E2D\u6240\u6709\u8207\u8A72\u9053\u5177\u4EA4\u63DB\u4F4D\u7F6E\u76F8\u540C\u7684\u98DF\u7269\u3002",
    Generation_Method4: "4. \u6D88\u96641\u884C+1\u5217\u7684\u98DF\u7269\uFF0C\u5305\u62EC\u9396\u93C8\u548C\u51B0\u584A\u3002",
    Generation_Method5: "5. \u6D88\u96643\u884C+3\u5217\u7684\u98DF\u7269\uFF0C\u5305\u62EC\u9396\u93C8\u548C\u51B0\u584A\u3002",
    Generation_Method6: "6. \u5C07\u87A2\u5E55\u4E2D\u96A8\u6A5F\u4E00\u7A2E\u98DF\u7269\u8B8A\u70BA\u706B\u7BAD\u4E26\u91CB\u653E\u3002",
    Generation_Method7: "7. \u6D88\u9664\u5176\u4E2D\u5FC3\u5411\u5916\u5EF6\u4F38\u768424\u500B\u98DF\u7269\uFF0C\u5305\u62EC\u9396\u93C8\u548C\u51B0\u584A\u3002",
    Generation_Method8: "8. \u5C07\u87A2\u5E55\u4E2D\u96A8\u6A5F\u4E00\u7A2E\u98DF\u7269\u584A\u8B8A\u70BA\u70B8\u5F48\u4E26\u91CB\u653E\u3002",
    Generation_Method9: "9. \u6D88\u9664\u87A2\u5E55\u4E2D\u6240\u6709\u98DF\u7269\uFF0C\u5305\u62EC\u9396\u93C8\u548C\u51B0\u584A\u3002",
    //常駐任務
    Permanent_Task: "\u5E38\u99D0\u4EFB\u52D9",
    Permanent_Task1: "\u73A9\u5BB6\u6BCF\u6D88\u966410\u96BB\u8783\u87F9\uFF0C\u4FBF\u6703\u7372\u5F97\u5169\u6B21\u96A8\u6A5F\u6D88\u9664\u4E00\u584A2x2\u5340\u57DF\u7684\u734E\u52F5\uFF0C\u82E5\u672C\u6B21\u6D88\u9664\u8783\u87F9\u6EFF\u8DB3\u5169\u6B21\u53CA\u4EE5\u4E0A\uFF0C\u5247\u7B2C\u4E8C\u6B21\u958B\u59CB\u53EA\u7D66\u4E00\u7D442x2\u6D88\u9664\u3002",
    //隨機任務
    Random_Task: "\u96A8\u6A5F\u4EFB\u52D9",
    Random_Task1: "\u6839\u64DA\u4EBA\u6578\u589E\u52A0\u6240\u9700\u6D88\u9664\u7684\u6578\u91CF\uFF0C\u6BD4\u4F8B\u70BA\u52A0\u4E00\u4EBA\u5247\u9700\u591A\u6D88\u9664\u4E00\u500B\uFF0C\u4F8B\u59822\u4EBA\u5C40\u9700\u6D88\u96647\u6B21\uFF0C3\u4EBA\u5C40\u9700\u6D88\u96648\u6B21\u3002",
    Random_Task2: "- \u6BCF\u6B21\u6700\u591A\u540C\u6642\u5B58\u57281\u500B\u4EFB\u52D9\u3002",
    Random_Task3: "- \u4EFB\u52D9\u9806\u5E8F\u6BCF\u5C40\u958B\u5C40\u96A8\u6A5F\u751F\u6210\u4E00\u7A2E\uFF0C\u4F9D\u6B21\u4EA4\u66FF\u9032\u884C\u3002",
    Random_Task4: "- \u6D88\u96647\u500B\u6307\u5B9A\u984F\u8272\u53EF\u7372\u5F97\u4E00\u500B\u96A8\u6A5F\u5F69\u3002",
    Random_Task5: "- \u6D88\u96647\u500B\u6307\u5B9A\u984F\u8272\u53EF\u7372\u5F97\u4E00\u500B\u51B0\u584A\u3002",
    Random_Task6: "- \u6D88\u96647\u500B\u6307\u5B9A\u984F\u8272\u53EF\u7372\u5F97\u4E00\u500B\u9396\u93C8\u3002",
    Random_Task7: "- \u6D88\u96647\u500B\u6307\u5B9A\u984F\u8272\u53EF\u7372\u5F97\u4E00\u500B\u96A8\u6A5F\u5F62\u614B\u7684\u706B\u7BAD\u9053\u5177\u3002",
    //鎖鏈簡介
    Chains: "\u9396\u93C8",
    Chains1: "1. \u6D88\u9664\u4EFB\u52D9\uFF1A\u904A\u6232\u6BD4\u8CFD\u958B\u59CB\u5F8C\uFF0C\u6BCF\u4F4D\u73A9\u5BB6\u6703\u6536\u5230\u7CFB\u7D71\u6D3E\u767C\u7684\u4EFB\u52D9\u3002\u5B8C\u6210\u4EFB\u52D9\u6703\u5411\u5176\u4ED6\u73A9\u5BB6\u91CB\u653E\u59A8\u7919\u9053\u5177\u2014\u2014\u9396\u93C8\u3002",
    Chains2: "2. \u9396\u93C8\u7684\u59A8\u7919\u6548\u679C\uFF1A\u96A8\u6A5F\u9396\u4F4F\u4E00\u500B\u4F4D\u7F6E\u5167\u7684\u98DF\u7269\u3002\u53D7\u9650\u5236\u671F\u9593\uFF0C\u9396\u93C8\u9396\u4F4F\u7684\u4F4D\u7F6E\u4E0D\u80FD\u79FB\u52D5\uFF0C\u9396\u93C8\u5167\u7684\u98DF\u7269\u53EF\u80FD\u6703\u56E0\u70BA\u5176\u4ED6\u5730\u584A\u7684\u6D88\u9664\u800C\u6539\u8B8A\uFF0C\u4E26\u975E\u9396\u4F4F\u56FA\u5B9A\u98DF\u7269\u3002",
    Chains3: "3. \u9396\u93C8\u7684\u89E3\u9664\uFF1A\u5728\u9396\u93C8\u9130\u8FD1\u4F4D\u7F6E\uFF08\u4E0A\u4E0B\u5DE6\u53F31\u683C\u7684\u584A\uFF09\u9032\u884C\u666E\u901A\u6D88\u9664\u5F8C\u6216\u8005\u5728\u9053\u5177\u6709\u6548\u5340\u57DF\u5167\uFF0C\u6D88\u96641\u6B21\u5373\u53EF\u89E3\u9664\u3002",
    //冰塊簡介
    Ice_Blocks: "\u51B0\u584A",
    Ice_Blocks1: "1. \u6D88\u9664\u4EFB\u52D9\uFF1A\u904A\u6232\u6BD4\u8CFD\u958B\u59CB\u5F8C\uFF0C\u6BCF\u4F4D\u73A9\u5BB6\u6703\u6536\u5230\u7CFB\u7D71\u6D3E\u767C\u7684\u4EFB\u52D9\u3002\u5B8C\u6210\u4EFB\u52D9\u6703\u5411\u5176\u4ED6\u73A9\u5BB6\u91CB\u653E\u59A8\u7919\u9053\u5177\u2014\u2014\u51B0\u584A\u3002",
    Ice_Blocks2: "2. \u51B0\u584A\u7684\u59A8\u7919\u6548\u679C\uFF1A\u96A8\u6A5F\u51CD\u4F4F\u4E00\u500B\u98DF\u7269\uFF0C\u53D7\u51CD\u671F\u9593\uFF0C\u4F4D\u7F6E\u53EF\u4EE5\u88AB\u52D5\u79FB\u52D5\uFF0C\u73A9\u5BB6\u4E0D\u80FD\u4E3B\u52D5\u79FB\u52D5\u3002",
    Ice_Blocks3: "3. \u51B0\u584A\u7684\u89E3\u9664\uFF1A\u5728\u51B0\u584A\u9130\u8FD1\u4F4D\u7F6E\uFF08\u4E0A\u4E0B\u5DE6\u53F31\u683C\u7684\u584A\uFF09\u9032\u884C\u666E\u901A\u6D88\u9664\u5F8C\u6216\u8005\u5728\u9053\u5177\u6709\u6548\u5340\u57DF\u5167,\u6D88\u96641\u6B21\u5373\u53EF\u89E3\u9664\u3002",
    // 得分細則
    Scoring_Details: "\u5F97\u5206\u7D30\u5247",
    Scoring_Details1: "\u666E\u901A\u6D88\u9664\uFF1A\u6D88\u9664\u55AE\u584A\u5F97\u5206=20\u5206;",
    Scoring_Details2: "\u9023\u7E8C\u6D88\u9664\uFF1A\u6D88\u9664\u55AE\u584A\u5F97\u5206=\u9023\u6D88\u6B21\u6578x20\u5206;",
    Scoring_Details3: "\u706B\u7BAD\u6D88\u9664\uFF1A\u706B\u7BAD\u555F\u52D5\u5F97\u5206=300\u5206;\u7531\u706B\u7BAD\u6D88\u9664\u7684\u55AE\u584A\u5F97\u5206=30\u5206;",
    Scoring_Details4: "\u70B8\u5F48\u6D88\u9664\uFF1A\u70B8\u5F48\u555F\u52D5\u5F97\u5206=500\u5206;\u7531\u70B8\u5F48\u6D88\u9664\u7684\u55AE\u584A\u5F97\u5206=50\u5206;",
    Scoring_Details5: "\u5F69\u8679\u6D88\u9664\uFF1A\u5F69\u8679\u555F\u52D5\u5F97\u5206=600\u5206;\u7531\u5F69\u8679\u6D88\u9664\u7684\u55AE\u584A\u5F97\u5206=60\u5206;",
    Scoring_Details6: "\u706B\u7BAD\u7D44\u5408\u6D88\u9664\uFF1A\u96D9\u706B\u7BAD\u555F\u52D5\u5F97\u5206=1200\u5206;\u7531\u8D85\u7D1A\u706B\u7BAD\u6D88\u9664\u7684\u55AE\u584A\u5F97\u5206=60\u5206;",
    Scoring_Details7: "\u70B8\u5F48\u706B\u7BAD\u7D44\u5408\u6D88\u9664\uFF1A\u70B8\u5F48\u706B\u7BAD\u7D44\u5408\u555F\u52D5\u5F97\u5206=1600\u5206;\u7531\u70B8\u5F48\u706B\u7BAD\u6D88\u9664\u7684\u55AE\u584A\u5F97\u5206=80\u5206;",
    Scoring_Details8: "\u5F69\u8679\u706B\u7BAD\u7D44\u5408\u6D88\u9664\uFF1A\u5F69\u8679\u706B\u7BAD\u7D44\u5408\u555F\u52D5\u5F97\u5206=1800\u5206;\u7531\u5F69\u8679\u706B\u7BAD\u6D88\u9664\u7684\u55AE\u584A\u5F97\u5206=90\u5206;",
    Scoring_Details9: "\u70B8\u5F48\u7D44\u5408\u6D88\u9664\uFF1A\u8D85\u7D1A\u70B8\u5F48\u555F\u52D5\u5F97\u5206=2000\u5206;\u7531\u8D85\u7D1A\u70B8\u5F48\u6D88\u9664\u7684\u55AE\u584A\u5F97\u5206=100\u5206;",
    Scoring_Details10: "\u5F69\u8679\u70B8\u5F48\u6D88\u9664\uFF1A\u5F69\u8679\u70B8\u5F48\u555F\u52D5\u5F97\u5206=2200\u5206;\u7531\u5F69\u8679\u70B8\u5F48\u6D88\u9664\u7684\u55AE\u584A\u5F97\u5206=110\u5206;",
    Scoring_Details11: "\u5F69\u8679\u7D44\u5408\u6D88\u9664\uFF1A\u8D85\u7D1A\u5F69\u8679\u555F\u52D5\u5F97\u5206=2400\u5206;\u7531\u8D85\u7D1A\u5F69\u8679\u6D88\u9664\u7684\u55AE\u584A\u5F97\u5206=120\u5206;",
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_HK = exports.language;

cc._RF.pop();