"use strict";
cc._RF.push(module, 'f530dRaLT5IBJrG6Sh+L1QP', 'MeshSdkApi');
// meshTools/tools/MeshSdkApi.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var MeshTools_1 = require("../MeshTools");
var BaseSDK_1 = require("../BaseSDK");
var MessageBaseBean_1 = require("../../scripts/net/MessageBaseBean");
var GameMgr_1 = require("../../scripts/common/GameMgr");
var EventCenter_1 = require("../../scripts/common/EventCenter");
var MeshSdk = require("MeshSdk");
// var ZegoGameClient = ZG.ZegoGameClient;
var MeshSdkApi = /** @class */ (function (_super) {
    __extends(MeshSdkApi, _super);
    function MeshSdkApi() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._meshSdk = MeshSdk.meshSDK; // new ZegoGameClient();
        _this._isChangeVoluming = false;
        _this.IsNotifyGameLoaded = false;
        _this.IsNotifyGameLoading = false;
        return _this;
    }
    // 监听 APP 事件  APP -> H5
    MeshSdkApi.prototype.AddAPPEvent = function () {
        // regReceiveMessage  注册 APP 回调到 H5 的函数
        this._meshSdk.regReceiveMessage({
            // 余额变更
            walletUpdate: function () {
                console.log("walletUpdate");
                var autoMessageBean = {
                    'msgId': MessageBaseBean_1.AutoMessageId.WalletUpdateMsg,
                    'data': {}
                };
                GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            },
            // code 变更回调
            serverCodeUpdate: function (data) {
                data.code = encodeURIComponent(data.code);
                var autoMessageBean = {
                    'msgId': MessageBaseBean_1.AutoMessageId.ServerCodeUpdateMsg,
                    'data': { code: data.code }
                };
                GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            }
        });
    };
    // H5 调用APP 获取APP 版本号
    MeshSdkApi.prototype.GetAppVersion = function (callback, errCallback) {
        callback && callback("1.0.0");
    };
    // H5 调用 APP  获取用户信息数据
    MeshSdkApi.prototype.GetConfig = function (callback) {
        if (MeshTools_1.MeshTools.Publish.isDataByURL) {
            callback({
                "gameConfig": {
                    "currencyIcon": "",
                    "sceneMode": 0
                },
                "code": MeshTools_1.MeshTools.Publish.code,
                "appId": MeshTools_1.MeshTools.Publish.appId,
                "language": MeshTools_1.MeshTools.Publish.language,
                "gameMode": MeshTools_1.MeshTools.Publish.gameMode,
                "userId": MeshTools_1.MeshTools.Publish.userId,
                "roomId": MeshTools_1.MeshTools.Publish.roomId,
                "appChannel": MeshTools_1.MeshTools.Publish.appChannel,
                "gsp": MeshTools_1.MeshTools.Publish.gsp
            });
            return;
        }
        if (CC_DEBUG) {
            callback({
                "gameConfig": {
                    "currencyIcon": "https://game-center-test.jieyou.shop/static/images/index/game_bean.png",
                    "sceneMode": 0
                },
                "code": "qFwaAVKyEYTmPey1vWA4Huq7bvto4xexT0UJRnh03vlwTghRwFyVsbO4JRLV",
                "appId": 66666666,
                "language": "0",
                "gameMode": 3,
                "userId": 1111,
                "roomId": "room01",
                "appChannel": "debug",
                "gsp": 8001
            });
            return;
        }
        this._meshSdk.getConfig().then(callback).catch(function (err) {
            cc.error(err);
        });
    };
    // 通知APP 游戏资源加载完了
    MeshSdkApi.prototype.HideLoading = function () {
        if (this.IsNotifyGameLoaded == true) {
            return;
        }
        this.IsNotifyGameLoaded = true;
        this._meshSdk.gameLoaded();
    };
    // 销毁 WebView
    MeshSdkApi.prototype.CloseWebView = function () {
        this._meshSdk.destroy();
    };
    // 余额不足  H5 调用 APP
    MeshSdkApi.prototype.ShowAppShop = function (type) {
        cc.log("余额不足回调");
        this._meshSdk.gameRecharge();
    };
    return MeshSdkApi;
}(BaseSDK_1.BaseSDK));
exports.default = MeshSdkApi;

cc._RF.pop();