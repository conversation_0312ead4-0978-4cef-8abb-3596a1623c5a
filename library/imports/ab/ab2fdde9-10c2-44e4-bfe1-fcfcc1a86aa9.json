[{"__type__": "cc.Prefab", "_name": "primitives", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": true}, {"__type__": "cc.Node", "_name": "RootNode", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}, {"__id__": 8}, {"__id__": 11}, {"__id__": 14}, {"__id__": 17}, {"__id__": 20}, {"__id__": 23}], "_active": true, "_components": [], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "capsule", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 8.146033536604618e-08, 0, 0, 0.9999999999999967, 100, 100, 100]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 9.334666828389458e-06, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "a5849239-3ad3-41d1-8ab4-ae9fea11f97f"}], "_mesh": {"__uuid__": "83f5eff8-3385-4f95-9b76-8da0aa1d96cd"}, "_receiveShadows": false, "_shadowCastingMode": 0, "_enableAutoBatch": false, "textures": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "capsule", "sync": false}, {"__type__": "cc.Node", "_name": "plane", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": {"__id__": 7}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 8.146033536604618e-08, 0, 0, 0.9999999999999967, 100, 100, 100]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 9.334666828389458e-06, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "_materials": [{"__uuid__": "a5849239-3ad3-41d1-8ab4-ae9fea11f97f"}], "_mesh": {"__uuid__": "a1ef2fc9-9c57-418a-8f69-6bed9a7a0e7f"}, "_receiveShadows": false, "_shadowCastingMode": 0, "_enableAutoBatch": false, "textures": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "plane", "sync": false}, {"__type__": "cc.Node", "_name": "cone", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 9}], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, -0.7071068286895765, 0, 0, 0.7071067336835153, 100, 100, 100]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": -90.00000769819565, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_materials": [{"__uuid__": "a5849239-3ad3-41d1-8ab4-ae9fea11f97f"}], "_mesh": {"__uuid__": "7a17de6e-227a-46b1-8009-e7157d4d3acf"}, "_receiveShadows": false, "_shadowCastingMode": 0, "_enableAutoBatch": false, "textures": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cone", "sync": false}, {"__type__": "cc.Node", "_name": "torus", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 12}], "_prefab": {"__id__": 13}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 8.146033536604618e-08, 0, 0, 0.9999999999999967, 100, 100, 100]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 9.334666828389458e-06, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_materials": [{"__uuid__": "a5849239-3ad3-41d1-8ab4-ae9fea11f97f"}], "_mesh": {"__uuid__": "14c74869-bdb4-4f57-86d8-a7875de2be30"}, "_receiveShadows": false, "_shadowCastingMode": 0, "_enableAutoBatch": false, "textures": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "torus", "sync": false}, {"__type__": "cc.Node", "_name": "sphere", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 15}], "_prefab": {"__id__": 16}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 8.146033536604618e-08, 0, 0, 0.9999999999999967, 100, 100, 100]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 9.334666828389458e-06, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_materials": [{"__uuid__": "a5849239-3ad3-41d1-8ab4-ae9fea11f97f"}], "_mesh": {"__uuid__": "3bbdb0f6-c5f6-45de-9f33-8b5cbafb4d6d"}, "_receiveShadows": false, "_shadowCastingMode": 0, "_enableAutoBatch": false, "textures": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "sphere", "sync": false}, {"__type__": "cc.Node", "_name": "quad", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 18}], "_prefab": {"__id__": 19}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 8.146033536604618e-08, 0, 0, 0.9999999999999967, 100, 100, 100]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 9.334666828389458e-06, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "_materials": [{"__uuid__": "a5849239-3ad3-41d1-8ab4-ae9fea11f97f"}], "_mesh": {"__uuid__": "e93d3fa9-8c21-4375-8a21-14ba84066c77"}, "_receiveShadows": false, "_shadowCastingMode": 0, "_enableAutoBatch": false, "textures": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "quad", "sync": false}, {"__type__": "cc.Node", "_name": "cylinder", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 21}], "_prefab": {"__id__": 22}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 8.146033536604618e-08, 0, 0, 0.9999999999999967, 100, 100, 100]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 9.334666828389458e-06, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "_materials": [{"__uuid__": "a5849239-3ad3-41d1-8ab4-ae9fea11f97f"}], "_mesh": {"__uuid__": "b430cea3-6ab3-4106-b073-26c698918edd"}, "_receiveShadows": false, "_shadowCastingMode": 0, "_enableAutoBatch": false, "textures": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cylinder", "sync": false}, {"__type__": "cc.Node", "_name": "box", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 24}], "_prefab": {"__id__": 25}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 8.146033536604618e-08, 0, 0, 0.9999999999999967, 100, 100, 100]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 9.334666828389458e-06, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_materials": [{"__uuid__": "a5849239-3ad3-41d1-8ab4-ae9fea11f97f"}], "_mesh": {"__uuid__": "046f172c-1574-488b-bbb8-6415a9adb96d"}, "_receiveShadows": false, "_shadowCastingMode": 0, "_enableAutoBatch": false, "textures": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "box", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]