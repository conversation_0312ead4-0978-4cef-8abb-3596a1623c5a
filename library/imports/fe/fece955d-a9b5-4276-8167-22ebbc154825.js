"use strict";
cc._RF.push(module, 'fece9VdqbVCdoFnIuu8FUgl', 'PlayerScoreController');
// scripts/pfb/PlayerScoreController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerScoreController = /** @class */ (function (_super) {
    __extends(PlayerScoreController, _super);
    function PlayerScoreController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.nameLabel = null; //用户昵称
        // 分数显示相关节点
        _this.scoreBgMy = null; //我的分数背景节点 score_bg_my
        _this.scoreBgOthers = null; //其他人的分数背景节点 score_bg_others
        // 加减分效果节点
        _this.addScoreNode = null; //加分背景节点 addscore
        _this.subScoreNode = null; //减分背景节点 deductscore
        // 当前用户数据
        _this.currentUser = null;
        return _this;
    }
    PlayerScoreController.prototype.start = function () {
        // 初始化时隐藏所有加减分效果
        this.hideScoreEffects();
    };
    /**
     * 设置玩家数据
     * @param user 房间用户数据
     */
    PlayerScoreController.prototype.setData = function (user) {
        this.currentUser = user;
        if (user == null) {
            // 清空数据
            this.avatar.active = false;
            this.nameLabel.string = "";
            this.hideAllScoreBackgrounds();
            this.hideScoreEffects();
        }
        else {
            // 设置头像和昵称
            Tools_1.Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);
            var nicknameLabel = this.nameLabel.getComponent(NickNameLabel_1.default);
            nicknameLabel.string = user.nickName;
            this.avatar.active = true;
            // 设置分数显示
            this.updateScore(user.score || 0);
        }
    };
    /**
     * 更新分数显示
     * @param score 新的分数值
     */
    PlayerScoreController.prototype.updateScore = function (score) {
        if (!this.currentUser)
            return;
        var isMyself = this.isCurrentUser(this.currentUser.userId);
        if (isMyself) {
            // 显示我的分数
            this.showMyScore(score);
        }
        else {
            // 显示其他人的分数
            this.showOthersScore(score);
        }
    };
    /**
     * 判断是否为当前登录用户
     * @param userId 用户ID
     */
    PlayerScoreController.prototype.isCurrentUser = function (userId) {
        var _a, _b;
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        return userId === currentUserId;
    };
    /**
     * 显示我的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showMyScore = function (score) {
        // 显示我的分数背景，隐藏其他人的
        if (this.scoreBgMy) {
            this.scoreBgMy.active = true;
            // 获取my_score文本节点并设置分数
            var myScoreLabel = this.scoreBgMy.getChildByName("my_score");
            if (myScoreLabel) {
                var labelComponent = myScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示其他人的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showOthersScore = function (score) {
        // 显示其他人的分数背景，隐藏我的
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = true;
            // 获取other_score文本节点并设置分数
            var otherScoreLabel = this.scoreBgOthers.getChildByName("other_score");
            if (otherScoreLabel) {
                var labelComponent = otherScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
    };
    /**
     * 隐藏所有分数背景
     */
    PlayerScoreController.prototype.hideAllScoreBackgrounds = function () {
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示加分效果
     * @param addValue 加分数值
     */
    PlayerScoreController.prototype.showAddScore = function (addValue) {
        var _this = this;
        if (this.addScoreNode) {
            this.addScoreNode.active = true;
            // 获取change_score文本节点并设置加分文本
            var changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.addScoreNode) {
                    _this.addScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    PlayerScoreController.prototype.showSubScore = function (subValue) {
        var _this = this;
        if (this.subScoreNode) {
            this.subScoreNode.active = true;
            // 获取change_score文本节点并设置减分文本
            var changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.subScoreNode) {
                    _this.subScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 隐藏加减分效果节点
     */
    PlayerScoreController.prototype.hideScoreEffects = function () {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    };
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Label)
    ], PlayerScoreController.prototype, "nameLabel", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgMy", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgOthers", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "addScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "subScoreNode", void 0);
    PlayerScoreController = __decorate([
        ccclass
    ], PlayerScoreController);
    return PlayerScoreController;
}(cc.Component));
exports.default = PlayerScoreController;

cc._RF.pop();