{"__type__": "cc.EffectAsset", "_name": "label-gradient", "_objFlags": 0, "_native": "", "properties": null, "techniques": [{"passes": [{"blendState": {"targets": [{"blend": true}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"texture": {"value": "white", "type": 29}, "topColor": {"value": [0.831, 0.992, 0.878, 1], "null": null, "editor": "color", "type": 16}, "bottomColor": {"value": [0.902, 0.992, 1, 1], "editor": "color", "type": 16}}, "program": "label-gradient|vs|fs"}]}], "shaders": [{"hash": 1650339068, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec2 a_uv0;\nin vec4 a_color;\nout vec2 v_uv;\nout vec4 v_color;\nvoid main () {\n  gl_Position = cc_matViewProj * cc_matWorld * vec4(a_position, 1.0);\n  v_uv = a_uv0;\n  v_color = a_color;\n}", "frag": "\nprecision highp float;\nuniform sampler2D texture;\nuniform Properties {\n  vec4 topColor;\n  vec4 bottomColor;\n};\nin vec2 v_uv;\nin vec4 v_color;\nvoid main () {\n  vec4 texColor = texture(texture, v_uv);\n  vec4 gradient = mix(topColor, bottomColor, v_uv.y);\n  vec4 finalColor = texColor * gradient;\n  finalColor.a *= v_color.a;\n  gl_FragColor = finalColor;\n}"}, "glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec2 a_uv0;\nattribute vec4 a_color;\nvarying vec2 v_uv;\nvarying vec4 v_color;\nvoid main () {\n  gl_Position = cc_matViewProj * cc_matWorld * vec4(a_position, 1.0);\n  v_uv = a_uv0;\n  v_color = a_color;\n}", "frag": "\nprecision highp float;\nuniform sampler2D texture;\nuniform vec4 topColor;\nuniform vec4 bottomColor;\nvarying vec2 v_uv;\nvarying vec4 v_color;\nvoid main () {\n  vec4 texColor = texture2D(texture, v_uv);\n  vec4 gradient = mix(topColor, bottomColor, v_uv.y);\n  vec4 finalColor = texColor * gradient;\n  finalColor.a *= v_color.a;\n  gl_FragColor = finalColor;\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}], "samplers": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplers": []}}, "defines": [], "blocks": [{"name": "Properties", "members": [{"name": "topColor", "type": 16, "count": 1}, {"name": "bottomColor", "type": 16, "count": 1}], "defines": [], "binding": 0}], "samplers": [{"name": "texture", "type": 29, "count": 1, "defines": [], "binding": 30}], "record": null, "name": "label-gradient|vs|fs"}]}