{"version": 3, "sources": ["assets/resources/i18n/zh_CN.ts"], "names": [], "mappings": ";;;;;;;AAAa,QAAA,QAAQ,GAAG;IACxB,SAAS;IACL,QAAQ,EAAE,QAAQ;IAClB,SAAS,EAAE,OAAO;IAClB,mBAAmB,EAAE,UAAU;IAC/B,iBAAiB,EAAE,QAAQ;IAC3B,YAAY,EAAE,MAAM;IACpB,UAAU,EAAE,MAAM;IAClB,eAAe,EAAE,OAAO;IACxB,iBAAiB,EAAE,UAAU;IAC7B,gBAAgB,EAAE,OAAO;IACzB,uBAAuB,EAAE,QAAQ;IACjC,eAAe,EAAE,SAAS;IAC1B,WAAW,EAAE,aAAa;IAE1B,gBAAgB,EAAE,QAAQ;IAC1B,sBAAsB,EAAE,QAAQ;IAChC,QAAQ,EAAE,eAAe;IAEzB,MAAM,EAAC,MAAM;IACb,QAAQ,EAAC,MAAM;IACf,SAAS,EAAC,IAAI;IACd,SAAS,EAAC,IAAI;IACd,UAAU,EAAC,MAAM;IACjB,MAAM,EAAC,IAAI;IACX,OAAO,EAAC,IAAI;IACZ,QAAQ,EAAC,IAAI;IACb,IAAI,EAAC,IAAI;IACT,KAAK,EAAC,IAAI;IACV,KAAK,EAAC,IAAI;IACV,KAAK,EAAC,IAAI;IACV,IAAI,EAAC,IAAI;IACT,MAAM,EAAC,IAAI;IACX,IAAI,EAAC,IAAI;IACT,IAAI,EAAC,IAAI;IACT,WAAW,EAAC,KAAK;IACjB,IAAI,EAAC,IAAI;IACT,WAAW,EAAC,MAAM;IAClB,cAAc,EAAC,OAAO;IACtB,UAAU,EAAC,OAAO;IAClB,iBAAiB,EAAC,OAAO;IACzB,IAAI,EAAC,IAAI;IACT,OAAO,EAAC,IAAI;IACZ,MAAM,EAAC,IAAI;IACX,OAAO,EAAC,IAAI;IACZ,KAAK,EAAC,IAAI;IAEV,SAAS,EAAC,KAAK;IAEf,OAAO,EAAC,MAAM;CAEjB,CAAC;AAEF,IAAM,KAAK,GAAG,EAAS,CAAC;AACxB,IAAI,CAAC,KAAK,CAAC,QAAQ;IAAE,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AACzC,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,gBAAQ,CAAC", "file": "", "sourceRoot": "/", "sourcesContent": ["export const language = {\n//这部分是通用的\n    kickout1: '您被请出房间',\n    LeaveRoom: '房间已解散',\n    InsufficientBalance: '余额不足，去充值',\n    GameRouteNotFound: '游戏线路异常',\n    NetworkError: '网络异常',\n    RoomIsFull: '房间已满',\n    EnterRoomNumber: '输入房间号',\n    GetUserInfoFailed: '获取用户信息失败',\n    RoomDoesNotExist: '房间不存在',\n    FailedToDeductGoldCoins: '扣除金币失败',\n    ExitApplication: '确定退出游戏？',\n    QuitTheGame: '退出后将无法返回游戏。',\n\n    NotEnoughPlayers: '玩家数量不足',\n    TheGameIsFullOfPlayers: '玩家数量已满',\n    kickout2: '是否将 {0} 请出房间?',//踢出玩家文案\n\n    upSeat:'加入游戏',\n    downSeat:'退出游戏', \n    startGame:'开始', \n    readyGame:'准备', \n    cancelGame:'取消准备', \n    cancel:'取消', \n    confirm:'确定', \n    kickout3:'踢出', \n    back:'返回',//返回\n    leave:'退出',\n    music:'音乐',\n    sound:'音效',\n    join:'加入', //加入\n    create:'创建', //创建\n    auto:'匹配',\n    Room:'房间',\n    room_number:'房间号', //房间号\n    copy:'复制', //复制\n    game_amount:'游戏费用', //游戏费用\n    player_numbers:'玩家数量:', //玩家数量\n    room_exist:'房间不存在',//房间不存在\n    enter_room_number:'输入房间号',//输入房间号\n    free:'免费',\n    players:'玩家', //玩家\n    Player:'玩家',\n    Tickets:'门票',\n    Empty:'空位',\n\n    nextlevel:'下一关',//下一关\n\n    relevel:'再玩一次', //再来一局\n\n};\n\nconst cocos = cc as any;\nif (!cocos.Jou_i18n) cocos.Jou_i18n = {};\ncocos.Jou_i18n.zh_CN = language;"]}