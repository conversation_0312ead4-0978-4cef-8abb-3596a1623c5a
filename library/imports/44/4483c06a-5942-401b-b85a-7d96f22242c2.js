"use strict";
cc._RF.push(module, '4483cBqWUJAG7hafZbyIkLC', 'CongratsItemController');
// scripts/pfb/CongratsItemController.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Publish_1 = require("../../meshTools/tools/Publish");
var Config_1 = require("../util/Config");
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var ccclass = cc._decorator.ccclass;
var CongratsItemController = /** @class */ (function (_super) {
    __extends(CongratsItemController, _super);
    function CongratsItemController() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    CongratsItemController.prototype.start = function () {
        this.boardIconCrownNode = this.node.getChildByName('board_icon_crown'); //皇冠的节点
        this.boardNum = this.node.getChildByName('board_num'); //皇冠的节点
        this.avatarNode = this.node.getChildByName('avatar'); //头像的节点
        this.nameNode = this.node.getChildByName('name_layout').getChildByName('name'); //名称的节点
        this.stepNode = this.node.getChildByName("step_layout").getChildByName('step'); // 步数的节点
        this.numberNode = this.node.getChildByName('congrats_list_frame').getChildByName('number_view').getChildByName('number'); //金豆的节点
        this.beanIcon = this.node.getChildByName('congrats_list_frame').getChildByName('board_icon_beans'); //金豆图标
    };
    //设置数据
    //settleType:结算类型
    //intUserID:中断游戏的玩家Id
    CongratsItemController.prototype.createData = function (settlement, gameUsers) {
        if (settlement.rank <= 3) {
            this.boardIconCrownNode.active = true;
            this.boardNum.active = false;
            //设置皇冠图片
            switch (settlement.rank) {
                case 1:
                    Tools_1.Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config_1.Config.gameRes + 'board_icon_crown_01');
                    break;
                case 2:
                    Tools_1.Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config_1.Config.gameRes + 'board_icon_crown_02');
                    break;
                case 3:
                    Tools_1.Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config_1.Config.gameRes + 'board_icon_crown_03');
                    break;
            }
        }
        else {
            this.boardIconCrownNode.active = false;
            this.boardNum.active = true;
            this.boardNum.getComponent(cc.Label).string = settlement.rank + ''; //显示名次
        }
        // 根据数据类型显示不同的分数字段
        var scoreValue;
        if ('score' in settlement) {
            // UserSettlement 类型
            scoreValue = settlement.score;
        }
        else {
            // PlayerFinalResult 类型
            scoreValue = settlement.totalScore;
        }
        this.stepNode.getComponent(cc.Label).string = scoreValue + ''; //显示分数
        var index = gameUsers.findIndex(function (item) { return item.userId === settlement.userId; }); //搜索
        if (index != -1) {
            var user = gameUsers[index];
            Tools_1.Tools.setNodeSpriteFrameUrl(this.avatarNode, user.avatar);
            this.nameNode.getComponent(NickNameLabel_1.default).string = user.nickName;
        }
        this.numberNode.getComponent(cc.Label).string = Tools_1.Tools.NumToTBMK(settlement.coinChg);
        if (Publish_1.Publish.GetInstance().currencyIcon != null && Publish_1.Publish.GetInstance().currencyIcon !== '') {
            Tools_1.Tools.setNodeSpriteFrameUrl(this.beanIcon, Publish_1.Publish.GetInstance().currencyIcon);
        }
    };
    CongratsItemController = __decorate([
        ccclass
    ], CongratsItemController);
    return CongratsItemController;
}(cc.Component));
exports.default = CongratsItemController;

cc._RF.pop();