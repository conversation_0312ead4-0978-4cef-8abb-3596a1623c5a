<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>angle</key>
    <integer>360</integer>
    <key>angleVariance</key>
    <integer>360</integer>
    <key>blendFuncDestination</key>
    <integer>1</integer>
    <key>blendFuncSource</key>
    <integer>770</integer>
    <key>duration</key>
    <integer>-1</integer>
    <key>emitterType</key>
    <integer>0</integer>
    <key>finishColorAlpha</key>
    <real>0.8399999737739563</real>
    <key>finishColorBlue</key>
    <real>0.0771484375</real>
    <key>finishColorGreen</key>
    <real>0.6349284052848816</real>
    <key>finishColorRed</key>
    <real>0.6808268427848816</real>
    <key>finishColorVarianceAlpha</key>
    <real>0.7400000095367432</real>
    <key>finishColorVarianceBlue</key>
    <real>0.9800000190734863</real>
    <key>finishColorVarianceGreen</key>
    <real>0.9800000190734863</real>
    <key>finishColorVarianceRed</key>
    <real>0.41999998688697815</real>
    <key>finishParticleSize</key>
    <real>30.31999969482422</real>
    <key>finishParticleSizeVariance</key>
    <integer>0</integer>
    <key>gravityx</key>
    <real>0.25</real>
    <key>gravityy</key>
    <real>0.8600000143051147</real>
    <key>maxParticles</key>
    <integer>200</integer>
    <key>maxRadius</key>
    <integer>100</integer>
    <key>maxRadiusVariance</key>
    <integer>0</integer>
    <key>minRadius</key>
    <integer>0</integer>
    <key>particleLifespan</key>
    <real>0.20000000298023224</real>
    <key>particleLifespanVariance</key>
    <real>0.5</real>
    <key>radialAccelVariance</key>
    <real>65.79000091552734</real>
    <key>radialAcceleration</key>
    <real>-671.0499877929688</real>
    <key>rotatePerSecond</key>
    <integer>0</integer>
    <key>rotatePerSecondVariance</key>
    <integer>0</integer>
    <key>rotationEnd</key>
    <real>-47.369998931884766</real>
    <key>rotationEndVariance</key>
    <real>-142.11000061035156</real>
    <key>rotationStart</key>
    <real>-47.369998931884766</real>
    <key>rotationStartVariance</key>
    <integer>0</integer>
    <key>sourcePositionVariancex</key>
    <integer>7</integer>
    <key>sourcePositionVariancey</key>
    <integer>7</integer>
    <key>sourcePositionx</key>
    <real>373.7277526855469</real>
    <key>sourcePositiony</key>
    <real>478.40472412109375</real>
    <key>speed</key>
    <integer>0</integer>
    <key>speedVariance</key>
    <real>190.7899932861328</real>
    <key>startColorAlpha</key>
    <real>0.6399999856948853</real>
    <key>startColorBlue</key>
    <real>0.3375650942325592</real>
    <key>startColorGreen</key>
    <real>0.7879231572151184</real>
    <key>startColorRed</key>
    <real>0.794921875</real>
    <key>startColorVarianceAlpha</key>
    <real>0.7799999713897705</real>
    <key>startColorVarianceBlue</key>
    <real>0.6800000071525574</real>
    <key>startColorVarianceGreen</key>
    <integer>1</integer>
    <key>startColorVarianceRed</key>
    <real>0.8999999761581421</real>
    <key>startParticleSize</key>
    <real>3.369999885559082</real>
    <key>startParticleSizeVariance</key>
    <integer>50</integer>
    <key>tangentialAccelVariance</key>
    <real>65.79000091552734</real>
    <key>tangentialAcceleration</key>
    <real>-92.11000061035156</real>
    <key>spriteFrameUuid</key>
    <string>472df5d3-35e7-4184-9e6c-7f41bee65ee3</string>
  </dict>
</plist>