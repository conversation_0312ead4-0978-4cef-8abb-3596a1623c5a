{"version": 3, "sources": ["assets/scripts/hall/HallPageController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;;AAGtF,iDAAgD;AAChD,6CAA4C;AAC5C,8CAA6C;AAC7C,4DAA2D;AAC3D,sDAA0D;AAC1D,sDAAiD;AACjD,qDAAoD;AACpD,+DAA0D;AAC1D,+DAA0D;AAC1D,qEAAgE;AAChE,iEAA4D;AAC5D,+EAA0E;AAC1E,iEAA4D;AAC5D,qEAAgE;AAE1D,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C,IAAY,WAGX;AAHD,WAAY,WAAW;IACnB,2DAAW,CAAA;IACX,6DAAY,CAAA;AAChB,CAAC,EAHW,WAAW,GAAX,mBAAW,KAAX,mBAAW,QAGtB;AAGD;IAAgD,sCAAY;IAA5D;QAAA,qEAqLC;QAlLG,oBAAc,GAAY,IAAI,CAAC;QAE/B,qBAAe,GAAY,IAAI,CAAC;QAEhC,0BAAoB,GAAyB,IAAI,CAAA,CAAC,QAAQ;QAE1D,2BAAqB,GAA0B,IAAI,CAAA,CAAC,SAAS;QAE7D,6BAAuB,GAA4B,IAAI,CAAA,CAAC,MAAM;QAE9D,6BAAuB,GAA4B,IAAI,CAAA,CAAC,cAAc;QAEtE,qBAAe,GAAoB,IAAI,CAAA,CAAE,WAAW;QAEpD,+BAAyB,GAA8B,IAAI,CAAA,CAAC,WAAW;QAEvE,iBAAW,GAAgB,IAAI,CAAA;;QAiK/B,iBAAiB;IACrB,CAAC;IA7JG,mCAAM,GAAN;QACI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAA;QAClF,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,+BAAqB,CAAC,CAAA;IAEzF,CAAC;IAES,qCAAQ,GAAlB;QACI,2BAAY,CAAC,OAAO,EAAE,CAAA;QACtB,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;IAEhD,CAAC;IAED,kCAAK,GAAL;QAAA,iBA8BC;QA5BG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC;YAC/B,QAAQ;YACR,KAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAC,cAAK,CAAC,CAAC,CAAA;QAC7C,CAAC,EAAE;YACC,UAAU;YACV,KAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAK,CAAC,CAAC,CAAA;QAC1C,CAAC,EAAE;YACC,QAAQ;YACR,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAK,CAAC,CAAC,CAAA;QAC7C,CAAC,EAAE;YACC,YAAY;YACZ,KAAI,CAAC,aAAa,CAAC,qBAAS,CAAC,kBAAkB,CAAC,CAAA;QAEpD,CAAC,EAAE;YACC,eAAe;YACf,KAAI,CAAC,aAAa,CAAC,qBAAS,CAAC,mBAAmB,CAAC,CAAA;QAErD,CAAC,EAAE,UAAC,MAAc,EAAE,QAAgB;YAChC,2BAA2B;YAC3B,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;QACvD,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC;YAChC,aAAa;YACb,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;QAE5E,CAAC,CAAC,CAAA;IAEN,CAAC;IACD,uCAAU,GAAV;QACI,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAA;IAC1C,CAAC;IAED,aAAa;IACb,0CAAa,GAAb,UAAc,KAAgB;QAC1B,2CAA2C;QAC3C,IAAI,mCAAgB,CAAC,WAAW,EAAE,CAAC,QAAQ,IAAI,kCAAkB,CAAC,SAAS,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,IAAI,IAAI,EAAE;YACvH,OAAO;SACV;QAED,qBAAqB;QACrB,IAAI,WAAW,GAAgB;YAC3B,SAAS,EAAE,uBAAU,CAAC,WAAW,EAAE,CAAC,OAAO;YAC3C,GAAG,EAAE,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU;SAC3C,CAAA;QACD,aAAa;QACb,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;IAC/D,CAAC;IAED,cAAc;IACd,2CAAc,GAAd,UAAe,WAAwB;QACnC,IAAI,IAAI,CAAC,WAAW,KAAK,WAAW,EAAE;YAClC,OAAM;SACT;QACD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,KAAK,CAAA;QAClC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAA;QACnC,QAAQ,WAAW,EAAE;YACjB,KAAK,WAAW,CAAC,WAAW;gBACxB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAA;gBACjC,MAAM;YACV,KAAK,WAAW,CAAC,YAAY;gBACzB,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAA;gBAClC,MAAM;SACb;IACL,CAAC;IAGD,yCAAY,GAAZ;QACI,aAAa;QACb,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC3B,IAAI,SAAS,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAA;QAClD,IAAI,SAAS,EAAE;YACX,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,OAAO;gBAC/B,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAA,CAAA,gBAAgB;aACzF;YACD,IAAI,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,EAAC,QAAQ;gBACnC,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,mBAAmB,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;aACxH;iBAAM;gBACH,qBAAqB;gBACrB,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAA;aAC1C;YACD,wBAAwB;YACxB,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,SAAS,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,YAAY,EAAE;gBACpG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;aAC/C;SACJ;QACD,IAAI,CAAC,OAAO,EAAE,CAAA;QACd,cAAc;QACd,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;IACrF,CAAC;IAED,UAAU;IACV,4CAAe,GAAf,UAAgB,YAA0B;QACtC,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAA;IAC3D,CAAC;IACD,MAAM;IACN,sCAAS,GAAT,UAAU,iBAAoC;QAC1C,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAA;IAC1D,CAAC;IAGD,MAAM;IACN,oCAAO,GAAP;QACI,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAA;IACvC,CAAC;IAED,cAAc;IACd,4CAAe,GAAf;QACI,IAAI,CAAC,qBAAqB,CAAC,eAAe,EAAE,CAAA;IAChD,CAAC;IACD,QAAQ;IACR,wCAAW,GAAX;QACI,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAA;IAC5C,CAAC;IAED,QAAQ;IACR,2CAAc,GAAd;QACI,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,CAAA;IAC9C,CAAC;IACD,OAAO;IACP,sCAAS,GAAT;QACI,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,CAAA;IACzC,CAAC;IACD,SAAS;IACT,0CAAa,GAAb,UAAc,sBAA8C;QACxD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAA;SAClE;IAEL,CAAC;IAED;;;OAGG;IACH,6CAAgB,GAAhB,UAAiB,iBAAsB;QACnC,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;SACtE;IACL,CAAC;IA/KD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACa;IAE/B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACc;IAEhC;QADC,QAAQ,CAAC,8BAAoB,CAAC;oEACkB;IAEjD;QADC,QAAQ,CAAC,+BAAqB,CAAC;qEACmB;IAEnD;QADC,QAAQ,CAAC,iCAAuB,CAAC;uEACqB;IAEvD;QADC,QAAQ,CAAC,iCAAuB,CAAC;uEACqB;IAEvD;QADC,QAAQ,CAAC,yBAAe,CAAC;+DACa;IAEvC;QADC,QAAQ,CAAC,mCAAyB,CAAC;yEACuB;IAjB1C,kBAAkB;QADtC,OAAO;OACa,kBAAkB,CAqLtC;IAAD,yBAAC;CArLD,AAqLC,CArL+C,EAAE,CAAC,SAAS,GAqL3D;kBArLoB,kBAAkB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { AcceptInvite, NoticeLeaveInvite, NoticeUserInviteStatus, PairRequest } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport { MessageId } from \"../net/MessageId\";\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport { WebSocketToolState } from \"../net/WebSocketTool\";\nimport ToastController from \"../ToastController\";\nimport { AudioManager } from \"../util/AudioManager\";\nimport HallParentController from \"./HallParentController\";\nimport InfoDialogController from \"./InfoDialogController\";\nimport KickOutDialogController from \"./KickOutDialogController\";\nimport LeaveDialogController from \"./LeaveDialogController\";\nimport LevelSelectPageController from \"./Level/LevelSelectPageController\";\nimport MatchParentController from \"./MatchParentController\";\nimport SettingDialogController from \"./SettingDialogController\";\n\nconst { ccclass, property } = cc._decorator;\n\n\nexport enum HallOrMatch {\n    HALL_PARENT,    //大厅页面\n    MATCH_PARENT,   //匹配页面\n}\n\n@ccclass\nexport default class HallPageController extends cc.Component {\n\n    @property(cc.Node)\n    hallParentNode: cc.Node = null;\n    @property(cc.Node)\n    matchParentNode: cc.Node = null;\n    @property(InfoDialogController)\n    infoDialogController: InfoDialogController = null //道具简介弹窗\n    @property(LeaveDialogController)\n    leaveDialogController: LeaveDialogController = null // 退出游戏弹窗\n    @property(SettingDialogController)\n    settingDialogController: SettingDialogController = null //设置弹窗\n    @property(KickOutDialogController)\n    kickOutDialogController: KickOutDialogController = null //踢出用户的 dialog\n    @property(ToastController)\n    toastController: ToastController = null  //toast 的布局\n    @property(LevelSelectPageController)\n    levelSelectPageController: LevelSelectPageController = null //关卡选择页面控制器\n\n    hallOrMatch: HallOrMatch = null\n\n    hallParentController: HallParentController\n    matchParentController: MatchParentController\n\n    onLoad() {\n        this.hallParentController = this.hallParentNode.getComponent(HallParentController)\n        this.matchParentController = this.matchParentNode.getComponent(MatchParentController)\n\n    }\n\n    protected onEnable(): void {\n        AudioManager.playBgm()\n        this.setHallOrMatch(HallOrMatch.HALL_PARENT)\n\n    }\n\n    start() {\n\n        this.hallParentController.setClick(() => {\n            //返回键的回调\n            this.leaveDialogController.show(0,()=>{})\n        }, () => {\n            //info 的回调\n            this.infoDialogController.show(()=>{})\n        }, () => {\n            //设置键的回调\n            this.settingDialogController.show(()=>{})\n        }, () => {\n            //start 按钮点击\n            this.startOrCreate(MessageId.MsgTypePairRequest)\n\n        }, () => {\n            //create 点击创建房间\n            this.startOrCreate(MessageId.MsgTypeCreateInvite)\n\n        }, (userId: string, nickname: string) => {\n            //点击创建房间内的 点击玩家头像 弹出的踢出房间弹窗\n            this.kickOutDialogController.show(userId, nickname)\n        })\n\n        this.matchParentController.setClick(() => {\n            //匹配页面的返回键的回调\n            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeCancelPair, {});\n\n        })\n\n    }\n    updateGold() {\n        this.hallParentController.updateGold()\n    }\n\n    //开始匹配 或者创建房间\n    startOrCreate(msgId: MessageId) {\n        //判断是否链接成功，并且还得有登录成功的数据返回 ，不成功的话就不允许执行下面的操作\n        if (WebSocketManager.GetInstance().webState != WebSocketToolState.Connected || GlobalBean.GetInstance().loginData == null) {\n            return;\n        }\n\n        //点击 快速开始游戏 start 的回调\n        let pairRequest: PairRequest = {\n            playerNum: GlobalBean.GetInstance().players,\n            fee: GlobalBean.GetInstance().ticketsNum,\n        }\n        //发送请求开始游戏的消息\n        WebSocketManager.GetInstance().sendMsg(msgId, pairRequest);\n    }\n\n    //设置是大厅 还是匹配页面\n    setHallOrMatch(hallOrMatch: HallOrMatch) {\n        if (this.hallOrMatch === hallOrMatch) {\n            return\n        }\n        this.hallOrMatch = hallOrMatch\n        this.hallParentNode.active = false\n        this.matchParentNode.active = false\n        switch (hallOrMatch) {\n            case HallOrMatch.HALL_PARENT:\n                this.hallParentNode.active = true\n                break;\n            case HallOrMatch.MATCH_PARENT:\n                this.matchParentNode.active = true\n                break;\n        }\n    }\n\n\n    LoginSuccess() {\n        //登录成功后 执行的操作\n        GameMgr.Console.Log(\"登录成功\")\n        let loginData = GlobalBean.GetInstance().loginData\n        if (loginData) {\n            if (loginData.roomId > 0) { //正在游戏中\n                WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeEnterRoom, {})//重连进来的 玩家请求进入房间\n            }\n            if (loginData.inviteCode > 0) {//正在私人房间\n                WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeAcceptInvite, { 'inviteCode': Number(loginData.inviteCode) })\n            } else {\n                //房间已经解散了  但是我还留在私人房间\n                this.hallParentController.exitTheRoom()\n            }\n            //重连的时候 被遗留在匹配页面的话 就回到大厅\n            if (loginData.roomId == 0 && loginData.inviteCode == 0 && this.hallOrMatch == HallOrMatch.MATCH_PARENT) {\n                this.setHallOrMatch(HallOrMatch.HALL_PARENT)\n            }\n        }\n        this.setFees()\n        // 登录成功后请求关卡进度\n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelProgress, {});\n    }\n\n    //设置接受邀请成功\n    setAcceptInvite(acceptInvite: AcceptInvite) {\n        this.hallParentController.setAcceptInvite(acceptInvite)\n    }\n    //离开房间\n    leaveRoom(noticeLeaveInvite: NoticeLeaveInvite) {\n        this.hallParentController.leaveRoom(noticeLeaveInvite)\n    }\n\n\n    //设置门票\n    setFees() {\n        this.hallParentController.setFees()\n    }\n\n    //初始化 match 页面\n    createMatchView() {\n        this.matchParentController.createMatchView()\n    }\n    //设置匹配数据\n    setGameData() {\n        this.matchParentController.setGameData()\n    }\n\n    //进入私人房间\n    joinCreateRoom(){\n        this.hallParentController.joinCreateRoom()\n    }\n    //房间号无效\n    joinError() {\n        this.hallParentController.joinError()\n    }\n    //准备 取消准备\n    setReadyState(noticeUserInviteStatus: NoticeUserInviteStatus) {\n        if (this.hallParentController) {\n            this.hallParentController.setReadyState(noticeUserInviteStatus)\n        }\n\n    }\n\n    /**\n     * 设置关卡进度（从后端获取数据后调用）\n     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels\n     */\n    setLevelProgress(levelProgressData: any) {\n        if (this.levelSelectPageController) {\n            this.levelSelectPageController.setLevelProgress(levelProgressData);\n        }\n    }\n\n    // update (dt) {}\n}\n"]}