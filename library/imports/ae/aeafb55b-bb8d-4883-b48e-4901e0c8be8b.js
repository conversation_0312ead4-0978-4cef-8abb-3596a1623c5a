"use strict";
cc._RF.push(module, 'aeafbVbu41Ig7SOSQHgyL6L', 'HttpUtils');
// scripts/net/HttpUtils.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpUtils = void 0;
var HttpUtils = /** @class */ (function () {
    function HttpUtils() {
    }
    HttpUtils.SyncGet = function (address, params) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            var xhr = new XMLHttpRequest();
            var url = address + _this.ParamsToString(params);
            xhr.open("GET", url, true); // 异步模式
            var interfaceName = _this.GetUrlInterfaceName(address);
            _this._isShowLog && console.log(interfaceName + " url: ", url);
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) { // 请求完成
                    if ((xhr.status >= 200 && xhr.status < 300) || xhr.status === 304) {
                        var data = _this.GetHttpData(xhr);
                        _this._isShowLog && console.log(interfaceName, data);
                        resolve(data);
                    }
                    else {
                        var errorData = _this.CreateGetErrorData(address, xhr.status);
                        _this._isShowLog && console.log(interfaceName + " 错误", errorData);
                        reject(errorData);
                    }
                }
            };
            xhr.send();
        });
    };
    HttpUtils.Get = function (address, params, cb) {
        var _this = this;
        var interfaceName = this.GetUrlInterfaceName(address);
        var xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function (ev) {
            if (xhr.readyState == 4) {
                var data = null;
                if ((xhr.status >= 200 && xhr.status < 300) || xhr.status == 304) {
                    data = _this.GetHttpData(xhr);
                }
                else {
                    data = _this.CreateGetErrorData(address, xhr.status);
                }
                _this._isShowLog && console.log(interfaceName, data);
                cb && cb(data);
            }
        };
        var url = address + this.ParamsToString(params);
        xhr.open("GET", url);
        xhr.send();
        this._isShowLog && console.log(interfaceName + " url: ", url);
    };
    HttpUtils.ParamsToString = function (params) {
        var arr = [];
        for (var key in params) {
            arr.push(key + "=" + params[key]);
        }
        if (arr.length > 0) {
            return "?" + arr.join("&");
        }
        return "";
    };
    HttpUtils.GetHttpData = function (xhr) {
        var outData = {};
        try {
            outData = JSON.parse(xhr.response);
        }
        catch (err) {
            outData = xhr.response;
        }
        return outData;
    };
    HttpUtils.GetUrlInterfaceName = function (url) {
        var adress = url.split("?")[0];
        var strArr = adress.split("/");
        return strArr[strArr.length - 1];
    };
    HttpUtils.CreateGetErrorData = function (address, status) {
        var interfaceName = this.GetUrlInterfaceName(address);
        var resData = {};
        resData.code = status;
        resData.docs = interfaceName + " status: " + status;
        return resData;
    };
    HttpUtils._isShowLog = true;
    return HttpUtils;
}());
exports.HttpUtils = HttpUtils;

cc._RF.pop();