"use strict";
cc._RF.push(module, 'f3b5eIOZ4tF8YT//S1WoI5Z', 'CongratsDialogController');
// scripts/game/CongratsDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var MessageBaseBean_1 = require("../net/MessageBaseBean");
var CongratsItemController_1 = require("../pfb/CongratsItemController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//结算页面
var CongratsDialogController = /** @class */ (function (_super) {
    __extends(CongratsDialogController, _super);
    function CongratsDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.contentLay = null;
        _this.backBtn = null;
        _this.congratsItem = null; //列表的 item
        _this.layoutNode = null; //存放列表的布局
        _this.countdownTimeLabel = null;
        _this.countdownInterval = null; //倒计时的 id
        _this.backCallback = null; //隐藏弹窗的回调
        _this.seconds = 10; //倒计时 10 秒
        return _this;
    }
    CongratsDialogController.prototype.onLoad = function () {
        this.countdownTimeLabel = this.backBtn.getChildByName('buttonLabel_time').getComponent(cc.Label);
    };
    CongratsDialogController.prototype.onEnable = function () {
        this.updateCountdownLabel(this.seconds);
        Tools_1.Tools.setCountDownTimeLabel(this.backBtn);
    };
    CongratsDialogController.prototype.start = function () {
        var _this = this;
        //backBtn 按钮点击事件
        Tools_1.Tools.greenButton(this.backBtn, function () {
            _this.hide(true);
        });
    };
    CongratsDialogController.prototype.show = function (noticeSettlement, backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        this._setData(noticeSettlement);
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    CongratsDialogController.prototype._setData = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            this.startCountdown(10); // 仍然启动倒计时
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) {
            // 对于扫雷游戏，finalRanking 中有 coinChg 字段，需要更新金币
            if ('coin' in userList[index]) {
                // UserSettlement 类型，直接使用 coin 字段
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin = userList[index].coin;
            }
            else if ('coinChg' in userList[index]) {
                // PlayerFinalResult 类型，使用 coinChg 字段更新金币
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin += userList[index].coinChg;
            }
        }
        this.layoutNode.removeAllChildren();
        var _loop_1 = function (i) {
            var item = cc.instantiate(this_1.congratsItem);
            var data = userList[i];
            this_1.layoutNode.addChild(item);
            setTimeout(function () {
                item.getComponent(CongratsItemController_1.default).createData(data, GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users);
            }, 100);
        };
        var this_1 = this;
        for (var i = 0; i < userList.length; ++i) {
            _loop_1(i);
        }
        this.startCountdown(10); //倒计时 10 秒
    };
    // bool 在隐藏的时候是否返回大厅
    CongratsDialogController.prototype.hide = function (bool) {
        var _this = this;
        if (bool === void 0) { bool = false; }
        if (this.backCallback) {
            this.backCallback();
        }
        GameMgr_1.GameMgr.Console.Log('隐藏结算页面');
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
            if (bool) {
                GlobalBean_1.GlobalBean.GetInstance().cleanData();
                var autoMessageBean = {
                    'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                    'data': { 'type': 2 } //2是结算弹窗跳转的
                };
                GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            }
        })
            .start();
    };
    CongratsDialogController.prototype.onDisable = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }
    };
    CongratsDialogController.prototype.startCountdown = function (seconds) {
        var _this = this;
        var remainingSeconds = seconds;
        this.updateCountdownLabel(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            if (remainingSeconds <= 0) {
                clearInterval(_this.countdownInterval);
                _this.countdownInterval = null;
                // 倒计时结束时的处理逻辑
                _this.hide(true);
                return;
            }
            _this.updateCountdownLabel(remainingSeconds);
        }, 1000);
    };
    CongratsDialogController.prototype.updateCountdownLabel = function (seconds) {
        if (this.countdownTimeLabel) {
            this.countdownTimeLabel.string = "\uFF08" + seconds + "s\uFF09";
        }
    };
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "backBtn", void 0);
    __decorate([
        property(cc.Prefab)
    ], CongratsDialogController.prototype, "congratsItem", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "layoutNode", void 0);
    CongratsDialogController = __decorate([
        ccclass
    ], CongratsDialogController);
    return CongratsDialogController;
}(cc.Component));
exports.default = CongratsDialogController;

cc._RF.pop();