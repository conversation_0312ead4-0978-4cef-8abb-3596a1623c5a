{"version": 3, "sources": ["assets/resources/i18n/en.ts"], "names": [], "mappings": ";;;;;;;AAAa,QAAA,QAAQ,GAAG;IACxB,SAAS;IACL,QAAQ,EAAG,uCAAuC;IAClD,SAAS,EAAG,uBAAuB;IACnC,mBAAmB,EAAG,4DAA4D;IAClF,iBAAiB,EAAG,sBAAsB;IAC1C,YAAY,EAAG,eAAe;IAC9B,UAAU,EAAG,cAAc;IAC3B,eAAe,EAAG,mBAAmB;IACrC,iBAAiB,EAAG,sBAAsB;IAC1C,gBAAgB,EAAG,qBAAqB;IACxC,uBAAuB,EAAG,6BAA6B;IACvD,eAAe,EAAG,wBAAwB;IAC1C,WAAW,EAAG,4DAA4D;IAE1E,gBAAgB,EAAG,oBAAoB;IACvC,sBAAsB,EAAG,6BAA6B;IACtD,QAAQ,EAAE,sCAAsC;IAEhD,MAAM,EAAC,MAAM;IACb,QAAQ,EAAC,OAAO;IAChB,SAAS,EAAC,OAAO;IACjB,SAAS,EAAC,OAAO;IACjB,UAAU,EAAC,QAAQ;IACnB,MAAM,EAAC,QAAQ;IACf,OAAO,EAAC,SAAS;IACjB,QAAQ,EAAC,UAAU;IACnB,IAAI,EAAC,MAAM;IACX,KAAK,EAAC,OAAO;IACb,KAAK,EAAC,OAAO;IACb,KAAK,EAAC,OAAO;IACb,IAAI,EAAC,MAAM;IACX,MAAM,EAAC,QAAQ;IACf,IAAI,EAAC,MAAM;IACX,IAAI,EAAC,MAAM;IACX,WAAW,EAAC,aAAa;IACzB,IAAI,EAAC,MAAM;IACX,WAAW,EAAC,aAAa;IACzB,cAAc,EAAC,iBAAiB;IAChC,UAAU,EAAC,oBAAoB;IAC/B,iBAAiB,EAAC,mBAAmB;IACrC,IAAI,EAAC,MAAM;IACX,OAAO,EAAC,SAAS;IACjB,MAAM,EAAC,QAAQ;IACf,OAAO,EAAC,SAAS;IACjB,KAAK,EAAC,OAAO;IAEb,SAAS,EAAC,MAAM;IAEhB,OAAO,EAAC,YAAY;IACpB,KAAK,EAAC,OAAO;CAGhB,CAAC;AAEF,IAAM,KAAK,GAAG,EAAS,CAAC;AACxB,IAAI,CAAC,KAAK,CAAC,QAAQ;IAAE,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AACzC,KAAK,CAAC,QAAQ,CAAC,EAAE,GAAG,gBAAQ,CAAC", "file": "", "sourceRoot": "/", "sourcesContent": ["export const language = {\n//这部分是通用的\n    kickout1 : 'You have been asked to leave the room',//您被请出房间\n    LeaveRoom : 'The room is dissolved',//房间已解散\n    InsufficientBalance : 'The current balance is insufficient, please go to purchase',//余额不足\n    GameRouteNotFound : 'Game route not found',//游戏线路异常\n    NetworkError : 'network error',//网络异常\n    RoomIsFull : 'Room is full',//房间已满\n    EnterRoomNumber : 'Enter room number',//输入房间号\n    GetUserInfoFailed : 'get user info failed',//获取用户信息失败\n    RoomDoesNotExist : 'Room does not exist',//房间不存在\n    FailedToDeductGoldCoins : 'Failed to deduct gold coins',//扣除金币失败\n    ExitApplication : 'Are you sure to leave?',//完全退出游戏\n    QuitTheGame : 'Once you exit the game, you won’t be able to return to it.',//退出本局游戏\n\n    NotEnoughPlayers : 'Not enough players',//玩家数量不足\n    TheGameIsFullOfPlayers : 'The game is full of players',//玩家数量已满\n    kickout2: 'Whether to kick {0} out of the room?',//踢出玩家文案\n\n    upSeat:'Join', //上座\n    downSeat:'Leave', //下座\n    startGame:'Start', //开始游戏\n    readyGame:'Ready', //准备\n    cancelGame:'Cancel', //取消准备\n    cancel:'Cancel', \n    confirm:'Confirm', \n    kickout3:'Kick Out', \n    back:'Back',//返回\n    leave:'Leave', //退出\n    music:'Music',  //音乐\n    sound:'Sound', //音效\n    join:'Join', //加入\n    create:'Create', //创建\n    auto:'Auto',\n    Room:'Room',\n    room_number:'Room Number', //房间号\n    copy:'Copy', //复制\n    game_amount:'Game Amount', //游戏费用\n    player_numbers:'Player Numbers:', //玩家数量\n    room_exist:'Room doesn’t exist',//房间不存在\n    enter_room_number:'Enter room number',//输入房间号\n    free:'Free',\n    players:'Players', //玩家\n    Player:'Player',\n    Tickets:'Tickets',\n    Empty:'Empty',\n\n    nextlevel:'Next',//下一关\n\n    relevel:'Play Again', //再来一局\n    rules:'Rules',\n\n    \n};\n\nconst cocos = cc as any;\nif (!cocos.Jou_i18n) cocos.Jou_i18n = {};\ncocos.Jou_i18n.en = language;"]}