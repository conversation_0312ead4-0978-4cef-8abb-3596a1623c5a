"use strict";
cc._RF.push(module, 'cc55fhCTRdMjZxuQuVowyEX', 'en');
// resources/i18n/en.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: 'You have been asked to leave the room',
    LeaveRoom: 'The room is dissolved',
    InsufficientBalance: 'The current balance is insufficient, please go to purchase',
    GameRouteNotFound: 'Game route not found',
    NetworkError: 'network error',
    RoomIsFull: 'Room is full',
    EnterRoomNumber: 'Enter room number',
    GetUserInfoFailed: 'get user info failed',
    RoomDoesNotExist: 'Room does not exist',
    FailedToDeductGoldCoins: 'Failed to deduct gold coins',
    ExitApplication: 'Are you sure to leave?',
    QuitTheGame: 'Once you exit the game, you won’t be able to return to it.',
    NotEnoughPlayers: 'Not enough players',
    TheGameIsFullOfPlayers: 'The game is full of players',
    kickout2: 'Whether to kick {0} out of the room?',
    upSeat: 'Join',
    downSeat: 'Leave',
    startGame: 'Start',
    readyGame: 'Ready',
    cancelGame: 'Cancel',
    cancel: 'Cancel',
    confirm: 'Confirm',
    kickout3: 'Kick Out',
    back: 'Back',
    leave: 'Leave',
    music: 'Music',
    sound: 'Sound',
    join: 'Join',
    create: 'Create',
    auto: 'Auto',
    Room: 'Room',
    room_number: 'Room Number',
    copy: 'Copy',
    game_amount: 'Game Amount',
    player_numbers: 'Player Numbers:',
    room_exist: 'Room doesn’t exist',
    enter_room_number: 'Enter room number',
    free: 'Free',
    players: 'Players',
    Player: 'Player',
    Tickets: 'Tickets',
    Empty: 'Empty',
    nextlevel: 'Next',
    relevel: 'Play Again',
    rules: 'Rules',
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.en = exports.language;

cc._RF.pop();